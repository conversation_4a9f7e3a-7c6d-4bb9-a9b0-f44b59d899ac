<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <!-- 模型封面图片 -->
        <ImageView
            android:id="@+id/iv_thumbnail"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="fitCenter"
            android:adjustViewBounds="true"
            android:background="@color/design_default_color_surface"
            app:layout_constraintDimensionRatio="H,3:4"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 选中状态覆盖层 -->
        <View
            android:id="@+id/view_selected_overlay"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#80000000"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/iv_thumbnail"
            app:layout_constraintEnd_toEndOf="@id/iv_thumbnail"
            app:layout_constraintStart_toStartOf="@id/iv_thumbnail"
            app:layout_constraintTop_toTopOf="@id/iv_thumbnail" />

        <!-- 下载状态指示器 -->
        <ImageView
            android:id="@+id/iv_download_status"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_margin="4dp"
            android:background="@drawable/bg_download_status"
            android:padding="2dp"
            android:src="@drawable/ic_download"
            android:tint="@color/white"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@id/iv_thumbnail"
            app:layout_constraintTop_toTopOf="@id/iv_thumbnail" />

        <!-- 选中图标 -->
        <ImageView
            android:id="@+id/iv_selected_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_check"
            android:tint="@color/white"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/iv_thumbnail"
            app:layout_constraintEnd_toEndOf="@id/iv_thumbnail"
            app:layout_constraintStart_toStartOf="@id/iv_thumbnail"
            app:layout_constraintTop_toTopOf="@id/iv_thumbnail" />

        <!-- 模型名称 -->
        <TextView
            android:id="@+id/tv_model_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="模型名称"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_thumbnail" />

        <!-- 模型分类标签 -->
        <TextView
            android:id="@+id/tv_category"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:background="@drawable/bg_category_tag"
            android:paddingHorizontal="8dp"
            android:paddingVertical="2dp"
            android:text="横屏-男"
            android:textColor="@color/white"
            android:textSize="10sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_model_name" />

        <!-- 文件大小 -->
        <TextView
            android:id="@+id/tv_file_size"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="55.5 MB"
            android:textColor="@color/design_default_color_on_surface"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_model_name" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
