package ai.guiji.duix.test.ui.activity

import ai.guiji.duix.test.R
import ai.guiji.duix.test.databinding.ActivityLauncherBinding
import ai.guiji.duix.test.ui.activity.BaseActivity
import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import java.io.BufferedReader
import java.io.File
import java.io.FileInputStream
import java.io.InputStreamReader

/**
 * 应用启动Activity
 * 检查必要的配置和权限后直接跳转到数字人界面
 */
class LauncherActivity : BaseActivity() {

    companion object {
        const val PERMISSION_REQUEST_CODE = 1001
    }

    private lateinit var binding: ActivityLauncherBinding
    private val sharedPrefs by lazy { getSharedPreferences(SettingsActivity.PREF_NAME, MODE_PRIVATE) }

    private val baseConfigUrl = "https://cdn.guiji.ai/duix/location/gj_dh_res.zip"
    private lateinit var baseDir: File
    private lateinit var modelDir: File
    private val baseConfigUUID = "d39caddd-488b-4682-b6d1-13549b135dd1"
    private val modelUUID = "d39caddd-488b-4682-b6d1-13549b135dd1"

    // 核心权限：数字人功能必需的权限
    private val corePermissions = arrayOf(
        Manifest.permission.RECORD_AUDIO  // 语音识别必需
    )
    
    // 可选权限：功能增强权限，可在需要时动态请求
    private val optionalPermissions = arrayOf(
        Manifest.permission.CAMERA,
        Manifest.permission.WRITE_EXTERNAL_STORAGE,
        Manifest.permission.READ_EXTERNAL_STORAGE
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 强制输出日志，确保能看到LauncherActivity被调用
        android.util.Log.e("DUIX_NET", "!!! LauncherActivity onCreate 被调用 !!!")
        android.util.Log.e("LauncherActivity", "!!! LauncherActivity onCreate 被调用 !!!")
        
        binding = ActivityLauncherBinding.inflate(layoutInflater)
        setContentView(binding.root)

        android.util.Log.e("DUIX_NET", "LauncherActivity onCreate 开始初始化目录")
        Log.i("LauncherActivity", "=== LauncherActivity onCreate 开始 ===")
        Log.i("LauncherActivity", "开始初始化目录")
        initDirectories()
        android.util.Log.e("DUIX_NET", "LauncherActivity 目录初始化完成")

        // 添加一个延迟，让我们先检查当前权限状态
        Log.i("LauncherActivity", "开始检查当前权限状态...")
        checkCurrentPermissionStatus()
        android.util.Log.e("DUIX_NET", "LauncherActivity 权限检查完成")

        Log.i("LauncherActivity", "开始检查并启动应用")
        android.util.Log.e("DUIX_NET", "LauncherActivity 即将调用checkAndLaunchApp")
        checkAndLaunchApp()
        android.util.Log.e("DUIX_NET", "LauncherActivity checkAndLaunchApp调用完成")
    }

    private fun initDirectories() {
        val duixDir = File(getExternalFilesDir("duix"), "")
        if (!duixDir.exists()) {
            duixDir.mkdirs()
        }

        baseDir = File(duixDir, baseConfigUrl.substring(baseConfigUrl.lastIndexOf("/") + 1)
            .replace(".zip", ""))

        // 动态发现实际的模型目录
        modelDir = findValidModelDirectory(duixDir)

        Log.d("LauncherActivity", "初始化目录:")
        Log.d("LauncherActivity", "duixDir: ${duixDir.absolutePath}")
        Log.d("LauncherActivity", "baseDir: ${baseDir.absolutePath}")
        Log.d("LauncherActivity", "modelDir: ${modelDir.absolutePath}")
    }

    /**
     * 动态发现有效的模型目录
     * 优先使用用户在模型选择界面选择的模型，然后扫描duix目录下的所有子目录
     */
    private fun findValidModelDirectory(duixDir: File): File {
        Log.d("LauncherActivity", "开始扫描模型目录...")

        // 首先检查用户是否在模型选择界面选择了模型
        val selectedModelUrl = sharedPrefs.getString(SettingsActivity.KEY_SELECTED_MODEL_URL, "")
        if (!selectedModelUrl.isNullOrEmpty()) {
            val modelFileName = selectedModelUrl.substring(selectedModelUrl.lastIndexOf("/") + 1).replace(".zip", "")
            val selectedModelDir = File(duixDir, modelFileName)
            if (isValidModelDirectory(selectedModelDir)) {
                Log.d("LauncherActivity", "使用用户选择的模型目录: ${selectedModelDir.name}")
                // 保存为上次使用的模型目录
                sharedPrefs.edit().putString("last_used_model_dir", selectedModelDir.name).apply()
                return selectedModelDir
            } else {
                Log.w("LauncherActivity", "用户选择的模型目录不存在或无效: ${selectedModelDir.name}")
            }
        }

        // 然后尝试从SharedPreferences获取上次使用的模型目录
        val lastUsedModelDir = sharedPrefs.getString("last_used_model_dir", "")
        if (!lastUsedModelDir.isNullOrEmpty()) {
            val lastUsedDir = File(duixDir, lastUsedModelDir)
            if (isValidModelDirectory(lastUsedDir)) {
                Log.d("LauncherActivity", "使用上次使用的模型目录: ${lastUsedDir.name}")
                return lastUsedDir
            }
        }

        // 扫描duix目录下的所有子目录
        val subdirs = duixDir.listFiles { file ->
            file.isDirectory && file.name != "base" && !file.name.startsWith("gj_dh_res")
        } ?: emptyArray()

        Log.d("LauncherActivity", "找到 ${subdirs.size} 个候选目录: ${subdirs.map { it.name }}")

        // 查找包含有效模型文件的目录
        for (dir in subdirs) {
            if (isValidModelDirectory(dir)) {
                Log.d("LauncherActivity", "找到有效模型目录: ${dir.name}")
                // 保存为上次使用的模型目录
                sharedPrefs.edit().putString("last_used_model_dir", dir.name).apply()
                return dir
            }
        }

        // 如果没有找到有效的模型目录，返回默认目录（可能不存在）
        val defaultModelDir = File(duixDir, "ningxun_20240509v3")
        Log.w("LauncherActivity", "未找到有效模型目录，使用默认目录: ${defaultModelDir.name}")
        return defaultModelDir
    }

    /**
     * 检查目录是否包含有效的模型文件
     */
    private fun isValidModelDirectory(dir: File): Boolean {
        if (!dir.exists() || !dir.isDirectory) {
            return false
        }

        // 检查必需的模型文件
        val requiredFiles = listOf("uuid", "bbox.j", "config.j", "dh_model.b", "dh_model.p")
        val missingFiles = requiredFiles.filter { !File(dir, it).exists() }

        if (missingFiles.isNotEmpty()) {
            Log.d("LauncherActivity", "目录 ${dir.name} 缺少文件: $missingFiles")
            return false
        }

        // 检查uuid文件内容是否有效
        val uuidFile = File(dir, "uuid")
        try {
            val uuid = uuidFile.readText().trim()
            if (uuid.isNotEmpty() && uuid.length >= 30) { // 简单的UUID格式检查
                Log.d("LauncherActivity", "目录 ${dir.name} 验证通过，UUID: $uuid")
                return true
            }
        } catch (e: Exception) {
            Log.e("LauncherActivity", "读取UUID文件失败: ${e.message}")
        }

        return false
    }

    private fun checkCurrentPermissionStatus() {
        Log.d("LauncherActivity", "=== 当前权限状态检查 ===")
        Log.d("LauncherActivity", "核心权限状态:")
        corePermissions.forEach { permission ->
            val isGranted = ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED
            Log.d("LauncherActivity", "  $permission: ${if (isGranted) "✅ 已授予" else "❌ 未授予"}")
        }
        Log.d("LauncherActivity", "可选权限状态:")
        optionalPermissions.forEach { permission ->
            val isGranted = ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED
            Log.d("LauncherActivity", "  $permission: ${if (isGranted) "✅ 已授予" else "❌ 未授予"}")
        }
        Log.d("LauncherActivity", "=== 权限状态检查完成 ===")
    }

    private fun checkAndLaunchApp() {
        android.util.Log.e("DUIX_NET", "!!! checkAndLaunchApp 开始执行 !!!")
        Log.d("LauncherActivity", "checkAndLaunchApp 开始")

        // 检查权限
        android.util.Log.e("DUIX_NET", "开始检查权限")
        if (!checkPermissions()) {
            android.util.Log.e("DUIX_NET", "权限检查失败，请求权限")
            requestPermissions()
            return
        }
        android.util.Log.e("DUIX_NET", "权限检查通过")

        // 检查配置完整性
        android.util.Log.e("DUIX_NET", "开始检查配置完整性")
        val configComplete = isConfigurationComplete()
        android.util.Log.e("DUIX_NET", "配置完整性检查结果: $configComplete")
        Log.d("LauncherActivity", "配置完整性检查结果: $configComplete")

        // 检查基础配置文件和模型文件
        val baseConfigReady = try {
            baseDir.exists() &&
            File(baseDir, "/uuid").exists() &&
            baseConfigUUID == BufferedReader(InputStreamReader(FileInputStream(File(baseDir, "/uuid")))).readLine()
        } catch (e: Exception) {
            Log.e("LauncherActivity", "检查基础配置文件失败", e)
            false
        }

        val modelReady = try {
            modelDir.exists() &&
            File(modelDir, "uuid").exists() &&
            File(modelDir, "bbox.j").exists() &&
            File(modelDir, "config.j").exists() &&
            File(modelDir, "dh_model.b").exists() &&
            File(modelDir, "dh_model.p").exists()
        } catch (e: Exception) {
            Log.e("LauncherActivity", "检查模型文件失败", e)
            false
        }

        Log.e("LauncherActivity", "=== 决策逐个检查 ===")
        Log.e("LauncherActivity", "configComplete: $configComplete")
        Log.e("LauncherActivity", "modelReady: $modelReady")
        Log.e("LauncherActivity", "baseConfigReady: $baseConfigReady")
        
        when {
            // 如果配置完整且文件都准备好，直接启动数字人
            configComplete && modelReady && baseConfigReady -> {
                Log.e("LauncherActivity", "!!! 条件1: 配置完整且文件都已准备好，直接启动数字人 !!!")
                startCallActivity()
            }
            // 如果配置完整但没有模型文件，跳转到模型选择界面
            configComplete && !modelReady -> {
                Log.e("LauncherActivity", "!!! 条件2: 配置完整但模型文件不存在，跳转到模型选择界面 !!!")
                startModelSelectionActivity()
            }
            // 如果配置不完整，跳转到设置界面
            !configComplete -> {
                Log.e("LauncherActivity", "!!! 条件3: 配置不完整，跳转到设置界面 !!!")
                startSettingsActivity()
            }
            // 如果基础配置文件不存在，跳转到设置界面
            !baseConfigReady -> {
                Log.e("LauncherActivity", "!!! 条件4: 基础配置文件不存在，跳转到设置界面 !!!")
                startSettingsActivity()
            }
            else -> {
                Log.e("LauncherActivity", "!!! 条件5: 未知状态，跳转到设置界面 !!!")
                startSettingsActivity()
            }
        }
    }

    private fun checkPermissions(): Boolean {
        Log.d("LauncherActivity", "开始检查核心权限，Android版本: ${Build.VERSION.SDK_INT}")

        // 只检查核心权限，确保数字人基本功能可用
        val corePermissionResults = corePermissions.map { permission ->
            val granted = ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED
            Log.d("LauncherActivity", "核心权限 $permission: ${if (granted) "已授予" else "未授予"}")
            permission to granted
        }

        val allCoreGranted = corePermissionResults.all { (_, granted) -> granted }
        Log.d("LauncherActivity", "核心权限检查结果: ${if (allCoreGranted) "全部通过" else "存在未授予的核心权限"}")

        return allCoreGranted
    }

    private fun requestPermissions() {
        Log.d("LauncherActivity", "准备请求核心权限")

        // 只请求未授予的核心权限
        val corePermissionsToRequest = corePermissions.filter { permission ->
            val needRequest = ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED
            Log.d("LauncherActivity", "核心权限 $permission 需要请求: $needRequest")
            needRequest
        }.toTypedArray()

        Log.d("LauncherActivity", "需要请求的核心权限数量: ${corePermissionsToRequest.size}")
        if (corePermissionsToRequest.isNotEmpty()) {
            Log.d("LauncherActivity", "开始请求核心权限: ${corePermissionsToRequest.joinToString()}")
            ActivityCompat.requestPermissions(this, corePermissionsToRequest, PERMISSION_REQUEST_CODE)
        } else {
            Log.d("LauncherActivity", "无需请求权限，直接继续")
            checkAndLaunchApp()
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        Log.d("LauncherActivity", "权限请求结果回调，requestCode: $requestCode")

        if (requestCode == PERMISSION_REQUEST_CODE) {
            Log.d("LauncherActivity", "处理核心权限请求结果")

            // 详细记录每个权限的授予结果
            permissions.forEachIndexed { index, permission ->
                val granted = grantResults.getOrNull(index) == PackageManager.PERMISSION_GRANTED
                Log.d("LauncherActivity", "核心权限结果 $permission: ${if (granted) "已授予" else "被拒绝"}")
            }

            val allCoreGranted = grantResults.all { it == PackageManager.PERMISSION_GRANTED }
            Log.d("LauncherActivity", "所有核心权限授予结果: $allCoreGranted")

            if (allCoreGranted) {
                Log.d("LauncherActivity", "核心权限授予成功，继续启动流程")
                checkAndLaunchApp()
            } else {
                Log.w("LauncherActivity", "核心权限授予失败，无法继续")
                Toast.makeText(this, "需要授予语音权限以继续操作", Toast.LENGTH_LONG).show()
                startSettingsActivity()
            }
        }
    }

    private fun startCallActivity() {
        Log.d("LauncherActivity", "startCallActivity 开始")
        val ttsUrl = sharedPrefs.getString(SettingsActivity.KEY_TTS_URL, SettingsActivity.DEFAULT_TTS_URL)
        val apiKey = sharedPrefs.getString(SettingsActivity.KEY_APIKEY, "")

        // 获取用户选择的模型信息
        val selectedModelUrl = sharedPrefs.getString(SettingsActivity.KEY_SELECTED_MODEL_URL, "")
        val selectedModelCategory = sharedPrefs.getString(SettingsActivity.KEY_SELECTED_MODEL_CATEGORY, "")

        Log.d("LauncherActivity", "baseDir: ${baseDir.absolutePath}")
        Log.d("LauncherActivity", "modelDir: ${modelDir.absolutePath}")
        Log.d("LauncherActivity", "ttsUrl: $ttsUrl")
        Log.d("LauncherActivity", "selectedModelUrl: $selectedModelUrl")
        Log.d("LauncherActivity", "selectedModelCategory: $selectedModelCategory")

        val intent = Intent(this, CallActivity::class.java).apply {
            putExtra("baseDir", baseDir.absolutePath)
            putExtra("modelDir", modelDir.absolutePath)
            putExtra("ttsUrl", ttsUrl)
            putExtra("apiKey", apiKey)
            putExtra("selectedModelUrl", selectedModelUrl)
            putExtra("selectedModelCategory", selectedModelCategory)
            putExtra("forceReload", true) // 强制重新加载以清理重影
        }
        Log.d("LauncherActivity", "准备启动CallActivity")
        startActivity(intent)
        Log.d("LauncherActivity", "CallActivity已启动，结束LauncherActivity")
        finish() // 结束启动Activity
    }

    private fun startSettingsActivity() {
        Log.e("LauncherActivity", "!!! 即将跳转到SettingsActivity !!!")
        val intent = Intent(this, SettingsActivity::class.java)
        startActivity(intent)
        finish() // 结束启动Activity
    }

    private fun startModelSelectionActivity() {
        Log.d("LauncherActivity", "启动模型选择界面")
        val intent = Intent(this, ModelSelectionActivity::class.java)
        startActivity(intent)
        finish() // 结束启动Activity
    }

    /**
     * 检查配置是否完整
     * 暂时只检查核心配置，简化检查逻辑
     */
    private fun isConfigurationComplete(): Boolean {
        Log.d("LauncherActivity", "=== 开始检查配置完整性 ===")
        
        // 核心必需配置
        val ttsUrl = sharedPrefs.getString(SettingsActivity.KEY_TTS_URL, "")?.trim()
        val apiKey = sharedPrefs.getString(SettingsActivity.KEY_APIKEY, "")?.trim()
        val userId = sharedPrefs.getString(SettingsActivity.KEY_USER_ID, "")?.trim()
        
        Log.d("LauncherActivity", "TTS_URL: '$ttsUrl' (非空: ${!ttsUrl.isNullOrEmpty()})")
        Log.d("LauncherActivity", "APIKEY: '$apiKey' (非空: ${!apiKey.isNullOrEmpty()})")
        Log.d("LauncherActivity", "USER_ID: '$userId' (非空: ${!userId.isNullOrEmpty()})")
        
        // 暂时只检查核心配置
        val isComplete = !ttsUrl.isNullOrEmpty() && !apiKey.isNullOrEmpty() && !userId.isNullOrEmpty()
        
        Log.d("LauncherActivity", "配置完整性结果: $isComplete")
        Log.d("LauncherActivity", "=== 配置检查完成 ===")
        
        return isComplete
    }
}
