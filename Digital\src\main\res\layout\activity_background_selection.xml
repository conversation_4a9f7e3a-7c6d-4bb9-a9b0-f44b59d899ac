<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <!-- 标题 -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="选择背景"
        android:textColor="@color/black"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 刷新按钮 -->
    <ImageButton
        android:id="@+id/btn_refresh"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="16dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:contentDescription="刷新背景列表"
        android:padding="8dp"
        android:src="@android:drawable/ic_popup_sync"
        android:tint="@color/black"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintStart_toEndOf="@id/tv_title"
        app:layout_constraintTop_toTopOf="@id/tv_title" />

    <!-- 筛选标签 -->
    <HorizontalScrollView
        android:id="@+id/hsv_filters"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:scrollbars="none"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title">

        <com.google.android.material.chip.ChipGroup
            android:id="@+id/chip_group_filters"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:selectionRequired="true"
            app:singleSelection="true">

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_horizontal"
                style="@style/Widget.MaterialComponents.Chip.Filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                android:text="横屏" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_vertical"
                style="@style/Widget.MaterialComponents.Chip.Filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="竖屏" />

        </com.google.android.material.chip.ChipGroup>

    </HorizontalScrollView>

    <!-- 背景列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_backgrounds"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintBottom_toTopOf="@id/btn_apply_background"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/hsv_filters" />

    <!-- 应用背景按钮 -->
    <Button
        android:id="@+id/btn_apply_background"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="应用背景"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
