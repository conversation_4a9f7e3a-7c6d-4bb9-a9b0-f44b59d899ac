aat ; "{/*1.5 A x A^T}" ; "matrix size" ; 4:5000
ata ; "{/*1.5 A^T x A}" ; "matrix size" ; 4:5000
atv ; "{/*1.5 matrix^T x vector}" ; "matrix size" ; 4:5000
axpby ; "{/*1.5 Y = alpha X + beta Y}" ; "vector size" ; 5:1000000
axpy ; "{/*1.5 Y += alpha X}" ; "vector size" ; 5:1000000
matrix_matrix ; "{/*1.5 matrix matrix product}" ; "matrix size" ; 4:5000
matrix_vector ; "{/*1.5 matrix vector product}" ; "matrix size" ; 4:5000
trmm ; "{/*1.5 triangular matrix matrix product}" ; "matrix size" ; 4:5000
trisolve_vector ; "{/*1.5 triangular solver - vector (X = inv(L) X)}" ; "size" ; 4:5000
trisolve_matrix ; "{/*1.5 triangular solver - matrix (M = inv(L) M)}" ; "size" ; 4:5000
cholesky ; "{/*1.5 Cholesky decomposition}" ; "matrix size" ; 4:5000
complete_lu_decomp ; "{/*1.5 Complete LU decomposition}" ; "matrix size" ; 4:5000
partial_lu_decomp ; "{/*1.5 Partial LU decomposition}" ; "matrix size" ; 4:5000
tridiagonalization ; "{/*1.5 Tridiagonalization}" ; "matrix size" ; 4:5000
hessenberg ; "{/*1.5 Hessenberg decomposition}" ; "matrix size" ; 4:5000
symv ; "{/*1.5 symmetric matrix vector product}" ; "matrix size" ; 4:5000
syr2 ; "{/*1.5 symmretric rank-2 update (A += u^T v + u v^T)}" ; "matrix size" ; 4:5000
ger ; "{/*1.5 general rank-1 update (A += u v^T)}" ; "matrix size" ; 4:5000
rot ; "{/*1.5 apply rotation in the plane}" ; "vector size" ; 4:1000000
