package ai.guiji.duix.test.util

import android.os.Handler
import android.os.Looper
import android.util.Log
import android.widget.TextView
import androidx.core.widget.NestedScrollView
import io.noties.markwon.Markwon
import java.util.concurrent.atomic.AtomicBoolean

/**
 * UI渲染优化器
 * 主要功能：
 * 1. 批量UI更新，减少频繁刷新
 * 2. 智能滚动控制
 * 3. Markdown渲染优化
 */
class UIOptimizer {
    
    companion object {
        private const val TAG = "UIOptimizer"
        private const val UPDATE_INTERVAL = 100L // 100ms批量更新间隔
        private const val SCROLL_DEBOUNCE_TIME = 200L // 滚动防抖时间
    }
    
    private val mainHandler = Handler(Looper.getMainLooper())
    private val pendingUpdates = mutableListOf<() -> Unit>()
    private val isUpdateScheduled = AtomicBoolean(false)
    private var lastScrollTime = 0L
    
    /**
     * 批量UI更新 - 减少频繁的UI刷新
     */
    fun batchUIUpdate(update: () -> Unit) {
        synchronized(pendingUpdates) {
            pendingUpdates.add(update)
        }
        
        if (!isUpdateScheduled.getAndSet(true)) {
            mainHandler.postDelayed({
                executeBatchUpdates()
            }, UPDATE_INTERVAL)
        }
    }
    
    /**
     * 执行批量更新
     */
    private fun executeBatchUpdates() {
        val updates = synchronized(pendingUpdates) {
            val copy = pendingUpdates.toList()
            pendingUpdates.clear()
            copy
        }
        
        if (updates.isNotEmpty()) {
            Log.d(TAG, "执行${updates.size}个批量UI更新")
            updates.forEach { it() }
        }
        
        isUpdateScheduled.set(false)
    }
    
    /**
     * 优化的Markdown渲染 - 减少重复渲染
     */
    fun optimizedMarkdownRender(
        markwon: Markwon,
        textView: TextView,
        content: String,
        lastContent: String?
    ) {
        // 避免重复渲染相同内容
        if (content == lastContent) {
            Log.d(TAG, "内容未变化，跳过Markdown渲染")
            return
        }
        
        // 对于长文本，使用增量渲染
        if (content.length > 1000 && lastContent != null && content.startsWith(lastContent)) {
            Log.d(TAG, "使用增量Markdown渲染")
            // 只渲染新增部分（这里需要更复杂的实现）
            markwon.setMarkdown(textView, content)
        } else {
            Log.d(TAG, "使用完整Markdown渲染")
            markwon.setMarkdown(textView, content)
        }
    }
    
    /**
     * 智能滚动控制 - 防抖和性能优化
     */
    fun smartScroll(scrollView: NestedScrollView, targetY: Int, smooth: Boolean = true) {
        val currentTime = System.currentTimeMillis()
        
        // 防抖：如果短时间内多次滚动请求，只执行最后一次
        if (currentTime - lastScrollTime < SCROLL_DEBOUNCE_TIME) {
            Log.d(TAG, "滚动防抖，跳过本次滚动")
            return
        }
        
        lastScrollTime = currentTime
        
        mainHandler.post {
            try {
                if (smooth) {
                    scrollView.smoothScrollTo(0, targetY)
                } else {
                    scrollView.scrollTo(0, targetY)
                }
                Log.d(TAG, "执行智能滚动到位置: $targetY")
            } catch (e: Exception) {
                Log.e(TAG, "智能滚动失败: ${e.message}", e)
            }
        }
    }
    
    /**
     * 延迟执行UI操作 - 避免阻塞主线程
     */
    fun delayedUIOperation(delay: Long = 50L, operation: () -> Unit) {
        mainHandler.postDelayed(operation, delay)
    }
    
    /**
     * 检查是否在主线程
     */
    fun ensureMainThread(operation: () -> Unit) {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            operation()
        } else {
            mainHandler.post(operation)
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        mainHandler.removeCallbacksAndMessages(null)
        synchronized(pendingUpdates) {
            pendingUpdates.clear()
        }
        isUpdateScheduled.set(false)
        Log.i(TAG, "UI优化器资源已清理")
    }
}