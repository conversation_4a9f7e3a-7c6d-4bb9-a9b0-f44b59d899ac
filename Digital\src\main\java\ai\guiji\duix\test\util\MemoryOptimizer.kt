package ai.guiji.duix.test.util

import android.util.Log
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap

/**
 * 内存优化器
 * 主要功能：
 * 1. 智能内存管理
 * 2. 对象池复用
 * 3. 内存泄漏预防
 */
class MemoryOptimizer {
    
    companion object {
        private const val TAG = "MemoryOptimizer"
        private const val MAX_STRING_BUILDER_SIZE = 10 * 1024 * 1024 // 10MB
        private const val MAX_CACHE_ENTRIES = 100
    }
    
    // StringBuilder对象池
    private val stringBuilderPool = mutableListOf<StringBuilder>()
    private val maxPoolSize = 5
    
    // 弱引用缓存，防止内存泄漏
    private val weakCache = ConcurrentHashMap<String, WeakReference<Any>>()
    
    /**
     * 获取优化的StringBuilder
     */
    fun getOptimizedStringBuilder(): StringBuilder {
        synchronized(stringBuilderPool) {
            if (stringBuilderPool.isNotEmpty()) {
                val sb = stringBuilderPool.removeAt(stringBuilderPool.size - 1)
                sb.clear()
                Log.d(TAG, "复用StringBuilder，池大小: ${stringBuilderPool.size}")
                return sb
            }
        }
        
        Log.d(TAG, "创建新StringBuilder")
        return StringBuilder()
    }
    
    /**
     * 回收StringBuilder到对象池
     */
    fun recycleStringBuilder(sb: StringBuilder) {
        // 检查大小，避免内存占用过大
        if (sb.capacity() > MAX_STRING_BUILDER_SIZE) {
            Log.d(TAG, "StringBuilder过大，不回收: ${sb.capacity()}")
            return
        }
        
        synchronized(stringBuilderPool) {
            if (stringBuilderPool.size < maxPoolSize) {
                sb.clear()
                stringBuilderPool.add(sb)
                Log.d(TAG, "回收StringBuilder到池，池大小: ${stringBuilderPool.size}")
            }
        }
    }
    
    /**
     * 智能缓存管理 - 使用弱引用防止内存泄漏
     */
    fun <T> smartCache(key: String, value: T): T {
        // 清理已被GC的条目
        cleanupWeakCache()
        
        weakCache[key] = WeakReference(value as Any)
        Log.d(TAG, "添加到智能缓存: $key, 缓存大小: ${weakCache.size}")
        return value
    }
    
    /**
     * 从智能缓存获取
     */
    @Suppress("UNCHECKED_CAST")
    fun <T> getFromSmartCache(key: String): T? {
        val weakRef = weakCache[key]
        val value = weakRef?.get() as? T
        
        if (value == null && weakRef != null) {
            // 对象已被GC，清理引用
            weakCache.remove(key)
            Log.d(TAG, "清理已GC的缓存条目: $key")
        }
        
        return value
    }
    
    /**
     * 清理弱引用缓存
     */
    private fun cleanupWeakCache() {
        val iterator = weakCache.entries.iterator()
        var cleanedCount = 0
        
        while (iterator.hasNext()) {
            val entry = iterator.next()
            if (entry.value.get() == null) {
                iterator.remove()
                cleanedCount++
            }
        }
        
        if (cleanedCount > 0) {
            Log.d(TAG, "清理了${cleanedCount}个已GC的缓存条目")
        }
        
        // 如果缓存过大，强制清理一些条目
        if (weakCache.size > MAX_CACHE_ENTRIES) {
            val toRemove = weakCache.size - MAX_CACHE_ENTRIES
            val keys = weakCache.keys.take(toRemove)
            keys.forEach { weakCache.remove(it) }
            Log.d(TAG, "强制清理${toRemove}个缓存条目")
        }
    }
    
    /**
     * 内存使用情况报告
     */
    fun getMemoryReport(): String {
        val runtime = Runtime.getRuntime()
        val totalMemory = runtime.totalMemory()
        val freeMemory = runtime.freeMemory()
        val usedMemory = totalMemory - freeMemory
        val maxMemory = runtime.maxMemory()
        
        return buildString {
            appendLine("=== 内存使用报告 ===")
            appendLine("已使用: ${usedMemory / 1024 / 1024}MB")
            appendLine("总分配: ${totalMemory / 1024 / 1024}MB")
            appendLine("最大可用: ${maxMemory / 1024 / 1024}MB")
            appendLine("StringBuilder池: ${stringBuilderPool.size}")
            appendLine("智能缓存: ${weakCache.size}")
            appendLine("==================")
        }
    }
    
    /**
     * 强制垃圾回收（谨慎使用）
     */
    fun forceGC() {
        Log.i(TAG, "执行强制垃圾回收")
        System.gc()
        
        // 延迟清理缓存
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            cleanupWeakCache()
        }, 1000)
    }
    
    /**
     * 清理所有资源
     */
    fun cleanup() {
        synchronized(stringBuilderPool) {
            stringBuilderPool.clear()
        }
        weakCache.clear()
        Log.i(TAG, "内存优化器资源已清理")
    }
}