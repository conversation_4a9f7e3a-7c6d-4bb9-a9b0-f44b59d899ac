<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.1.2" type="baseline" client="gradle" dependencies="false" name="AGP (8.1.2)" variant="all" version="8.1.2">

    <issue
        id="CanvasSize"
        message="Calling `Canvas.getHeight()` is usually wrong; you should be calling `getHeight()` instead"
        errorLine1="                    canvas.getWidth(), canvas.getHeight(), Bitmap.Config.ARGB_8888);"
        errorLine2="                                       ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/ai/guiji/duix/test/ui/view/VisualizerView.java"
            line="98"
            column="40"/>
    </issue>

    <issue
        id="CanvasSize"
        message="Calling `Canvas.getWidth()` is usually wrong; you should be calling `getWidth()` instead"
        errorLine1="                    canvas.getWidth(), canvas.getHeight(), Bitmap.Config.ARGB_8888);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/ai/guiji/duix/test/ui/view/VisualizerView.java"
            line="98"
            column="21"/>
    </issue>

    <issue
        id="CustomViewStyleable"
        message="By convention, the custom view (`VisualizerView`) and the declare-styleable (`visualizerView`) should have the same name (various editor features rely on this convention)"
        errorLine1="        TypedArray args = context.obtainStyledAttributes(attrs, R.styleable.visualizerView);"
        errorLine2="                                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/ai/guiji/duix/test/ui/view/VisualizerView.java"
            line="73"
            column="65"/>
    </issue>

    <issue
        id="NotificationPermission"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission (usage from com.bumptech.glide.request.target.NotificationTarget)">
        <location
            file="src/main/AndroidManifest.xml"/>
    </issue>

    <issue
        id="SimpleDateFormat"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates."
        errorLine1="        SimpleDateFormat fmt = new SimpleDateFormat(&quot;yyyyMMddHHmmssSSS&quot;);"
        errorLine2="                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/ai/guiji/duix/test/util/StringUtils.java"
            line="170"
            column="32"/>
    </issue>

    <issue
        id="SimpleDateFormat"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates."
        errorLine1="        SimpleDateFormat format = new SimpleDateFormat(&quot;yyyyMMddHHmmssSSS&quot;);"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/ai/guiji/duix/test/util/StringUtils.java"
            line="177"
            column="35"/>
    </issue>

    <issue
        id="SimpleDateFormat"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates."
        errorLine1="        SimpleDateFormat format = new SimpleDateFormat(&quot;yyyy-MM&quot;);"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/ai/guiji/duix/test/util/StringUtils.java"
            line="183"
            column="35"/>
    </issue>

    <issue
        id="SimpleDateFormat"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates."
        errorLine1="        SimpleDateFormat format = new SimpleDateFormat(&quot;MM-dd HH:mm&quot;);"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/ai/guiji/duix/test/util/StringUtils.java"
            line="189"
            column="35"/>
    </issue>

    <issue
        id="SimpleDateFormat"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates."
        errorLine1="        SimpleDateFormat sdf = new SimpleDateFormat(&quot;yyyy-MM-dd&quot;);"
        errorLine2="                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/ai/guiji/duix/test/util/StringUtils.java"
            line="365"
            column="32"/>
    </issue>

    <issue
        id="UseSwitchCompatOrMaterialXml"
        message="Use `SwitchCompat` from AppCompat or `SwitchMaterial` from Material library"
        errorLine1="            &lt;Switch"
        errorLine2="            ^">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="157"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.13.0"
        errorLine1=""
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="47"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.appcompat:appcompat than 1.2.0 is available: 1.6.1"
        errorLine1="dependencies {"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="48"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.android.material:material than 1.4.0 is available: 1.11.0"
        errorLine1=""
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="49"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.constraintlayout:constraintlayout than 2.1.1 is available: 2.1.4"
        errorLine1="    implementation fileTree(include: [&apos;*.jar&apos;, &apos;*.aar&apos;], dir: &apos;libs&apos;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="50"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.activity:activity than 1.3.0 is available: 1.8.2"
        errorLine1="    implementation &apos;androidx.core:core-ktx:1.12.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="51"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.fragment:fragment than 1.3.0 is available: 1.5.7"
        errorLine1="    implementation &apos;androidx.appcompat:appcompat:1.2.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="52"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.android.exoplayer:exoplayer than 2.14.2 is available: 2.18.5"
        errorLine1=""
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="59"
            column="20"/>
    </issue>

    <issue
        id="SpUsage"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes"
        errorLine1="        android:textSize=&quot;18dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="15"
            column="9"/>
    </issue>

    <issue
        id="DrawAllocation"
        message="Avoid object allocations during draw/layout operations (preallocate and reuse instead)"
        errorLine1="        canvas.drawBitmap(mCanvasBitmap, new Matrix(), null);"
        errorLine2="                                         ~~~~~~~~~~~~">
        <location
            file="src/main/java/ai/guiji/duix/test/ui/view/VisualizerView.java"
            line="116"
            column="42"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is never &lt; 24"
        errorLine1="        if (Build.VERSION.SDK_INT &lt; Build.VERSION_CODES.M) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/ai/guiji/duix/test/ui/activity/BaseActivity.java"
            line="89"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_200` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_200&quot;>#FFBB86FC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_500&quot;>#FF6200EE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_700` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_700&quot;>#FF3700B3&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="5"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_200` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_200&quot;>#FF03DAC5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="6"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_700` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_700&quot;>#FF018786&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="7"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_launcher_background` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_launcher_background.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_launcher_foreground` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_launcher_foreground.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.need_record_permission` appears to be unused"
        errorLine1="    &lt;string name=&quot;need_record_permission&quot;>需要授予语音权限以继续操作&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="15"
            column="13"/>
    </issue>

    <issue
        id="TextFields"
        message="This text field does not specify an `inputType`"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="47"
            column="14"/>
    </issue>

    <issue
        id="TextFields"
        message="This text field does not specify an `inputType`"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="70"
            column="14"/>
    </issue>

    <issue
        id="TextFields"
        message="This text field does not specify an `inputType`"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="93"
            column="14"/>
    </issue>

    <issue
        id="TextFields"
        message="This text field does not specify an `inputType`"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="115"
            column="14"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="47"
            column="14"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="70"
            column="14"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="93"
            column="14"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="115"
            column="14"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="    &lt;ImageView"
        errorLine2="     ~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_call.xml"
            line="7"
            column="6"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="            &lt;ImageView"
        errorLine2="             ~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_call.xml"
            line="33"
            column="14"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="            &lt;ImageView"
        errorLine2="             ~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_call.xml"
            line="43"
            column="14"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="            &lt;ImageView"
        errorLine2="             ~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_call.xml"
            line="93"
            column="14"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="            &lt;ImageView"
        errorLine2="             ~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_call.xml"
            line="103"
            column="14"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="47"
            column="14"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="70"
            column="14"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="93"
            column="14"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="115"
            column="14"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;1214228509954805760&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;1214228509954805760&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="51"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;5b8b91f1-fb2d-4cdf-a8f0-5f6dd178687b&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;5b8b91f1-fb2d-4cdf-a8f0-5f6dd178687b&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="74"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;1764548680285933569&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;1764548680285933569&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="97"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;300&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;300&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="119"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;启动ASR&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;启动ASR&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_test_asr.xml"
            line="19"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;关闭ASR&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;关闭ASR&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_test_asr.xml"
            line="27"
            column="9"/>
    </issue>

</issues>
