<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="Theme.DUIX.Test" parent="Theme.MaterialComponents.Light.NoActionBar">
        <!-- Modern Blue Theme -->
        <item name="colorPrimary">#007AFF</item>
        <item name="colorPrimaryDark">#0056D3</item>
        <item name="colorAccent">#34C759</item>

        <!-- TextAppearance attributes for Material Components -->
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        
        <!-- Window configuration -->
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowBackground">#F2F2F7</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowTranslucentStatus">false</item>
        
        <!-- Status bar -->
        <item name="android:statusBarColor">#007AFF</item>
        <item name="android:navigationBarColor">#007AFF</item>
        
        <!-- Text colors -->
        <item name="android:textColorPrimary">#1C1C1E</item>
        <item name="android:textColorSecondary">#3C3C43</item>
    </style>

    <!-- 设置页面专用样式 -->
    <style name="SettingsCardTitle">
        <item name="android:textColor">@color/md3_on_surface</item>
        <item name="android:textSize">@dimen/text_size_subtitle</item>
        <item name="android:textStyle">bold</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="SettingsCardIcon">
        <item name="android:layout_width">@dimen/icon_size_medium</item>
        <item name="android:layout_height">@dimen/icon_size_medium</item>
        <item name="android:tint">@color/md3_on_surface_variant</item>
    </style>

    <style name="SettingsInputField">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="boxBackgroundMode">outline</item>
        <item name="boxCornerRadiusBottomEnd">@dimen/input_corner_radius</item>
        <item name="boxCornerRadiusBottomStart">@dimen/input_corner_radius</item>
        <item name="boxCornerRadiusTopEnd">@dimen/input_corner_radius</item>
        <item name="boxCornerRadiusTopStart">@dimen/input_corner_radius</item>
        <item name="boxStrokeColor">@color/md3_outline</item>
    </style>

    <style name="SettingsCard">
        <item name="cardBackgroundColor">@color/md3_surface</item>
        <item name="cardCornerRadius">@dimen/card_corner_radius</item>
        <item name="cardElevation">@dimen/card_elevation</item>
        <item name="strokeWidth">0dp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginBottom">@dimen/spacing_large</item>
    </style>

    <style name="SettingsButton" parent="Widget.Material3.Button">
        <item name="android:textSize">@dimen/text_size_button</item>
        <item name="iconGravity">start</item>
    </style>

    <style name="SettingsButton.Tonal" parent="Widget.Material3.Button.TonalButton">
        <item name="android:textSize">@dimen/text_size_button</item>
        <item name="iconGravity">start</item>
    </style>

</resources>