<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">

    <!-- 视频播放器 -->
    <VideoView
        android:id="@+id/video_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true" />

    <!-- 加载指示器 -->
    <ProgressBar
        android:id="@+id/pb_loading"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_centerInParent="true"
        android:indeterminateTint="@color/digital_primary_light"
        android:visibility="visible" />

    <!-- 顶部工具栏 -->
    <LinearLayout
        android:id="@+id/ll_toolbar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_alignParentTop="true"
        android:background="@drawable/bg_digital_overlay"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="8dp">

        <!-- 返回按钮 -->
        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/bg_digital_icon_button"
            android:padding="8dp"
            android:src="@drawable/ic_arrow_back"
            android:contentDescription="返回" />

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            android:text="视频播放"
            android:textColor="@color/digital_text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end" />

        <!-- 全屏按钮 -->
        <ImageView
            android:id="@+id/iv_fullscreen"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/bg_digital_icon_button"
            android:padding="8dp"
            android:src="@drawable/ic_fullscreen"
            android:contentDescription="全屏" />

    </LinearLayout>

    <!-- 播放控制栏 -->
    <LinearLayout
        android:id="@+id/ll_controls"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@drawable/bg_digital_overlay"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="12dp">

        <!-- 播放/暂停按钮 -->
        <ImageView
            android:id="@+id/iv_play_pause"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/bg_digital_icon_button"
            android:padding="8dp"
            android:src="@android:drawable/ic_media_play"
            android:contentDescription="播放/暂停" />

        <!-- 当前时间 -->
        <TextView
            android:id="@+id/tv_current_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:text="00:00"
            android:textColor="@color/digital_text_primary"
            android:textSize="12sp" />

        <!-- 进度条 -->
        <SeekBar
            android:id="@+id/seek_bar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:progressTint="@color/digital_primary"
            android:thumbTint="@color/digital_primary" />

        <!-- 总时长 -->
        <TextView
            android:id="@+id/tv_total_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:text="00:00"
            android:textColor="@color/digital_text_primary"
            android:textSize="12sp" />

        <!-- 音量按钮 -->
        <ImageView
            android:id="@+id/iv_volume"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/bg_digital_icon_button"
            android:padding="8dp"
            android:src="@android:drawable/ic_lock_silent_mode_off"
            android:contentDescription="音量" />

    </LinearLayout>

    <!-- 中央播放按钮（初始状态） -->
    <ImageView
        android:id="@+id/iv_center_play"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_centerInParent="true"
        android:background="@drawable/bg_digital_icon_button"
        android:padding="20dp"
        android:src="@android:drawable/ic_media_play"
        android:visibility="gone" />

    <!-- 错误提示 -->
    <LinearLayout
        android:id="@+id/ll_error"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <ImageView
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:src="@android:drawable/ic_dialog_alert"
            android:tint="@color/digital_text_secondary" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="视频加载失败"
            android:textColor="@color/digital_text_secondary"
            android:textSize="14sp" />

        <Button
            android:id="@+id/btn_retry"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="重试"
            android:textColor="@color/digital_text_primary"
            android:background="@drawable/bg_digital_button" />

    </LinearLayout>

</RelativeLayout>
