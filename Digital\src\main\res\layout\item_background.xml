<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="4dp">

        <!-- 背景图片 -->
        <ImageView
            android:id="@+id/iv_background"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="fitCenter"
            android:adjustViewBounds="true"
            android:background="@color/design_default_color_surface"
            app:layout_constraintDimensionRatio="H,3:4"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 选中状态覆盖层 -->
        <View
            android:id="@+id/view_selected_overlay"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#80000000"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/iv_background"
            app:layout_constraintEnd_toEndOf="@id/iv_background"
            app:layout_constraintStart_toStartOf="@id/iv_background"
            app:layout_constraintTop_toTopOf="@id/iv_background" />

        <!-- 选中图标 -->
        <ImageView
            android:id="@+id/iv_selected_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_check"
            android:tint="@color/white"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/iv_background"
            app:layout_constraintEnd_toEndOf="@id/iv_background"
            app:layout_constraintStart_toStartOf="@id/iv_background"
            app:layout_constraintTop_toTopOf="@id/iv_background" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
