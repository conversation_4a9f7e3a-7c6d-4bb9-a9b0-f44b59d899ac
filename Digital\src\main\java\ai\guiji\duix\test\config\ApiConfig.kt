package ai.guiji.duix.test.config

/**
 * API配置类
 * 统一管理所有API接口路径
 */
object ApiConfig {
    
    /**
     * TTS相关接口路径
     */
    object TTS {
        const val TTS_ENDPOINT = "/v2/tts"
    }

    /**
     * LLM相关接口路径
     */
    object LLM {
        const val LLM_STREAMING_ENDPOINT = "/v2/llm-streaming"
        const val GET_CACHED_QUESTIONS_ENDPOINT = "/v2/get-cached-questions"
    }

    /**
     * 唤醒词相关接口路径
     */
    object WakeWord {
        const val GET_WAKE_WORDS_ENDPOINT = "/api/wakeword/"
    }

    /**
     * 设备相关接口路径
     */
    object Device {
        const val DEVICE_APPS = "/api/device/devices"

        /**
         * 构建设备应用列表接口路径
         * @param deviceId 设备ID
         * @return 完整的设备接口路径
         */
        fun getDeviceAppsPath(deviceId: String): String {
            return "$DEVICE_APPS/$deviceId"
        }
    }

    /**
     * 模型相关接口路径
     */
    object Model {
        const val MODELS = "/api/model/models"
    }

    /**
     * 媒体相关接口路径
     */
    object Media {
        const val MEDIA_ENDPOINT = "/api/media/media"
    }

    /**
     * Logo相关接口路径
     */
    object Logo {
        const val LOGO_LIST_ENDPOINT = "/api/logo/list"
    }

    /**
     * 其他API接口路径可以在这里添加
     */
    object Other {
        // 示例：const val USER_INFO = "/api/user/info"
        // 示例：const val UPLOAD = "/api/upload"
    }
    
    /**
     * 构建完整的API URL
     * @param baseUrl 基础服务器地址 (例如: "http://dt10mm6798527.vicp.fun:17586")
     * @param endpoint 接口路径 (例如: "/v2/tts")
     * @return 完整的API URL
     */
    fun buildUrl(baseUrl: String, endpoint: String): String {
        val cleanBaseUrl = baseUrl.trimEnd('/')
        val cleanEndpoint = if (endpoint.startsWith("/")) endpoint else "/$endpoint"
        return "$cleanBaseUrl$cleanEndpoint"
    }
}
