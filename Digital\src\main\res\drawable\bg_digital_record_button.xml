<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <gradient
                android:startColor="#1E40AF"
                android:endColor="#1E3A8A"
                android:angle="135" />
            <stroke
                android:width="3dp"
                android:color="#FFFFFF" />
        </shape>
    </item>
    <item android:state_enabled="false">
        <shape android:shape="oval">
            <solid android:color="@color/digital_button_secondary" />
            <stroke 
                android:width="2dp" 
                android:color="#60FFFFFF" />
        </shape>
    </item>
    <item>
        <shape android:shape="oval">
            <gradient
                android:startColor="#2563EB"
                android:endColor="#1D4ED8"
                android:angle="135" />
            <stroke
                android:width="2dp"
                android:color="#FFFFFF" />
        </shape>
    </item>
</selector>
