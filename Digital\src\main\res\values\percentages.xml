<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 百分比配置 - 响应式设计 -->

    <!-- 顶部工具栏位置 -->
    <item name="top_toolbar_position_percent" format="float" type="dimen">0.03</item>  <!-- 距离顶部3% -->

    <!-- 开场白卡片位置 -->
    <item name="opening_statement_position_percent" format="float" type="dimen">0.35</item>  <!-- 垂直居中偏上 -->

    <!-- 用户问题显示位置 - 下移避免挡住数字人脸部 -->
    <item name="user_question_position_percent" format="float" type="dimen">0.45</item>  <!-- 距离顶部45% -->

    <!-- AI回复区域位置 - 下移并缩小高度 -->
    <item name="ai_answer_position_percent" format="float" type="dimen">0.55</item>  <!-- 距离顶部55% -->
    <item name="ai_answer_end_percent" format="float" type="dimen">0.85</item>  <!-- 距离顶部85%结束，缩小高度 -->

    <!-- 底部推荐问题位置 -->
    <item name="bottom_suggestions_position_percent" format="float" type="dimen">0.88</item>  <!-- 距离顶部88% -->

    <!-- 录音按钮位置 - 移动到右上角偏下（稍微上移避免重叠） -->
    <item name="record_button_position_percent" format="float" type="dimen">0.25</item>  <!-- 距离顶部25% -->

    <!-- 开场白左侧3个问题位置 - 放在历史问题下面一点点 -->
    <item name="greeting_questions_top_percent" format="float" type="dimen">0.72</item>  <!-- 距离顶部72%，在历史问题下面 -->
    <item name="greeting_questions_left_percent" format="float" type="dimen">0.05</item>  <!-- 距离左侧5% -->
</resources>
