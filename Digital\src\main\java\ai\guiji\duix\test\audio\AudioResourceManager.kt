package ai.guiji.duix.test.audio

import android.content.Context
import android.media.AudioAttributes
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.media.AudioRecord
import android.os.Build
import android.util.Log

/**
 * 音频资源管理器
 * 主要功能：解决AI回复时唤醒词检测失败的音频冲突问题
 * 
 * 问题分析：
 * 1. AI播放音频时，麦克风录制受到干扰
 * 2. 音频焦点管理不当，导致录制权限被抢占
 * 3. 缺少回音消除，AI声音被录入影响唤醒词检测
 * 
 * 解决方案：
 * 1. 智能音频焦点管理
 * 2. 音频会话隔离
 * 3. 回音抑制
 * 4. 动态音频参数调整
 */
class AudioResourceManager(private val context: Context) {
    
    companion object {
        private const val TAG = "AudioResourceManager"
        
        // 音频参数配置
        private const val SAMPLE_RATE = 16000
        private const val CHANNEL_CONFIG = android.media.AudioFormat.CHANNEL_IN_MONO
        private const val AUDIO_FORMAT = android.media.AudioFormat.ENCODING_PCM_16BIT
        
        // 回音抑制参数
        private const val ECHO_SUPPRESSION_DELAY_MS = 100
        private const val VOLUME_THRESHOLD = 0.3f
    }
    
    private val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
    private var audioFocusRequest: AudioFocusRequest? = null
    private var hasAudioFocus = false
    
    // 音频状态监控
    private var isPlayingAudio = false
    private var isRecordingAudio = false
    private var lastPlaybackStartTime = 0L
    
    // 回音抑制相关
    private var playbackVolume = 1.0f
    private var recordingSensitivity = 1.0f
    
    /**
     * 初始化音频资源管理器
     */
    fun initialize() {
        Log.i(TAG, "初始化音频资源管理器")
        setupAudioFocusRequest()
        configureAudioSession()
    }
    
    /**
     * 设置音频焦点请求
     */
    private fun setupAudioFocusRequest() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val audioAttributes = AudioAttributes.Builder()
                .setUsage(AudioAttributes.USAGE_VOICE_COMMUNICATION)
                .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                .build()
                
            audioFocusRequest = AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN_TRANSIENT_MAY_DUCK)
                .setAudioAttributes(audioAttributes)
                .setAcceptsDelayedFocusGain(true)
                .setOnAudioFocusChangeListener(audioFocusChangeListener)
                .build()
        }
    }
    
    /**
     * 配置音频会话
     */
    private fun configureAudioSession() {
        try {
            // 设置音频模式为通信模式，优化语音处理
            audioManager.mode = AudioManager.MODE_IN_COMMUNICATION
            
            // 启用回音消除和噪音抑制
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                audioManager.setParameters("noise_suppression=on")
                audioManager.setParameters("echo_cancellation=on")
                audioManager.setParameters("auto_gain_control=on")
            }
            
            Log.i(TAG, "音频会话配置完成")
        } catch (e: Exception) {
            Log.e(TAG, "配置音频会话失败: ${e.message}", e)
        }
    }
    
    /**
     * 音频焦点变化监听器
     */
    private val audioFocusChangeListener = AudioManager.OnAudioFocusChangeListener { focusChange ->
        when (focusChange) {
            AudioManager.AUDIOFOCUS_GAIN -> {
                Log.i(TAG, "获得音频焦点")
                hasAudioFocus = true
                restoreAudioSettings()
            }
            AudioManager.AUDIOFOCUS_LOSS -> {
                Log.i(TAG, "失去音频焦点")
                hasAudioFocus = false
                pauseAudioOperations()
            }
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> {
                Log.i(TAG, "暂时失去音频焦点")
                hasAudioFocus = false
                pauseAudioOperations()
            }
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK -> {
                Log.i(TAG, "音频焦点被抢占，但可以降低音量")
                adjustVolumeForDucking()
            }
        }
    }
    
    /**
     * 请求音频焦点
     */
    fun requestAudioFocus(): Boolean {
        return try {
            val result = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && audioFocusRequest != null) {
                audioManager.requestAudioFocus(audioFocusRequest!!)
            } else {
                @Suppress("DEPRECATION")
                audioManager.requestAudioFocus(
                    audioFocusChangeListener,
                    AudioManager.STREAM_VOICE_CALL,
                    AudioManager.AUDIOFOCUS_GAIN_TRANSIENT_MAY_DUCK
                )
            }
            
            hasAudioFocus = result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
            Log.i(TAG, "音频焦点请求结果: ${if (hasAudioFocus) "成功" else "失败"}")
            hasAudioFocus
        } catch (e: Exception) {
            Log.e(TAG, "请求音频焦点失败: ${e.message}", e)
            false
        }
    }
    
    /**
     * 释放音频焦点
     */
    fun releaseAudioFocus() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && audioFocusRequest != null) {
                audioManager.abandonAudioFocusRequest(audioFocusRequest!!)
            } else {
                @Suppress("DEPRECATION")
                audioManager.abandonAudioFocus(audioFocusChangeListener)
            }
            hasAudioFocus = false
            Log.i(TAG, "音频焦点已释放")
        } catch (e: Exception) {
            Log.e(TAG, "释放音频焦点失败: ${e.message}", e)
        }
    }
    
    /**
     * 开始音频播放时的处理
     */
    fun onAudioPlaybackStart() {
        Log.i(TAG, "========== 音频播放开始 ==========")
        isPlayingAudio = true
        lastPlaybackStartTime = System.currentTimeMillis()
        
        // 调整录制参数以减少干扰
        adjustRecordingForPlayback()
        
        Log.i(TAG, "音频播放状态已更新，当前时间: $lastPlaybackStartTime")
    }
    
    /**
     * 音频播放结束时的处理
     */
    fun onAudioPlaybackEnd() {
        Log.i(TAG, "========== 音频播放结束 ==========")
        isPlayingAudio = false
        
        // 恢复正常录制参数
        restoreRecordingSettings()
        
        Log.i(TAG, "音频播放状态已重置")
    }
    
    /**
     * 检查是否可以进行录制
     */
    fun canStartRecording(): Boolean {
        val timeSincePlayback = System.currentTimeMillis() - lastPlaybackStartTime
        val canRecord = !isPlayingAudio || timeSincePlayback > ECHO_SUPPRESSION_DELAY_MS
        
        Log.i(TAG, "录制可用性检查:")
        Log.i(TAG, "  当前播放状态: $isPlayingAudio")
        Log.i(TAG, "  距离上次播放: ${timeSincePlayback}ms")
        Log.i(TAG, "  可以录制: $canRecord")
        
        return canRecord
    }
    
    /**
     * 获取优化的录制参数
     */
    fun getOptimizedRecordingParams(): RecordingParams {
        val bufferSize = AudioRecord.getMinBufferSize(SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT)
        val optimizedBufferSize = if (isPlayingAudio) {
            // 播放时使用更大的缓冲区减少干扰
            bufferSize * 3
        } else {
            bufferSize * 2
        }
        
        return RecordingParams(
            sampleRate = SAMPLE_RATE,
            channelConfig = CHANNEL_CONFIG,
            audioFormat = AUDIO_FORMAT,
            bufferSize = optimizedBufferSize,
            sensitivity = recordingSensitivity
        )
    }
    
    /**
     * 调整录制参数以适应播放
     */
    private fun adjustRecordingForPlayback() {
        Log.i(TAG, "调整录制参数以减少播放干扰")
        
        // 降低录制灵敏度
        recordingSensitivity = 0.7f
        
        // 设置音频参数
        try {
            audioManager.setParameters("input_source=7") // VOICE_RECOGNITION
            audioManager.setParameters("noise_suppression=on")
            audioManager.setParameters("echo_cancellation=on")
        } catch (e: Exception) {
            Log.w(TAG, "设置音频参数失败: ${e.message}")
        }
    }
    
    /**
     * 恢复正常录制设置
     */
    private fun restoreRecordingSettings() {
        Log.i(TAG, "恢复正常录制设置")
        recordingSensitivity = 1.0f
    }
    
    /**
     * 恢复音频设置
     */
    private fun restoreAudioSettings() {
        playbackVolume = 1.0f
        recordingSensitivity = 1.0f
    }
    
    /**
     * 暂停音频操作
     */
    private fun pauseAudioOperations() {
        // 这里可以添加暂停逻辑
        Log.i(TAG, "暂停音频操作")
    }
    
    /**
     * 调整音量以适应音频抢占
     */
    private fun adjustVolumeForDucking() {
        playbackVolume = VOLUME_THRESHOLD
        recordingSensitivity = 1.2f // 提高录制灵敏度补偿
        Log.i(TAG, "调整音量以适应音频抢占，播放音量: $playbackVolume, 录制灵敏度: $recordingSensitivity")
    }
    
    /**
     * 释放资源
     */
    fun release() {
        Log.i(TAG, "释放音频资源管理器")
        releaseAudioFocus()
        
        // 恢复音频模式
        try {
            audioManager.mode = AudioManager.MODE_NORMAL
        } catch (e: Exception) {
            Log.e(TAG, "恢复音频模式失败: ${e.message}")
        }
    }
    
    /**
     * 录制参数数据类
     */
    data class RecordingParams(
        val sampleRate: Int,
        val channelConfig: Int,
        val audioFormat: Int,
        val bufferSize: Int,
        val sensitivity: Float
    )
}
