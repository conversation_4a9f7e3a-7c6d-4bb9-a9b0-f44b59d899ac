package ai.guiji.duix.test.audio

import android.content.Context
import android.media.AudioManager
import android.media.AudioRecord
import android.os.Handler
import android.os.Looper
import android.util.Log
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong

/**
 * 音频状态监控器
 * 主要功能：监控音频系统状态，诊断唤醒词检测问题
 * 
 * 监控内容：
 * 1. 音频焦点状态
 * 2. 录制设备状态
 * 3. 播放设备状态
 * 4. 音频信号强度
 * 5. 系统音频配置
 */
class AudioStateMonitor(private val context: Context) {
    
    companion object {
        private const val TAG = "AudioStateMonitor"
        private const val MONITOR_INTERVAL_MS = 1000L
        private const val AUDIO_LEVEL_THRESHOLD = 500 // 音频信号阈值
    }
    
    private val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
    private val isMonitoring = AtomicBoolean(false)
    private val lastAudioSignalTime = AtomicLong(0)
    
    private var monitorHandler: Handler? = null
    private var testAudioRecord: AudioRecord? = null
    
    // 监控数据
    private var audioFocusState = "UNKNOWN"
    private var recordingState = "IDLE"
    private var playbackState = "IDLE"
    private var audioSignalLevel = 0
    private var lastDiagnosisTime = 0L
    
    /**
     * 开始监控
     */
    fun startMonitoring() {
        if (isMonitoring.get()) {
            Log.w(TAG, "监控已在运行中")
            return
        }
        
        Log.i(TAG, "========== 开始音频状态监控 ==========")
        isMonitoring.set(true)
        
        monitorHandler = Handler(Looper.getMainLooper())
        startPeriodicMonitoring()
        
        Log.i(TAG, "✓ 音频状态监控已启动")
    }
    
    /**
     * 停止监控
     */
    fun stopMonitoring() {
        if (!isMonitoring.get()) {
            return
        }
        
        Log.i(TAG, "========== 停止音频状态监控 ==========")
        isMonitoring.set(false)
        
        monitorHandler?.removeCallbacksAndMessages(null)
        monitorHandler = null
        
        // 释放测试录制器
        testAudioRecord?.stop()
        testAudioRecord?.release()
        testAudioRecord = null
        
        Log.i(TAG, "✓ 音频状态监控已停止")
    }
    
    /**
     * 开始周期性监控
     */
    private fun startPeriodicMonitoring() {
        if (!isMonitoring.get()) return
        
        try {
            // 检查音频焦点状态
            checkAudioFocusState()
            
            // 检查录制状态
            checkRecordingState()
            
            // 检查播放状态
            checkPlaybackState()
            
            // 检查音频信号
            checkAudioSignal()
            
            // 每5秒输出一次诊断报告
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastDiagnosisTime > 5000) {
                outputDiagnosisReport()
                lastDiagnosisTime = currentTime
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "监控过程中发生错误: ${e.message}", e)
        }
        
        // 安排下次监控
        monitorHandler?.postDelayed({
            startPeriodicMonitoring()
        }, MONITOR_INTERVAL_MS)
    }
    
    /**
     * 检查音频焦点状态
     */
    private fun checkAudioFocusState() {
        try {
            // 通过音频管理器获取当前状态
            val mode = audioManager.mode
            val isMusicActive = audioManager.isMusicActive
            val isSpeakerphoneOn = audioManager.isSpeakerphoneOn
            
            audioFocusState = when (mode) {
                AudioManager.MODE_NORMAL -> "NORMAL"
                AudioManager.MODE_RINGTONE -> "RINGTONE"
                AudioManager.MODE_IN_CALL -> "IN_CALL"
                AudioManager.MODE_IN_COMMUNICATION -> "IN_COMMUNICATION"
                else -> "UNKNOWN($mode)"
            }
            
            if (isMusicActive) {
                audioFocusState += " + MUSIC_ACTIVE"
            }
            
        } catch (e: Exception) {
            audioFocusState = "ERROR: ${e.message}"
        }
    }
    
    /**
     * 检查录制状态
     */
    private fun checkRecordingState() {
        try {
            // 尝试创建测试录制器来检查录制可用性
            if (testAudioRecord == null) {
                val bufferSize = AudioRecord.getMinBufferSize(
                    16000,
                    android.media.AudioFormat.CHANNEL_IN_MONO,
                    android.media.AudioFormat.ENCODING_PCM_16BIT
                )
                
                if (bufferSize > 0) {
                    testAudioRecord = AudioRecord(
                        android.media.MediaRecorder.AudioSource.VOICE_RECOGNITION,
                        16000,
                        android.media.AudioFormat.CHANNEL_IN_MONO,
                        android.media.AudioFormat.ENCODING_PCM_16BIT,
                        bufferSize
                    )
                }
            }
            
            recordingState = when (testAudioRecord?.state) {
                AudioRecord.STATE_INITIALIZED -> "INITIALIZED"
                AudioRecord.STATE_UNINITIALIZED -> "UNINITIALIZED"
                else -> "UNKNOWN"
            }
            
            if (testAudioRecord?.recordingState == AudioRecord.RECORDSTATE_RECORDING) {
                recordingState += " + RECORDING"
            }
            
        } catch (e: Exception) {
            recordingState = "ERROR: ${e.message}"
        }
    }
    
    /**
     * 检查播放状态
     */
    private fun checkPlaybackState() {
        try {
            val isMusicActive = audioManager.isMusicActive
            val streamVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC)
            val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)
            
            playbackState = if (isMusicActive) {
                "PLAYING (音量: $streamVolume/$maxVolume)"
            } else {
                "IDLE (音量: $streamVolume/$maxVolume)"
            }
            
        } catch (e: Exception) {
            playbackState = "ERROR: ${e.message}"
        }
    }
    
    /**
     * 检查音频信号强度
     */
    private fun checkAudioSignal() {
        try {
            if (testAudioRecord?.state == AudioRecord.STATE_INITIALIZED) {
                // 临时启动录制来检测信号
                if (testAudioRecord?.recordingState != AudioRecord.RECORDSTATE_RECORDING) {
                    testAudioRecord?.startRecording()
                }
                
                val buffer = ShortArray(1024)
                val read = testAudioRecord?.read(buffer, 0, buffer.size) ?: 0
                
                if (read > 0) {
                    // 计算音频信号强度（RMS）
                    var sum = 0L
                    for (i in 0 until read) {
                        sum += (buffer[i] * buffer[i]).toLong()
                    }
                    audioSignalLevel = kotlin.math.sqrt(sum.toDouble() / read).toInt()
                    
                    if (audioSignalLevel > AUDIO_LEVEL_THRESHOLD) {
                        lastAudioSignalTime.set(System.currentTimeMillis())
                    }
                } else {
                    audioSignalLevel = 0
                }
                
                // 停止录制
                testAudioRecord?.stop()
            }
            
        } catch (e: Exception) {
            Log.w(TAG, "检查音频信号失败: ${e.message}")
            audioSignalLevel = -1
        }
    }
    
    /**
     * 输出诊断报告
     */
    private fun outputDiagnosisReport() {
        val currentTime = System.currentTimeMillis()
        val timeSinceLastSignal = currentTime - lastAudioSignalTime.get()
        
        val report = StringBuilder()
        report.appendLine("========== 音频状态监控报告 ==========")
        report.appendLine("时间: ${java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date())}")
        report.appendLine("音频焦点: $audioFocusState")
        report.appendLine("录制状态: $recordingState")
        report.appendLine("播放状态: $playbackState")
        report.appendLine("信号强度: $audioSignalLevel")
        report.appendLine("上次有效信号: ${if (timeSinceLastSignal < 60000) "${timeSinceLastSignal}ms前" else "超过1分钟"}")
        
        // 问题诊断
        val issues = mutableListOf<String>()
        
        if (recordingState.contains("ERROR") || recordingState.contains("UNINITIALIZED")) {
            issues.add("录制设备异常")
        }
        
        if (audioSignalLevel == -1) {
            issues.add("无法检测音频信号")
        } else if (audioSignalLevel == 0 && timeSinceLastSignal > 10000) {
            issues.add("长时间无音频输入")
        }
        
        if (playbackState.contains("PLAYING") && audioSignalLevel > 0) {
            issues.add("可能存在回音干扰")
        }
        
        if (issues.isNotEmpty()) {
            report.appendLine("⚠ 发现问题: ${issues.joinToString(", ")}")
        } else {
            report.appendLine("✓ 音频系统状态正常")
        }
        
        report.appendLine("=====================================")
        
        Log.i(TAG, report.toString())
    }
    
    /**
     * 获取当前状态摘要
     */
    fun getCurrentStatus(): String {
        return "焦点:$audioFocusState | 录制:$recordingState | 播放:$playbackState | 信号:$audioSignalLevel"
    }
    
    /**
     * 检查是否存在音频冲突
     */
    fun hasAudioConflict(): Boolean {
        return playbackState.contains("PLAYING") && 
               (recordingState.contains("ERROR") || audioSignalLevel == -1)
    }
}
