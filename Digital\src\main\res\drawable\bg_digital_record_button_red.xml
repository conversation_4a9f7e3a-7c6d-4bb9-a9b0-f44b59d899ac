<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <gradient
                android:startColor="#FF4444"
                android:endColor="#CC0000"
                android:angle="135" />
            <stroke 
                android:width="3dp" 
                android:color="#FFFFFF" />
        </shape>
    </item>
    <item android:state_enabled="false">
        <shape android:shape="oval">
            <solid android:color="#80FF4444" />
            <stroke 
                android:width="2dp" 
                android:color="#60FFFFFF" />
        </shape>
    </item>
    <item>
        <shape android:shape="oval">
            <gradient
                android:startColor="#FF6666"
                android:endColor="#FF4444"
                android:angle="135" />
            <stroke 
                android:width="2dp" 
                android:color="#FFFFFF" />
        </shape>
    </item>
</selector>
