<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="200dp"
    android:layout_height="150dp"
    android:layout_marginEnd="12dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/digital_card_background">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- 图片显示 -->
        <ImageView
            android:id="@+id/iv_media_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:visibility="gone" />

        <!-- 视频显示 -->
        <VideoView
            android:id="@+id/vv_media_video"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />

        <!-- 视频播放按钮覆盖层 -->
        <ImageView
            android:id="@+id/iv_play_button"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_centerInParent="true"
            android:src="@android:drawable/ic_media_play"
            android:background="@drawable/bg_digital_icon_button"
            android:padding="12dp"
            android:visibility="gone" />

        <!-- 加载指示器 -->
        <ProgressBar
            android:id="@+id/pb_loading"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_centerInParent="true"
            android:indeterminateTint="@color/digital_primary_light" />

        <!-- 标题覆盖层 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="@drawable/bg_digital_overlay"
            android:orientation="vertical"
            android:padding="8dp">

            <TextView
                android:id="@+id/tv_media_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="媒体标题"
                android:textColor="@color/digital_text_primary"
                android:textSize="12sp"
                android:textStyle="bold"
                android:maxLines="2"
                android:ellipsize="end" />

        </LinearLayout>

    </RelativeLayout>

</androidx.cardview.widget.CardView>
