# 唤醒词回复功能说明

## 🎯 功能概述

新增了唤醒词回复功能，当用户说出唤醒词成功唤醒数字人时，系统会自动播放"我在.mp3"音频文件作为回复，让交互更加自然和人性化。

## 🚀 功能特点

### ✨ 主要特性
- **自动回复**: 唤醒词检测成功后立即播放"我在"音频
- **可控开关**: 在设置界面提供开关控制，用户可自由启用/禁用
- **无缝集成**: 与现有唤醒词系统完美集成，不影响其他功能
- **音频质量**: 使用高质量的MP3音频文件，确保回复清晰
- **回音消除**: 智能暂停ASR录音，避免录入数字人自己的声音

### 🎵 音频文件
- **女声版本**: `Digital/src/main/assets/wav/我在.wav` (reference_id: woman)
- **男声版本**: `Digital/src/main/assets/wav/我在_男.wav` (reference_id: man)
- **音频格式**: 16kHz WAV 单声道（优化后格式）
- **音频内容**: "我在"语音回复
- **播放方式**: 通过DUIX数字人播放，与其他音频保持一致
- **自动选择**: 根据当前数字人模型的性别自动选择对应音频

## 🎮 使用方法

### 启用/禁用功能
1. 进入**设置界面**
2. 找到**"语音控制"**卡片
3. 在卡片中找到**"唤醒词回复"**开关
4. 切换开关状态：
   - **开启**：唤醒时播放"我在"音频回复
   - **关闭**：唤醒时不播放回复音频

### 体验功能
1. 确保已启用**本地ASR**功能
2. 确保**唤醒词回复**开关已开启
3. 说出配置的唤醒词（如"小爱同学"）
4. 系统检测到唤醒词后会：
   - 显示"唤醒成功: [唤醒词]"提示
   - **立即播放"我在"音频回复**
   - **自动暂停ASR录音，避免录入回复音频**
   - 等待回复音频播放完成（约3秒）
   - 恢复ASR录音并开始语音识别等待用户输入

## 🔧 技术实现

### 核心组件
1. **设置界面开关** - 控制功能启用/禁用
2. **音频播放逻辑** - 在唤醒词检测成功时播放回复音频
3. **配置管理** - 通过SharedPreferences保存用户设置

### 实现细节

#### 设置界面
```xml
<!-- 唤醒词回复开关 -->
<TextView android:text="唤醒词回复" />
<TextView android:text="@string/wakeup_reply_desc" />
<SwitchCompat android:id="@+id/switch_wakeup_reply" />
```

#### 配置管理
```kotlin
// 设置键名
const val KEY_WAKEUP_REPLY = "wakeup_reply"

// 读取设置（默认开启）
val enableWakeupReply = sharedPrefs.getBoolean(KEY_WAKEUP_REPLY, true)

// 保存设置
sharedPrefs.edit().putBoolean(KEY_WAKEUP_REPLY, isChecked).apply()
```

#### 音频播放
```kotlin
private fun playWakeupReplyAudio() {
    // 检查是否启用了唤醒词回复
    val enableWakeupReply = sharedPrefs.getBoolean(SettingsActivity.KEY_WAKEUP_REPLY, true)
    if (!enableWakeupReply) {
        Log.i(TAG_NET, "唤醒词回复已禁用，跳过播放")
        return
    }

    // 确保reference_id是最新的
    updateReferenceId()

    // 使用更新后的reference_id
    val currentReferenceId = reference_id ?: "man"

    // 根据当前reference_id选择对应的音频文件
    val audioFileName = when (currentReferenceId) {
        "woman" -> "我在.wav"      // 女声版本
        "man" -> "我在_男.wav"     // 男声版本
        else -> "我在.wav"         // 默认女声版本
    }

    Log.i(TAG_NET, "播放唤醒词回复音频: $audioFileName (reference_id: $currentReferenceId, 16kHz单声道)")
    try {
        playWav(audioFileName)
    } catch (e: Exception) {
        Log.e(TAG_NET, "播放唤醒词回复音频失败: ${e.message}", e)
    }
}
```

#### 唤醒词检测集成
```kotlin
// 在唤醒词检测成功的回调中添加
if (keyword.isNotBlank()) {
    runOnUiThread {
        Toast.makeText(this, "唤醒成功: $keyword", Toast.LENGTH_SHORT).show()
    }

    // 播放唤醒词回复音频
    playWakeupReplyAudio()
    
    // ... 其他唤醒逻辑
}
```

## 📱 用户界面

### 设置界面布局
```
语音控制
├── 语音识别 [按钮]    唤醒词 [按钮]
├── 启用本地ASR ────────────────── [开关]
│   使用本地语音识别引擎
└── 唤醒词回复 ────────────────── [开关]
    唤醒时播放"我在"音频回复
```

### 交互流程
```
用户说唤醒词 → 检测成功 → 暂停ASR → 播放"我在"回复 → 恢复ASR → 开始语音识别
```

### 回音消除机制
```
播放回复音频时：
1. 立即暂停ASR录音
2. 播放"我在"音频（约1-2秒）
3. 延迟2.5秒后恢复ASR录音
4. 确保不会录入数字人自己的声音
```

## 🎨 用户体验

### 交互优化
- **即时反馈**: 唤醒成功后立即播放回复，让用户知道系统已响应
- **自然对话**: "我在"回复模拟真实对话场景，提升交互自然度
- **可控性**: 用户可根据个人喜好启用或禁用此功能

### 场景适配
- **安静环境**: 回复音频确认系统已唤醒，避免用户重复唤醒
- **嘈杂环境**: 音频回复比视觉提示更容易被用户感知
- **无障碍使用**: 为视觉不便的用户提供音频反馈

## ⚙️ 配置选项

### 默认设置
- **唤醒词回复**: 默认**开启**
- **音频文件**: 固定使用"我在.mp3"
- **播放方式**: 通过DUIX数字人播放

### 自定义选项
- 用户可在设置界面自由开启/关闭功能
- 设置会自动保存，重启应用后保持用户选择

## 🔄 兼容性

### 系统要求
- 需要启用**本地ASR**功能
- 需要配置有效的**唤醒词**
- 音频播放依赖DUIX数字人系统

### 功能依赖
- ✅ 与现有唤醒词系统兼容
- ✅ 与语音识别功能兼容
- ✅ 与音频播放系统兼容
- ✅ 不影响其他音频播放功能

## 📝 使用建议

### 最佳实践
1. **首次使用**: 建议保持默认开启状态体验功能
2. **环境适配**: 在安静环境中使用效果更佳
3. **音量调节**: 确保设备音量适中，避免回复音频过大或过小

### 故障排除
1. **无回复音频**: 检查唤醒词回复开关是否开启
2. **音频异常**: 确认"我在.mp3"文件存在且完整
3. **功能失效**: 确认本地ASR和唤醒词功能正常工作

---

**注意**: 此功能需要与唤醒词检测功能配合使用，请确保已正确配置唤醒词和本地ASR功能。
