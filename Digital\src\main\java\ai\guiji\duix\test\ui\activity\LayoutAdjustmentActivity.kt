package ai.guiji.duix.test.ui.activity

import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import android.util.Log
import android.widget.SeekBar
import androidx.appcompat.app.AppCompatActivity
import ai.guiji.duix.test.databinding.ActivityLayoutAdjustmentBinding

/**
 * 界面布局调整Activity
 * 允许用户动态调整各个UI元素的位置
 */
class LayoutAdjustmentActivity : AppCompatActivity() {

    private lateinit var binding: ActivityLayoutAdjustmentBinding
    private lateinit var sharedPreferences: SharedPreferences

    companion object {
        const val PREFS_NAME = "layout_adjustment"
        const val KEY_LAYOUT_VERSION = "layout_version"  // 新增：布局版本号
        const val CURRENT_LAYOUT_VERSION = 2  // 当前版本号（推荐问题宽度改为50%）

        // SharedPreferences键名
        const val KEY_OPENING_STATEMENT = "opening_statement_position"
        const val KEY_GREETING_QUESTIONS = "greeting_questions_position"
        const val KEY_GREETING_QUESTIONS_WIDTH = "greeting_questions_width"  // 新增：推荐问题宽度
        const val KEY_CONVERSATION_DIALOG = "conversation_dialog_position"  // 合并：对话框位置
        const val KEY_CONVERSATION_HEIGHT = "conversation_height"  // 新增：对话框高度
        const val KEY_BOTTOM_SUGGESTIONS = "bottom_suggestions_position"
        const val KEY_RECORD_BUTTON = "record_button_position"
        const val KEY_INTERRUPT_HINT = "interrupt_hint_position"  // 新增：打断提示条位置

        // 默认值（对应原始百分比配置）
        const val DEFAULT_OPENING_STATEMENT = 0.35f
        const val DEFAULT_GREETING_QUESTIONS = 0.72f
        const val DEFAULT_GREETING_QUESTIONS_WIDTH = 0.50f  // 修改：推荐问题宽度默认50%
        const val DEFAULT_CONVERSATION_DIALOG = 0.45f  // 合并：对话框位置（原用户问题位置）
        const val DEFAULT_CONVERSATION_HEIGHT = 0.42f  // 新增：对话框高度（25%）
        const val DEFAULT_BOTTOM_SUGGESTIONS = 0.88f
        const val DEFAULT_RECORD_BUTTON = 0.25f
        const val DEFAULT_INTERRUPT_HINT = 0.36f  // 新增：打断提示条默认位置36%
        
        fun start(context: Context) {
            context.startActivity(Intent(context, LayoutAdjustmentActivity::class.java))
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLayoutAdjustmentBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        sharedPreferences = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

        // 检查布局版本，如果版本不匹配则重置为默认值
        checkAndUpdateLayoutVersion()

        initViews()
        loadSavedValues()
        setupListeners()
    }

    /**
     * 检查并更新布局版本
     * 如果版本不匹配，清除旧的设置以使用新的默认值
     */
    private fun checkAndUpdateLayoutVersion() {
        val savedVersion = sharedPreferences.getInt(KEY_LAYOUT_VERSION, 0)
        if (savedVersion < CURRENT_LAYOUT_VERSION) {
            Log.i("LayoutAdjustment", "布局版本更新：$savedVersion -> $CURRENT_LAYOUT_VERSION，重置为新的默认值")

            // 清除所有旧的布局设置
            sharedPreferences.edit()
                .clear()
                .putInt(KEY_LAYOUT_VERSION, CURRENT_LAYOUT_VERSION)
                .apply()

            android.widget.Toast.makeText(this, "布局设置已更新为新的默认值", android.widget.Toast.LENGTH_SHORT).show()
        }
    }

    private fun initViews() {
        // 返回按钮
        binding.ivBack.setOnClickListener {
            finish()
        }
        
        // 预览按钮
        binding.fabShowPreview.setOnClickListener {
            showFixedPreview()
        }
        
        // 关闭预览按钮
        binding.btnCloseFixedPreview.setOnClickListener {
            hideFixedPreview()
        }
    }
    
    /**
     * 显示悬浮在顶部的预览卡片
     */
    private fun showFixedPreview() {
        // 显示预览卡片和占位空间
        binding.fixedPreviewCard.visibility = android.view.View.VISIBLE
        binding.previewSpace.visibility = android.view.View.VISIBLE
        binding.fabShowPreview.visibility = android.view.View.GONE
        
        // 更新预览内容
        updatePreview()
        
        // 平滑滚动到顶部
        binding.scrollView.smoothScrollTo(0, 0)
    }
    
    /**
     * 隐藏悬浮在顶部的预览卡片
     */
    private fun hideFixedPreview() {
        // 隐藏预览卡片和占位空间
        binding.fixedPreviewCard.visibility = android.view.View.GONE
        binding.previewSpace.visibility = android.view.View.GONE
        binding.fabShowPreview.visibility = android.view.View.VISIBLE
    }
    
    /**
     * 更新预览内容
     */
    private fun updatePreview() {
        val openingStatement = sharedPreferences.getFloat(KEY_OPENING_STATEMENT, DEFAULT_OPENING_STATEMENT)
        val greetingQuestions = sharedPreferences.getFloat(KEY_GREETING_QUESTIONS, DEFAULT_GREETING_QUESTIONS)
        val greetingQuestionsWidth = sharedPreferences.getFloat(KEY_GREETING_QUESTIONS_WIDTH, DEFAULT_GREETING_QUESTIONS_WIDTH)
        val conversationDialog = sharedPreferences.getFloat(KEY_CONVERSATION_DIALOG, DEFAULT_CONVERSATION_DIALOG)
        val conversationHeight = sharedPreferences.getFloat(KEY_CONVERSATION_HEIGHT, DEFAULT_CONVERSATION_HEIGHT)
        val bottomSuggestions = sharedPreferences.getFloat(KEY_BOTTOM_SUGGESTIONS, DEFAULT_BOTTOM_SUGGESTIONS)
        val recordButton = sharedPreferences.getFloat(KEY_RECORD_BUTTON, DEFAULT_RECORD_BUTTON)
        val interruptHint = sharedPreferences.getFloat(KEY_INTERRUPT_HINT, DEFAULT_INTERRUPT_HINT)

        binding.layoutPreview.updatePositions(
            openingStatement,
            greetingQuestions,
            greetingQuestionsWidth,
            conversationDialog,
            conversationDialog + conversationHeight,  // 对话框结束位置：开始位置 + 动态高度
            bottomSuggestions,
            recordButton,
            interruptHint
        )
    }

    private fun loadSavedValues() {
        // 加载保存的值，如果没有则使用默认值
        val openingStatement = sharedPreferences.getFloat(KEY_OPENING_STATEMENT, DEFAULT_OPENING_STATEMENT)
        val greetingQuestions = sharedPreferences.getFloat(KEY_GREETING_QUESTIONS, DEFAULT_GREETING_QUESTIONS)
        val greetingQuestionsWidth = sharedPreferences.getFloat(KEY_GREETING_QUESTIONS_WIDTH, DEFAULT_GREETING_QUESTIONS_WIDTH)
        val conversationDialog = sharedPreferences.getFloat(KEY_CONVERSATION_DIALOG, DEFAULT_CONVERSATION_DIALOG)
        val conversationHeight = sharedPreferences.getFloat(KEY_CONVERSATION_HEIGHT, DEFAULT_CONVERSATION_HEIGHT)
        val bottomSuggestions = sharedPreferences.getFloat(KEY_BOTTOM_SUGGESTIONS, DEFAULT_BOTTOM_SUGGESTIONS)
        val recordButton = sharedPreferences.getFloat(KEY_RECORD_BUTTON, DEFAULT_RECORD_BUTTON)
        val interruptHint = sharedPreferences.getFloat(KEY_INTERRUPT_HINT, DEFAULT_INTERRUPT_HINT)

        // 设置SeekBar进度（直接使用百分比值，范围0-100）
        binding.sbOpeningStatement.progress = (openingStatement * 100).toInt()
        binding.sbGreetingQuestions.progress = (greetingQuestions * 100).toInt()
        binding.sbGreetingQuestionsWidth.progress = (greetingQuestionsWidth * 100).toInt()
        binding.sbConversationDialog.progress = (conversationDialog * 100).toInt()
        binding.sbConversationHeight.progress = (conversationHeight * 100).toInt()
        binding.sbBottomSuggestions.progress = (bottomSuggestions * 100).toInt()
        binding.sbRecordButton.progress = (recordButton * 100).toInt()
        binding.sbInterruptHint.progress = (interruptHint * 100).toInt()

        // 更新显示的百分比值
        updateValueDisplays()
    }

    private fun setupListeners() {
        // 开场白卡片位置
        binding.sbOpeningStatement.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val value = progress / 100f // 0%-100%
                    saveValue(KEY_OPENING_STATEMENT, value)
                    updateValueDisplays()
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        // 开场白问题位置
        binding.sbGreetingQuestions.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val value = progress / 100f // 0%-100%
                    saveValue(KEY_GREETING_QUESTIONS, value)
                    updateValueDisplays()
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        // 推荐问题宽度
        binding.sbGreetingQuestionsWidth.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val value = progress / 100f // 20%-60%
                    saveValue(KEY_GREETING_QUESTIONS_WIDTH, value)
                    updateValueDisplays()
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        // 对话框位置
        binding.sbConversationDialog.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val value = progress / 100f // 0%-100%
                    saveValue(KEY_CONVERSATION_DIALOG, value)
                    updateValueDisplays()
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        // 对话框高度
        binding.sbConversationHeight.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val value = progress / 100f // 15%-50%
                    saveValue(KEY_CONVERSATION_HEIGHT, value)
                    updateValueDisplays()
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        // 底部推荐问题位置
        binding.sbBottomSuggestions.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val value = progress / 100f // 0%-100%
                    saveValue(KEY_BOTTOM_SUGGESTIONS, value)
                    updateValueDisplays()
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        // 录音按钮位置
        binding.sbRecordButton.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val value = progress / 100f // 0%-100%
                    saveValue(KEY_RECORD_BUTTON, value)
                    updateValueDisplays()
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        // 打断提示条位置
        binding.sbInterruptHint.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val value = progress / 100f // 0%-100%
                    saveValue(KEY_INTERRUPT_HINT, value)
                    updateValueDisplays()
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        // 预设方案按钮
        binding.btnPresetSmall.setOnClickListener { applySmallScreenPreset() }
        binding.btnPresetLarge.setOnClickListener { applyLargeScreenPreset() }
        binding.btnReset.setOnClickListener { resetToDefaults() }

        // 操作按钮 - 只保留"保存并进入数字人"
        binding.btnSaveAndEnter.setOnClickListener { saveAndEnterDigitalHuman() }
    }

    private fun updateValueDisplays() {
        val openingStatement = sharedPreferences.getFloat(KEY_OPENING_STATEMENT, DEFAULT_OPENING_STATEMENT)
        val greetingQuestions = sharedPreferences.getFloat(KEY_GREETING_QUESTIONS, DEFAULT_GREETING_QUESTIONS)
        val greetingQuestionsWidth = sharedPreferences.getFloat(KEY_GREETING_QUESTIONS_WIDTH, DEFAULT_GREETING_QUESTIONS_WIDTH)
        val conversationDialog = sharedPreferences.getFloat(KEY_CONVERSATION_DIALOG, DEFAULT_CONVERSATION_DIALOG)
        val conversationHeight = sharedPreferences.getFloat(KEY_CONVERSATION_HEIGHT, DEFAULT_CONVERSATION_HEIGHT)
        val bottomSuggestions = sharedPreferences.getFloat(KEY_BOTTOM_SUGGESTIONS, DEFAULT_BOTTOM_SUGGESTIONS)
        val recordButton = sharedPreferences.getFloat(KEY_RECORD_BUTTON, DEFAULT_RECORD_BUTTON)
        val interruptHint = sharedPreferences.getFloat(KEY_INTERRUPT_HINT, DEFAULT_INTERRUPT_HINT)

        binding.tvOpeningStatementValue.text = "${(openingStatement * 100).toInt()}%"
        binding.tvGreetingQuestionsValue.text = "${(greetingQuestions * 100).toInt()}%"
        binding.tvGreetingQuestionsWidthValue.text = "${(greetingQuestionsWidth * 100).toInt()}%"
        binding.tvConversationDialogValue.text = "${(conversationDialog * 100).toInt()}%"
        binding.tvConversationHeightValue.text = "${(conversationHeight * 100).toInt()}%"
        binding.tvBottomSuggestionsValue.text = "${(bottomSuggestions * 100).toInt()}%"
        binding.tvRecordButtonValue.text = "${(recordButton * 100).toInt()}%"
        binding.tvInterruptHintValue.text = "${(interruptHint * 100).toInt()}%"

        // 如果预览卡片可见，更新预览
        if (binding.fixedPreviewCard.visibility == android.view.View.VISIBLE) {
            binding.layoutPreview.updatePositions(
                openingStatement,
                greetingQuestions,
                greetingQuestionsWidth,
                conversationDialog,
                conversationDialog + conversationHeight,  // 对话框结束位置：动态高度
                bottomSuggestions,
                recordButton,
                interruptHint
            )
        }
    }

    private fun saveValue(key: String, value: Float) {
        sharedPreferences.edit().putFloat(key, value).apply()
    }

    private fun applySmallScreenPreset() {
        // 竖屏预设：完全按照默认值列表
        saveValue(KEY_OPENING_STATEMENT, DEFAULT_OPENING_STATEMENT)  // 35%
        saveValue(KEY_GREETING_QUESTIONS, DEFAULT_GREETING_QUESTIONS)  // 72%
        saveValue(KEY_GREETING_QUESTIONS_WIDTH, DEFAULT_GREETING_QUESTIONS_WIDTH)  // 50%
        saveValue(KEY_CONVERSATION_DIALOG, DEFAULT_CONVERSATION_DIALOG)  // 45%
        saveValue(KEY_CONVERSATION_HEIGHT, DEFAULT_CONVERSATION_HEIGHT)  // 42%
        saveValue(KEY_BOTTOM_SUGGESTIONS, DEFAULT_BOTTOM_SUGGESTIONS)  // 88%
        saveValue(KEY_RECORD_BUTTON, DEFAULT_RECORD_BUTTON)  // 25%
        saveValue(KEY_INTERRUPT_HINT, DEFAULT_INTERRUPT_HINT)  // 36%

        loadSavedValues()
    }

    private fun applyLargeScreenPreset() {
        // 横屏预设：元素更分散（保持现状）
        saveValue(KEY_OPENING_STATEMENT, 0.40f)
        saveValue(KEY_GREETING_QUESTIONS, 0.75f)
        saveValue(KEY_GREETING_QUESTIONS_WIDTH, 0.25f)  // 横屏：较窄的推荐问题区域
        saveValue(KEY_CONVERSATION_DIALOG, 0.50f)
        saveValue(KEY_CONVERSATION_HEIGHT, 0.30f)  // 横屏：较大的对话框高度
        saveValue(KEY_BOTTOM_SUGGESTIONS, 0.90f)
        saveValue(KEY_RECORD_BUTTON, 0.30f)
        saveValue(KEY_INTERRUPT_HINT, 0.36f)  // 横屏：打断提示条位置36%

        loadSavedValues()
    }

    private fun resetToDefaults() {
        // 重置为默认值
        saveValue(KEY_OPENING_STATEMENT, DEFAULT_OPENING_STATEMENT)
        saveValue(KEY_GREETING_QUESTIONS, DEFAULT_GREETING_QUESTIONS)
        saveValue(KEY_GREETING_QUESTIONS_WIDTH, DEFAULT_GREETING_QUESTIONS_WIDTH)
        saveValue(KEY_CONVERSATION_DIALOG, DEFAULT_CONVERSATION_DIALOG)
        saveValue(KEY_CONVERSATION_HEIGHT, DEFAULT_CONVERSATION_HEIGHT)
        saveValue(KEY_BOTTOM_SUGGESTIONS, DEFAULT_BOTTOM_SUGGESTIONS)
        saveValue(KEY_RECORD_BUTTON, DEFAULT_RECORD_BUTTON)
        saveValue(KEY_INTERRUPT_HINT, DEFAULT_INTERRUPT_HINT)

        // 更新版本号
        sharedPreferences.edit().putInt(KEY_LAYOUT_VERSION, CURRENT_LAYOUT_VERSION).apply()

        loadSavedValues()
    }

    /**
     * 保存并进入数字人界面
     */
    private fun saveAndEnterDigitalHuman() {
        // 设置已经实时保存了，现在启动数字人界面
        try {
            // 获取必要的参数
            val sharedPrefs = getSharedPreferences(SettingsActivity.PREF_NAME, Context.MODE_PRIVATE)
            val ttsUrl = sharedPrefs.getString(SettingsActivity.KEY_TTS_URL, "") ?: ""
            val baseDir = getExternalFilesDir("duix")?.absolutePath ?: ""
            val modelDir = java.io.File(baseDir, "models").absolutePath

            if (ttsUrl.isEmpty()) {
                android.widget.Toast.makeText(this, "请先在设置中配置TTS服务地址", android.widget.Toast.LENGTH_LONG).show()
                return
            }

            val intent = android.content.Intent(this, CallActivity::class.java).apply {
                putExtra("baseDir", baseDir)
                putExtra("modelDir", modelDir)
                putExtra("ttsUrl", ttsUrl)
                putExtra("layoutAdjusted", true) // 标记来自布局调整
            }

            android.widget.Toast.makeText(this, "布局设置已保存，正在启动数字人", android.widget.Toast.LENGTH_SHORT).show()
            startActivity(intent)
            finish()

        } catch (e: Exception) {
            android.util.Log.e("LayoutAdjustment", "启动数字人失败: ${e.message}", e)
            android.widget.Toast.makeText(this, "启动失败: ${e.message}", android.widget.Toast.LENGTH_LONG).show()
        }
    }
}
