# 语速设置功能实现说明

## 📋 功能概述

在设置界面新增了语速调整功能，用户可以通过滑动条调节TTS语速，范围为1.0x-2.0x，设置值会在调用 `/v2/llm-streaming` 接口时作为 `tts_speed` 请求头传递给后端。

## 🔧 实现内容

### 1. 常量定义 (SettingsActivity.kt)

```kotlin
companion object {
    // ... 其他常量
    const val KEY_TTS_SPEED = "tts_speed"  // 新增：TTS语速设置
    const val DEFAULT_TTS_SPEED = 1.0f  // 默认语速
}
```

### 2. UI界面 (activity_settings.xml)

新增了以下UI组件：

```xml
<!-- 语速设置标题 -->
<TextView
    android:id="@+id/tv_tts_speed_label"
    android:text="语速设置"
    android:textStyle="bold" />

<!-- 当前语速值显示 -->
<TextView
    android:id="@+id/tv_tts_speed_value"
    android:text="1.0x" />

<!-- 语速调节滑动条 -->
<SeekBar
    android:id="@+id/seekbar_tts_speed"
    android:max="100"
    android:progress="0" />

<!-- 语速范围提示 -->
<TextView
    android:id="@+id/tv_tts_speed_range"
    android:text="1.0x - 2.0x" />
```

### 3. 核心功能实现 (SettingsActivity.kt)

#### 初始化方法
```kotlin
private fun initTtsSpeedSetting() {
    // 加载保存的语速值
    val savedSpeed = sharedPrefs.getFloat(KEY_TTS_SPEED, DEFAULT_TTS_SPEED)
    
    // 转换为SeekBar进度值 (1.0-2.0 → 0-100)
    val progress = ((savedSpeed - 1.0f) * 100).toInt()
    binding.seekbarTtsSpeed.progress = progress
    
    // 更新显示
    updateTtsSpeedDisplay(savedSpeed)
    
    // 设置监听器
    binding.seekbarTtsSpeed.setOnSeekBarChangeListener(...)
}
```

#### 辅助方法
```kotlin
// 更新语速显示
private fun updateTtsSpeedDisplay(speed: Float) {
    binding.tvTtsSpeedValue.text = String.format("%.1fx", speed)
}

// 保存语速设置
private fun saveTtsSpeed(speed: Float) {
    sharedPrefs.edit().putFloat(KEY_TTS_SPEED, speed).apply()
}
```

### 4. 请求头传递 (CallActivity.kt)

在 `/v2/llm-streaming` 请求中添加语速参数：

```kotlin
// 获取语速设置
val ttsSpeed = sharedPrefs.getFloat(SettingsActivity.KEY_TTS_SPEED, SettingsActivity.DEFAULT_TTS_SPEED)

// 构建请求时添加请求头
val request = Request.Builder()
    .url(fullLlmUrl)
    .get()
    .addHeader("dify_api_key", currentApiKey)
    .addHeader("reference_id", referenceId)
    .addHeader("user_id", userId)
    .addHeader("tts_speed", ttsSpeed.toString())  // 新增语速请求头
    .addHeader("User-Agent", "Android App")
    .addHeader("Accept", "*/*")
    .addHeader("Connection", "keep-alive")
    .build()
```

## 🎯 功能特点

### 1. 用户友好的界面
- **直观的滑动条**: 用户可以通过拖动滑动条调节语速
- **实时预览**: 拖动时实时显示当前语速值
- **范围提示**: 显示可调节的语速范围(1.0x-2.0x)

### 2. 精确的数值控制
- **浮点数精度**: 支持0.1的精度调节
- **范围限制**: 严格限制在1.0-2.0倍速之间
- **默认值**: 默认语速为1.0x（正常速度）

### 3. 持久化存储
- **自动保存**: 用户停止拖动时自动保存设置
- **跨会话保持**: 应用重启后保持用户设置
- **SharedPreferences**: 使用Android标准存储方式

### 4. 完整的日志记录
- **初始化日志**: 记录语速设置的初始化过程
- **保存日志**: 记录每次语速设置的保存
- **请求日志**: 记录发送给后端的语速参数

## 📊 数值转换逻辑

### SeekBar进度值 ↔ 语速值转换

```kotlin
// 语速值(1.0-2.0) → SeekBar进度值(0-100)
val progress = ((speed - 1.0f) * 100).toInt()

// SeekBar进度值(0-100) → 语速值(1.0-2.0)
val speed = 1.0f + (progress / 100.0f)
```

### 示例转换表

| 语速值 | SeekBar进度 | 显示文本 |
|--------|-------------|----------|
| 1.0x   | 0          | 1.0x     |
| 1.2x   | 20         | 1.2x     |
| 1.5x   | 50         | 1.5x     |
| 1.8x   | 80         | 1.8x     |
| 2.0x   | 100        | 2.0x     |

## 🔄 工作流程

1. **用户打开设置界面**
   - 从SharedPreferences加载保存的语速值
   - 设置SeekBar的初始位置
   - 显示当前语速值

2. **用户调节语速**
   - 拖动SeekBar滑动条
   - 实时更新显示的语速值
   - 停止拖动时自动保存设置

3. **发起LLM请求**
   - 从SharedPreferences读取语速设置
   - 将语速值作为`tts_speed`请求头发送
   - 后端根据语速参数调整TTS输出

## 🚀 使用方法

### 用户操作
1. 打开应用设置界面
2. 找到"语速设置"区域
3. 拖动滑动条调节语速
4. 观察实时显示的语速值
5. 松开滑动条，设置自动保存

### 开发者验证
1. 查看日志输出确认语速设置已保存
2. 发起LLM请求时检查请求头包含`tts_speed`
3. 验证后端接收到正确的语速参数

## 📝 注意事项

1. **数值精度**: 语速值保留一位小数显示
2. **范围限制**: 严格限制在1.0-2.0之间，超出范围会被自动调整
3. **默认值**: 新用户默认语速为1.0x
4. **兼容性**: 与现有的TTS功能完全兼容
5. **性能**: 设置保存操作异步执行，不影响UI响应

## ✅ 测试验证

### 功能测试
- [x] 滑动条可以正常拖动
- [x] 语速值实时更新显示
- [x] 设置能够正确保存和加载
- [x] 请求头正确包含tts_speed参数

### 边界测试
- [x] 最小值1.0x正常工作
- [x] 最大值2.0x正常工作
- [x] 中间值(如1.5x)正常工作
- [x] 应用重启后设置保持不变
