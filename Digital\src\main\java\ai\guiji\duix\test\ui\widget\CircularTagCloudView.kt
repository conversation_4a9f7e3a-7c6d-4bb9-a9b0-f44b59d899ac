package ai.guiji.duix.test.ui.widget

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.animation.LinearInterpolator
import androidx.core.content.ContextCompat
import ai.guiji.duix.test.R
import kotlin.math.*

class CircularTagCloudView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    data class TagItem(
        val text: String,
        val color: Int,
        var angle: Float = 0f,
        var radius: Float = 0f,
        var x: Float = 0f,
        var y: Float = 0f,
        var scale: Float = 1f
    )

    private val tags = mutableListOf<TagItem>()
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val rectF = RectF()
    
    private var centerX = 0f
    private var centerY = 0f
    private var baseRadius = 200f
    private var currentRotation = 0f
    
    private var rotationAnimator: ValueAnimator? = null
    
    // 随机颜色数组（不包含白色）
    private val colors = arrayOf(
        0xFFFF6B6B.toInt(), // 红色
        0xFF4ECDC4.toInt(), // 青色
        0xFF45B7D1.toInt(), // 蓝色
        0xFF96CEB4.toInt(), // 绿色
        0xFFFECA57.toInt(), // 黄色
        0xFFFF9FF3.toInt(), // 粉色
        0xFF54A0FF.toInt(), // 天蓝色
        0xFF5F27CD.toInt()  // 紫色
    )
    
    var onTagClickListener: ((String) -> Unit)? = null
    
    init {
        paint.style = Paint.Style.FILL
        textPaint.color = android.graphics.Color.WHITE
        textPaint.textAlign = Paint.Align.CENTER
        textPaint.textSize = 32f
        textPaint.isFakeBoldText = true
        
        startRotationAnimation()
    }
    
    fun setTags(tagTexts: List<String>) {
        tags.clear()
        
        tagTexts.forEachIndexed { index, text ->
            val angle = (360f / tagTexts.size) * index
            val radiusVariation = (50..150).random()
            val radius = baseRadius + radiusVariation
            
            tags.add(TagItem(
                text = text,
                color = colors.random(),
                angle = angle,
                radius = radius
            ))
        }
        
        updateTagPositions()
        invalidate()
    }
    
    private fun updateTagPositions() {
        tags.forEach { tag ->
            val totalAngle = tag.angle + currentRotation
            val radians = Math.toRadians(totalAngle.toDouble())
            
            tag.x = centerX + (tag.radius * cos(radians)).toFloat()
            tag.y = centerY + (tag.radius * sin(radians)).toFloat()
            
            // 3D效果：根据Y位置调整缩放
            val normalizedY = (tag.y - centerY) / baseRadius
            tag.scale = 0.7f + 0.3f * (1f - abs(normalizedY))
        }
    }
    
    private fun startRotationAnimation() {
        rotationAnimator?.cancel()
        
        rotationAnimator = ValueAnimator.ofFloat(0f, 360f).apply {
            duration = 20000 // 20秒一圈
            repeatCount = ValueAnimator.INFINITE
            interpolator = LinearInterpolator()
            
            addUpdateListener { animator ->
                currentRotation = animator.animatedValue as Float
                updateTagPositions()
                invalidate()
            }
            
            start()
        }
    }
    
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        centerX = w / 2f
        centerY = h / 2f
        baseRadius = minOf(w, h) / 4f
        updateTagPositions()
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        // 按Z轴顺序绘制标签（后面的先画）
        tags.sortedBy { it.y }.forEach { tag ->
            drawTag(canvas, tag)
        }
    }
    
    private fun drawTag(canvas: Canvas, tag: TagItem) {
        val scaledTextSize = textPaint.textSize * tag.scale
        textPaint.textSize = scaledTextSize
        
        // 计算文本尺寸
        val textWidth = textPaint.measureText(tag.text)
        val textHeight = scaledTextSize
        
        // 绘制背景
        paint.color = tag.color
        val padding = 16f * tag.scale
        rectF.set(
            tag.x - textWidth / 2 - padding,
            tag.y - textHeight / 2 - padding,
            tag.x + textWidth / 2 + padding,
            tag.y + textHeight / 2 + padding
        )
        
        val cornerRadius = 16f * tag.scale
        canvas.drawRoundRect(rectF, cornerRadius, cornerRadius, paint)
        
        // 绘制文本
        canvas.drawText(tag.text, tag.x, tag.y + textHeight / 4, textPaint)
    }
    
    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (event.action == MotionEvent.ACTION_DOWN) {
            val touchX = event.x
            val touchY = event.y
            
            // 检查是否点击了某个标签
            tags.forEach { tag ->
                val textWidth = textPaint.measureText(tag.text)
                val textHeight = textPaint.textSize * tag.scale
                val padding = 16f * tag.scale
                
                if (touchX >= tag.x - textWidth / 2 - padding &&
                    touchX <= tag.x + textWidth / 2 + padding &&
                    touchY >= tag.y - textHeight / 2 - padding &&
                    touchY <= tag.y + textHeight / 2 + padding) {
                    
                    onTagClickListener?.invoke(tag.text)
                    return true
                }
            }
        }
        return super.onTouchEvent(event)
    }
    
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        rotationAnimator?.cancel()
    }
}
