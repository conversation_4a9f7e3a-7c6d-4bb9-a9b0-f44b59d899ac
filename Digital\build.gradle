plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace 'ai.guiji.duix.test'
    compileSdk 34

    defaultConfig {
        applicationId "ai.guiji.duix.test.v3"
        minSdk 24
        targetSdk 34
        versionCode 3
        versionName "3.0.3"
    }

    signingConfigs {
        release {
            storeFile file('../demo.jks')
            storePassword '123456'
            keyAlias 'demo'
            keyPassword '123456'
        }
    }

    //日志开关
    def LOG_DEBUG = "LOG_DEBUG"
    //FileProvider
    def AUTHORITY = "AUTHORITY"

    defaultConfig {
        buildConfigField "boolean", LOG_DEBUG, "true"
        buildConfigField "String", AUTHORITY, "\"${defaultConfig.applicationId}.fileprovider\""
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
        release {
            minifyEnabled true  // 启用代码混淆和压缩
            shrinkResources true  // 启用资源压缩
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    buildFeatures {
        viewBinding true
    }
    composeOptions {
        kotlinCompilerExtensionVersion '1.4.3'
    }
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }

    lint {
        baseline = file("lint-baseline.xml")
    }
    lintOptions{
        checkReleaseBuilds false
    }
}

dependencies {

    implementation fileTree(include: ['*.jar', '*.aar'], dir: 'libs')
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.10.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation "androidx.activity:activity:1.8.2"
    implementation "androidx.fragment:fragment:1.6.2"

    implementation 'com.github.bumptech.glide:glide:4.12.0'

    implementation project(":duix-sdk")
    implementation project(":SherpaOnnxKws")
    implementation project(":minerva")
//    implementation project(":sherp")
    implementation 'com.squareup.okhttp3:okhttp:4.10.0'
    implementation 'com.squareup.okhttp3:okhttp-sse:4.10.0'
    implementation 'com.google.android.exoplayer:exoplayer:2.14.2'

    // JSON parsing using Gson (lightweight and compatible)
    implementation 'com.google.code.gson:gson:2.10.1'

    // Markdown rendering for AI responses
    implementation 'io.noties.markwon:core:4.6.2'
    implementation 'io.noties.markwon:ext-strikethrough:4.6.2'
    implementation 'io.noties.markwon:html:4.6.2'

    // FlexboxLayout for history questions tag cloud
    implementation 'com.google.android.flexbox:flexbox:3.0.0'

    implementation 'ink.lodz:pandora:2.0.8'
    implementation "com.github.permissions-dispatcher:ktx:1.1.4"
    implementation 'com.github.wendykierp:JTransforms:3.1'
    implementation 'androidx.annotation:annotation:1.8.0'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.0'
}