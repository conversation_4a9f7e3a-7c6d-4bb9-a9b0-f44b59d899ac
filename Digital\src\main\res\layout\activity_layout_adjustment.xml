<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_dark">
    
    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- 标题栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="24dp">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_back"
                android:tint="@color/white"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="4dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="界面布局调整"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold"
                android:gravity="center"
                android:layout_marginEnd="28dp" />

        </LinearLayout>

        <!-- 说明文字 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            app:cardBackgroundColor="@color/card_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="通过调整下方滑块来自定义各个界面元素的位置，适配您的设备屏幕。调整后的设置会自动保存。点击右下角按钮可显示布局预览。"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:padding="16dp"
                android:lineSpacingExtra="2dp" />

        </androidx.cardview.widget.CardView>
        
        <!-- 固定布局预览卡片的占位空间（默认隐藏） -->
        <Space
            android:id="@+id/preview_space"
            android:layout_width="match_parent"
            android:layout_height="350dp"
            android:layout_marginBottom="20dp"
            android:visibility="gone" />

        <!-- 预设方案 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            app:cardBackgroundColor="@color/card_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="预设方案"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <Button
                        android:id="@+id/btn_preset_small"
                        android:layout_width="0dp"
                        android:layout_height="40dp"
                        android:layout_weight="1"
                        android:text="竖屏"
                        android:textSize="12sp"
                        android:layout_marginEnd="8dp"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                    <Button
                        android:id="@+id/btn_preset_large"
                        android:layout_width="0dp"
                        android:layout_height="40dp"
                        android:layout_weight="1"
                        android:text="横屏"
                        android:textSize="12sp"
                        android:layout_marginEnd="8dp"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                    <Button
                        android:id="@+id/btn_reset"
                        android:layout_width="0dp"
                        android:layout_height="40dp"
                        android:layout_weight="1"
                        android:text="重置默认"
                        android:textSize="12sp"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 开场白卡片位置 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/card_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="开场白卡片位置"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="调整数字人介绍卡片的垂直位置（距离屏幕顶部的百分比）"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0%"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp"
                        android:minWidth="40dp" />

                    <SeekBar
                        android:id="@+id/sb_opening_statement"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:max="100"
                        android:progress="35"
                        android:layout_marginHorizontal="12dp" />

                    <TextView
                        android:id="@+id/tv_opening_statement_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="35%"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        android:minWidth="40dp"
                        android:gravity="end" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 开场白问题位置 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/card_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="开场白问题位置"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="调整左侧3个开场白问题的垂直位置（建议放在历史问题下方）"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0%"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp"
                        android:minWidth="40dp" />

                    <SeekBar
                        android:id="@+id/sb_greeting_questions"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:max="100"
                        android:progress="72"
                        android:layout_marginHorizontal="12dp" />

                    <TextView
                        android:id="@+id/tv_greeting_questions_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="72%"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        android:minWidth="40dp"
                        android:gravity="end" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 推荐问题宽度 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/card_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="推荐问题宽度"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="调整左侧推荐问题区域的宽度（适配不同屏幕尺寸）"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="20%"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp"
                        android:minWidth="40dp" />

                    <SeekBar
                        android:id="@+id/sb_greeting_questions_width"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:max="60"
                        android:min="20"
                        android:progress="50"
                        android:layout_marginHorizontal="12dp" />

                    <TextView
                        android:id="@+id/tv_greeting_questions_width_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="50%"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        android:minWidth="40dp"
                        android:gravity="end" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 对话框位置 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/card_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="对话框位置"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="调整用户问题和AI回复对话框的显示位置（避免遮挡数字人脸部）"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0%"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp"
                        android:minWidth="40dp" />

                    <SeekBar
                        android:id="@+id/sb_conversation_dialog"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:max="100"
                        android:progress="45"
                        android:layout_marginHorizontal="12dp" />

                    <TextView
                        android:id="@+id/tv_conversation_dialog_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="45%"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        android:minWidth="40dp"
                        android:gravity="end" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 对话框高度 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/card_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="对话框高度"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="调整对话框的高度大小（适应不同内容长度）"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="15%"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp"
                        android:minWidth="40dp" />

                    <SeekBar
                        android:id="@+id/sb_conversation_height"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:max="42"
                        android:min="15"
                        android:progress="42"
                        android:layout_marginHorizontal="12dp" />

                    <TextView
                        android:id="@+id/tv_conversation_height_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="42%"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        android:minWidth="40dp"
                        android:gravity="end" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>


        <!-- 底部推荐问题位置 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/card_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="底部推荐问题位置"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="调整底部3个数据事件问题的显示位置"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0%"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp"
                        android:minWidth="40dp" />

                    <SeekBar
                        android:id="@+id/sb_bottom_suggestions"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:max="100"
                        android:progress="88"
                        android:layout_marginHorizontal="12dp" />

                    <TextView
                        android:id="@+id/tv_bottom_suggestions_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="88%"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        android:minWidth="40dp"
                        android:gravity="end" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 录音按钮位置 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardBackgroundColor="@color/card_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="录音按钮位置"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="调整右上角录音按钮的垂直位置"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0%"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp"
                        android:minWidth="40dp" />

                    <SeekBar
                        android:id="@+id/sb_record_button"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:max="100"
                        android:progress="25"
                        android:layout_marginHorizontal="12dp" />

                    <TextView
                        android:id="@+id/tv_record_button_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="25%"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        android:minWidth="40dp"
                        android:gravity="end" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 打断提示条位置 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/card_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="打断提示条位置"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="调整AI回复时打断提示条的垂直位置（距离屏幕顶部的百分比）"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0%"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp"
                        android:minWidth="40dp" />

                    <SeekBar
                        android:id="@+id/sb_interrupt_hint"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:max="100"
                        android:progress="40"
                        android:layout_marginHorizontal="12dp" />

                    <TextView
                        android:id="@+id/tv_interrupt_hint_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="40%"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        android:minWidth="40dp"
                        android:gravity="end" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 操作按钮区域 -->
        <!-- 保存按钮 - 只保留"保存并进入数字人"并加长宽度，使用响应式设计 -->
        <Button
            android:id="@+id/btn_save_and_enter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="48dp"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="32dp"
            android:text="保存并进入数字人"
            android:textSize="16sp"
            android:textStyle="bold"
            style="@style/Widget.Material3.Button" />

    </LinearLayout>

    </ScrollView>
    
    <!-- 悬浮在屏幕顶部的预览卡片 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/fixed_preview_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        app:cardBackgroundColor="@color/card_background"
        app:cardCornerRadius="12dp"
        app:cardElevation="8dp"
        android:visibility="gone"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">
                
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="布局预览"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold" />
                    
                <Button
                    android:id="@+id/btn_close_fixed_preview"
                    android:layout_width="wrap_content"
                    android:layout_height="36dp"
                    android:text="关闭预览"
                    android:textSize="12sp"
                    style="@style/Widget.Material3.Button.TextButton" />
            </LinearLayout>

            <ai.guiji.duix.test.ui.view.LayoutPreviewView
                android:id="@+id/layout_preview"
                android:layout_width="match_parent"
                android:layout_height="300dp"
                android:layout_marginTop="8dp" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>
    
    <!-- 悬浮按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_show_preview"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@android:drawable/ic_menu_view"
        app:tint="@color/white"
        app:backgroundTint="@color/md3_primary" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>
