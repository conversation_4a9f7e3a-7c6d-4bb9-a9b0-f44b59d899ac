package ai.guiji.duix.test.util

import android.os.Handler
import android.os.Looper
import android.util.Log
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

/**
 * 性能监控器
 * 主要功能：
 * 1. 实时性能监控
 * 2. 卡顿检测
 * 3. 性能数据收集和分析
 */
class PerformanceMonitor {
    
    companion object {
        internal const val TAG = "PerformanceMonitor"
        private const val FRAME_TIME_THRESHOLD = 16L // 16ms，60fps阈值
        internal const val BLOCK_TIME_THRESHOLD = 100L // 100ms卡顿阈值
        private const val MONITOR_INTERVAL = 1000L // 1秒监控间隔
        
        @Volatile
        private var instance: PerformanceMonitor? = null
        
        fun getInstance(): PerformanceMonitor {
            return instance ?: synchronized(this) {
                instance ?: PerformanceMonitor().also { instance = it }
            }
        }
    }
    
    private val mainHandler = Handler(Looper.getMainLooper())
    private val performanceData = ConcurrentHashMap<String, PerformanceMetric>()
    private val frameTimeRecords = mutableListOf<Long>()
    private var isMonitoring = false
    private var lastFrameTime = 0L
    
    data class PerformanceMetric(
        val name: String,
        var totalTime: AtomicLong = AtomicLong(0),
        var callCount: AtomicLong = AtomicLong(0),
        var maxTime: AtomicLong = AtomicLong(0),
        var minTime: AtomicLong = AtomicLong(Long.MAX_VALUE)
    ) {
        fun getAverageTime(): Long = if (callCount.get() > 0) totalTime.get() / callCount.get() else 0
    }
    
    /**
     * 开始性能监控
     */
    fun startMonitoring() {
        if (isMonitoring) return
        
        isMonitoring = true
        Log.i(TAG, "开始性能监控")
        
        // 启动帧率监控
        startFrameRateMonitoring()
        
        // 启动定期报告
        startPeriodicReport()
    }
    
    /**
     * 停止性能监控
     */
    fun stopMonitoring() {
        isMonitoring = false
        mainHandler.removeCallbacksAndMessages(null)
        Log.i(TAG, "停止性能监控")
    }
    
    /**
     * 记录方法执行时间
     */
    fun <T> measureTime(methodName: String, block: () -> T): T {
        val startTime = System.currentTimeMillis()
        val result = block()
        val endTime = System.currentTimeMillis()
        val duration = endTime - startTime
        
        recordPerformanceMetric(methodName, duration)
        
        if (duration > BLOCK_TIME_THRESHOLD) {
            Log.w(TAG, "检测到卡顿: $methodName 耗时 ${duration}ms")
        }
        
        return result
    }
    
    /**
     * 记录性能指标
     */
    internal fun recordPerformanceMetric(name: String, duration: Long) {
        val metric = performanceData.getOrPut(name) { PerformanceMetric(name) }
        
        metric.totalTime.addAndGet(duration)
        metric.callCount.incrementAndGet()
        
        // 更新最大值
        var currentMax = metric.maxTime.get()
        while (duration > currentMax) {
            if (metric.maxTime.compareAndSet(currentMax, duration)) break
            currentMax = metric.maxTime.get()
        }
        
        // 更新最小值
        var currentMin = metric.minTime.get()
        while (duration < currentMin) {
            if (metric.minTime.compareAndSet(currentMin, duration)) break
            currentMin = metric.minTime.get()
        }
    }
    
    /**
     * 启动帧率监控
     */
    private fun startFrameRateMonitoring() {
        val frameMonitorRunnable = object : Runnable {
            override fun run() {
                if (!isMonitoring) return
                
                val currentTime = System.currentTimeMillis()
                if (lastFrameTime > 0) {
                    val frameTime = currentTime - lastFrameTime
                    
                    synchronized(frameTimeRecords) {
                        frameTimeRecords.add(frameTime)
                        // 保持最近100帧的记录
                        if (frameTimeRecords.size > 100) {
                            frameTimeRecords.removeAt(0)
                        }
                    }
                    
                    if (frameTime > FRAME_TIME_THRESHOLD) {
                        Log.d(TAG, "帧时间超标: ${frameTime}ms")
                    }
                }
                
                lastFrameTime = currentTime
                mainHandler.postDelayed(this, FRAME_TIME_THRESHOLD)
            }
        }
        
        mainHandler.post(frameMonitorRunnable)
    }
    
    /**
     * 启动定期报告
     */
    private fun startPeriodicReport() {
        val reportRunnable = object : Runnable {
            override fun run() {
                if (!isMonitoring) return
                
                generatePerformanceReport()
                mainHandler.postDelayed(this, MONITOR_INTERVAL)
            }
        }
        
        mainHandler.postDelayed(reportRunnable, MONITOR_INTERVAL)
    }
    
    /**
     * 生成性能报告
     */
    fun generatePerformanceReport(): String {
        val report = StringBuilder()
        report.appendLine("=== 性能监控报告 ===")
        report.appendLine("时间: ${java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date())}")
        
        // 帧率统计
        synchronized(frameTimeRecords) {
            if (frameTimeRecords.isNotEmpty()) {
                val avgFrameTime = frameTimeRecords.average()
                val fps = 1000.0 / avgFrameTime
                val droppedFrames = frameTimeRecords.count { it > FRAME_TIME_THRESHOLD }
                
                report.appendLine("帧率: ${String.format("%.1f", fps)} fps")
                report.appendLine("平均帧时间: ${String.format("%.1f", avgFrameTime)} ms")
                report.appendLine("掉帧数: $droppedFrames/${frameTimeRecords.size}")
            }
        }
        
        // 方法执行时间统计
        report.appendLine("\n方法执行时间统计:")
        performanceData.values.sortedByDescending { it.getAverageTime() }.take(10).forEach { metric ->
            report.appendLine("${metric.name}:")
            report.appendLine("  调用次数: ${metric.callCount.get()}")
            report.appendLine("  平均耗时: ${metric.getAverageTime()}ms")
            report.appendLine("  最大耗时: ${metric.maxTime.get()}ms")
            report.appendLine("  最小耗时: ${if (metric.minTime.get() == Long.MAX_VALUE) 0 else metric.minTime.get()}ms")
        }
        
        // 内存使用情况
        val runtime = Runtime.getRuntime()
        val usedMemory = (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024
        val totalMemory = runtime.totalMemory() / 1024 / 1024
        val maxMemory = runtime.maxMemory() / 1024 / 1024
        
        report.appendLine("\n内存使用:")
        report.appendLine("已使用: ${usedMemory}MB")
        report.appendLine("总分配: ${totalMemory}MB")
        report.appendLine("最大可用: ${maxMemory}MB")
        report.appendLine("使用率: ${String.format("%.1f", usedMemory * 100.0 / maxMemory)}%")
        
        report.appendLine("==================")
        
        val reportString = report.toString()
        Log.i(TAG, reportString)
        return reportString
    }
    
    /**
     * 检测当前是否卡顿
     */
    fun isCurrentlyBlocked(): Boolean {
        synchronized(frameTimeRecords) {
            if (frameTimeRecords.isEmpty()) return false
            
            // 检查最近5帧是否有卡顿
            val recentFrames = frameTimeRecords.takeLast(5)
            return recentFrames.any { it > BLOCK_TIME_THRESHOLD }
        }
    }
    
    /**
     * 获取当前FPS
     */
    fun getCurrentFPS(): Double {
        synchronized(frameTimeRecords) {
            if (frameTimeRecords.isEmpty()) return 0.0
            
            val avgFrameTime = frameTimeRecords.takeLast(10).average()
            return 1000.0 / avgFrameTime
        }
    }
    
    /**
     * 清理性能数据
     */
    fun clearPerformanceData() {
        performanceData.clear()
        synchronized(frameTimeRecords) {
            frameTimeRecords.clear()
        }
        Log.i(TAG, "性能数据已清理")
    }
    
    /**
     * 释放资源
     */
    fun release() {
        stopMonitoring()
        clearPerformanceData()
        instance = null
        Log.i(TAG, "性能监控器已释放")
    }
}