android.enableJetifier=true
android.useAndroidX=true
# org.gradle.daemon=true  # 暂时禁用以避免Java版本冲突
org.gradle.jvmargs=-Xms2048m -Xmx6656m
org.gradle.java.home=C:\\Users\\<USER>\\.jdks\\ms-17.0.15

# 强制使用Java 17
org.gradle.java.installations.auto-detect=false
org.gradle.java.installations.fromEnv=JAVA_HOME

# 强制Gradle使用指定的Java版本
org.gradle.java.installations.paths=C:\\Users\\<USER>\\.jdks\\ms-17.0.15

# 禁用Gradle守护进程以避免版本冲突
org.gradle.daemon=false

# 支持16KB页面大小 (Android 15+要求)
android.native.useEmbeddedDexer=true

# 抑制compileSdk版本警告
android.suppressUnsupportedCompileSdk=34

appcompatVersion=1.3.0
recyclerviewVersion=1.2.1
preferenceVersion=1.1.0
annotationVersion=1.1.0
ottoVersion=1.3.8
ijkVersion=0.8.8
core_ktx=1.5.0
lifecycle_ktx=2.3.1
android.injected.testOnly=false
android.defaults.buildfeatures.buildconfig=true
android.nonTransitiveRClass=false
android.nonFinalResIds=false