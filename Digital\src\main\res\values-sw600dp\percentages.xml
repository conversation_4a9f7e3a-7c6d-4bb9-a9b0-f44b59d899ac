<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 百分比配置 - 大屏幕/平板适配 -->

    <!-- 顶部工具栏位置 - 大屏幕上稍微下移 -->
    <item name="top_toolbar_position_percent" format="float" type="dimen">0.05</item>  <!-- 距离顶部5% -->

    <!-- 开场白卡片位置 - 大屏幕上更居中 -->
    <item name="opening_statement_position_percent" format="float" type="dimen">0.40</item>  <!-- 垂直居中 -->

    <!-- 用户问题显示位置 - 大屏幕上下移避免挡住数字人 -->
    <item name="user_question_position_percent" format="float" type="dimen">0.40</item>  <!-- 距离顶部40% -->

    <!-- AI回复区域位置 - 大屏幕上下移并控制高度 -->
    <item name="ai_answer_position_percent" format="float" type="dimen">0.50</item>  <!-- 距离顶部50% -->
    <item name="ai_answer_end_percent" format="float" type="dimen">0.78</item>  <!-- 距离顶部78%结束 -->

    <!-- 底部推荐问题位置 - 大屏幕上距离底部更远 -->
    <item name="bottom_suggestions_position_percent" format="float" type="dimen">0.82</item>  <!-- 距离顶部82% -->

    <!-- 录音按钮位置 - 大屏幕上移动到右上角偏下（稍微上移） -->
    <item name="record_button_position_percent" format="float" type="dimen">0.30</item>  <!-- 距离顶部30% -->

    <!-- 开场白左侧3个问题位置 - 大屏幕放在历史问题下面一点点 -->
    <item name="greeting_questions_top_percent" format="float" type="dimen">0.68</item>  <!-- 距离顶部68%，在历史问题下面 -->
    <item name="greeting_questions_left_percent" format="float" type="dimen">0.05</item>  <!-- 距离左侧5% -->
</resources>
