package ai.guiji.duix.test.ui.adapter

import ai.guiji.duix.test.databinding.ItemModelBinding
import ai.guiji.duix.test.model.ModelInfo
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide

class ModelAdapter(
    private val models: List<ModelInfo>,
    private val onModelClick: (ModelInfo) -> Unit,
    private val isModelDownloaded: (ModelInfo) -> Boolean
) : RecyclerView.Adapter<ModelAdapter.ModelViewHolder>() {

    companion object {
        private const val TAG_NET = "DUIX_NET"
    }

    private var selectedModel: ModelInfo? = null

    inner class ModelViewHolder(private val binding: ItemModelBinding) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(model: ModelInfo) {
            Log.d(TAG_NET, "绑定模型: ${model.name}, ID: ${model.id}")

            // 设置模型名称
            binding.tvModelName.text = model.name

            // 设置分类标签
            binding.tvCategory.text = model.getCategoryLabel()

            // 设置文件大小（转换为MB显示）
            binding.tvFileSize.text = model.getFileSizeMB()

            // 加载封面图片（使用thumbnail_url字段）
            // 注意：此处的模型已经通过is_show=true筛选，thumbnailUrl保证有值
            Log.d(TAG_NET, "加载模型封面: ${model.thumbnailUrl}, 方向: ${model.orientation}")

            // 根据模型方向动态调整图片显示参数
            val layoutParams = binding.ivThumbnail.layoutParams as androidx.constraintlayout.widget.ConstraintLayout.LayoutParams

            when (model.orientation) {
                "vertical" -> {
                    // 竖屏图片：使用3:4比例，适合竖屏图片
                    layoutParams.dimensionRatio = "H,3:4"
                    binding.ivThumbnail.scaleType = android.widget.ImageView.ScaleType.FIT_CENTER
                }
                "horizontal" -> {
                    // 横屏图片：使用更高的比例，让图片更大
                    layoutParams.dimensionRatio = "H,1:1"
                    binding.ivThumbnail.scaleType = android.widget.ImageView.ScaleType.CENTER_CROP
                }
                else -> {
                    // 默认使用3:4比例
                    layoutParams.dimensionRatio = "H,3:4"
                    binding.ivThumbnail.scaleType = android.widget.ImageView.ScaleType.FIT_CENTER
                }
            }
            binding.ivThumbnail.layoutParams = layoutParams

            // 根据缩放类型选择Glide加载方式
            val glideRequest = Glide.with(binding.root.context).load(model.thumbnailUrl)
            when (binding.ivThumbnail.scaleType) {
                android.widget.ImageView.ScaleType.CENTER_CROP -> glideRequest.centerCrop()
                else -> glideRequest.fitCenter()
            }.into(binding.ivThumbnail)

            // 设置下载状态指示器
            val isDownloaded = isModelDownloaded(model)
            binding.ivDownloadStatus.visibility = View.VISIBLE
            if (isDownloaded) {
                binding.ivDownloadStatus.setImageResource(ai.guiji.duix.test.R.drawable.ic_downloaded)
                binding.ivDownloadStatus.setBackgroundResource(ai.guiji.duix.test.R.drawable.bg_download_status_downloaded)
                binding.ivDownloadStatus.clearColorFilter()
            } else {
                binding.ivDownloadStatus.setImageResource(ai.guiji.duix.test.R.drawable.ic_download)
                binding.ivDownloadStatus.setBackgroundResource(ai.guiji.duix.test.R.drawable.bg_download_status_not_downloaded)
                binding.ivDownloadStatus.clearColorFilter()
            }

            // 设置选中状态
            val isSelected = selectedModel?.id == model.id
            binding.viewSelectedOverlay.visibility = if (isSelected) View.VISIBLE else View.GONE
            binding.ivSelectedIcon.visibility = if (isSelected) View.VISIBLE else View.GONE

            // 设置点击事件
            binding.root.setOnClickListener {
                Log.i(TAG_NET, "模型被点击: ${model.name}")
                onModelClick(model)
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ModelViewHolder {
        Log.d(TAG_NET, "创建模型ViewHolder")
        val binding = ItemModelBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ModelViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ModelViewHolder, position: Int) {
        Log.d(TAG_NET, "绑定模型位置: $position")
        holder.bind(models[position])
    }

    override fun getItemCount(): Int {
        Log.d(TAG_NET, "模型总数: ${models.size}")
        return models.size
    }

    /**
     * 设置选中的模型
     */
    fun setSelectedModel(model: ModelInfo) {
        val previousSelected = selectedModel
        selectedModel = model
        
        // 刷新之前选中的项和新选中的项
        if (previousSelected != null) {
            val previousIndex = models.indexOfFirst { it.id == previousSelected.id }
            if (previousIndex != -1) {
                notifyItemChanged(previousIndex)
            }
        }
        
        val newIndex = models.indexOfFirst { it.id == model.id }
        if (newIndex != -1) {
            notifyItemChanged(newIndex)
        }
    }
}
