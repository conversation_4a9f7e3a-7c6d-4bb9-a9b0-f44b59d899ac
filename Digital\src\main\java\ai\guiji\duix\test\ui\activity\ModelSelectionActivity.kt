package ai.guiji.duix.test.ui.activity

import ai.guiji.duix.test.R
import ai.guiji.duix.test.config.ApiConfig
import ai.guiji.duix.test.databinding.ActivityModelSelectionBinding
import ai.guiji.duix.test.model.ModelFilter
import ai.guiji.duix.test.model.ModelInfo
import ai.guiji.duix.test.ui.adapter.ModelAdapter
import ai.guiji.duix.test.service.StorageService
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.recyclerview.widget.GridLayoutManager
import java.io.File
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import okhttp3.Call
import okhttp3.Callback
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import java.io.IOException

class ModelSelectionActivity : BaseActivity() {

    companion object {
        const val PREF_NAME = "duix_settings"
        const val KEY_SELECTED_MODEL_URL = "selected_model_url"
        const val KEY_SELECTED_MODEL_CATEGORY = "selected_model_category"
        const val KEY_TTS_URL = "tts_url"
        const val DEFAULT_TTS_URL = ""
        private const val TAG_NET = "DUIX_NET"
    }

    private lateinit var binding: ActivityModelSelectionBinding
    private val sharedPrefs by lazy { getSharedPreferences(PREF_NAME, MODE_PRIVATE) }
    private val okHttpClient = OkHttpClient()
    private val objectMapper = ObjectMapper()
    
    private lateinit var modelAdapter: ModelAdapter
    private var allModels = mutableListOf<ModelInfo>()
    private var filteredModels = mutableListOf<ModelInfo>()
    private var currentFilter = ModelFilter.HORIZONTAL_MALE
    private var selectedModel: ModelInfo? = null

    // 下载相关
    private var isDownloading = false
    private lateinit var baseDir: File
    private lateinit var modelDir: File

    // 模型UUID（与SettingsActivity保持一致）
    private val modelUUID = "d39caddd-488b-4682-b6d1-13549b135dd1"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.i(TAG_NET, "ModelSelectionActivity onCreate 开始")

        try {
            binding = ActivityModelSelectionBinding.inflate(layoutInflater)
            setContentView(binding.root)
            Log.i(TAG_NET, "ModelSelectionActivity 布局设置完成")

            initDirectories()
            Log.i(TAG_NET, "ModelSelectionActivity initDirectories 完成")

            initViews()
            Log.i(TAG_NET, "ModelSelectionActivity initViews 完成")

            setupRecyclerView()
            Log.i(TAG_NET, "ModelSelectionActivity setupRecyclerView 完成")

            setupFilterChips()
            Log.i(TAG_NET, "ModelSelectionActivity setupFilterChips 完成")

            loadModels()
            Log.i(TAG_NET, "ModelSelectionActivity loadModels 开始")
        } catch (e: Exception) {
            Log.e(TAG_NET, "ModelSelectionActivity onCreate 失败: ${e.message}", e)
        }
    }

    private fun initViews() {
        // 刷新按钮
        binding.btnRefresh.setOnClickListener {
            Log.i(TAG_NET, "刷新按钮被点击")
            refreshModels()
        }

        // 进入数字人页面按钮
        binding.btnEnterDigitalHuman.setOnClickListener {
            if (selectedModel != null) {
                if (isDownloading) {
                    Toast.makeText(this, "模型正在下载中，请稍候...", Toast.LENGTH_SHORT).show()
                    return@setOnClickListener
                }

                // 检查模型是否已下载
                if (isModelDownloaded(selectedModel!!)) {
                    Log.i(TAG_NET, "模型已下载，检查API Key")

                    // 检查是否有API Key
                    val apiKey = sharedPrefs.getString(SettingsActivity.KEY_APIKEY, "") ?: ""
                    if (apiKey.isEmpty()) {
                        Log.w(TAG_NET, "API Key为空，提示用户先选择应用")
                        showApiKeyMissingDialog()
                        return@setOnClickListener
                    }

                    Log.i(TAG_NET, "API Key已配置，启动数字人")
                    saveSelectedModel()
                    startDigitalHumanActivity()
                } else {
                    Log.i(TAG_NET, "模型未下载，开始下载")
                    downloadModel(selectedModel!!)
                }
            } else {
                Toast.makeText(this, "请先选择一个模型", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun setupRecyclerView() {
        modelAdapter = ModelAdapter(filteredModels, { model ->
            selectModel(model)
        }, { model ->
            isModelDownloaded(model)
        })

        binding.rvModels.apply {
            layoutManager = GridLayoutManager(this@ModelSelectionActivity, 2)
            adapter = modelAdapter
        }
    }

    private fun setupFilterChips() {
        binding.chipHorizontalMale.setOnClickListener { applyFilter(ModelFilter.HORIZONTAL_MALE) }
        binding.chipHorizontalFemale.setOnClickListener { applyFilter(ModelFilter.HORIZONTAL_FEMALE) }
        binding.chipVerticalMale.setOnClickListener { applyFilter(ModelFilter.VERTICAL_MALE) }
        binding.chipVerticalFemale.setOnClickListener { applyFilter(ModelFilter.VERTICAL_FEMALE) }
    }

    private fun loadModels() {
        Log.i(TAG_NET, "loadModels 开始")
        val baseUrl = sharedPrefs.getString(KEY_TTS_URL, DEFAULT_TTS_URL) ?: DEFAULT_TTS_URL

        // 检查服务器地址是否配置
        if (baseUrl.isEmpty()) {
            Log.w(TAG_NET, "服务器地址未配置，仅显示内置模型")
            runOnUiThread {
                // 即使没有服务器配置，也显示内置模型
                allModels.clear()
                val builtInModel = createBuiltInModel()
                allModels.add(builtInModel)
                applyFilter(currentFilter)
                restoreSelectedModel()

                Toast.makeText(this@ModelSelectionActivity, "服务器地址未配置，显示内置模型。如需更多模型请先配置服务器地址", Toast.LENGTH_LONG).show()
            }
            return
        }

        val fullUrl = ApiConfig.buildUrl(baseUrl, ApiConfig.Model.MODELS)

        Log.i(TAG_NET, "模型API - 基础URL: $baseUrl")
        Log.i(TAG_NET, "模型API - 完整URL: $fullUrl")

        val request = Request.Builder()
            .url(fullUrl)
            .get()
            .build()

        Log.i(TAG_NET, "开始发送模型列表请求")
        okHttpClient.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e(TAG_NET, "模型列表请求失败: ${e.message}", e)
                runOnUiThread {
                    // 网络请求失败时，仍然显示内置模型
                    allModels.clear()
                    val builtInModel = createBuiltInModel()
                    allModels.add(builtInModel)
                    applyFilter(currentFilter)
                    restoreSelectedModel()

                    Toast.makeText(this@ModelSelectionActivity, "网络请求失败，显示内置模型。请检查服务器地址配置: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
            
            override fun onResponse(call: Call, response: Response) {
                Log.i(TAG_NET, "模型列表请求响应: ${response.code}")

                // 在后台线程中读取响应体
                try {
                    if (response.isSuccessful) {
                        val responseBody = response.body?.string()
                        Log.i(TAG_NET, "模型列表响应体长度: ${responseBody?.length ?: 0}")
                        Log.d(TAG_NET, "模型列表响应体内容: $responseBody")

                        if (responseBody != null) {
                            val models = objectMapper.readValue(responseBody, object : TypeReference<List<ModelInfo>>() {})
                            Log.i(TAG_NET, "解析到 ${models.size} 个模型")

                            // 只显示 is_show 为 true 的模型
                            // 对于 is_show=true 的模型，关键字段都保证有值
                            val visibleModels = models.filter { it.isShow }
                            Log.i(TAG_NET, "其中 ${visibleModels.size} 个模型可显示 (is_show=true)")

                            // 切换到主线程更新UI
                            runOnUiThread {
                                allModels.clear()

                                // 添加内置的"知心姐姐"基础模型
                                val builtInModel = createBuiltInModel()
                                allModels.add(builtInModel)

                                // 添加从服务器获取的模型
                                allModels.addAll(visibleModels)

                                Log.i(TAG_NET, "应用筛选器: $currentFilter")
                                applyFilter(currentFilter)

                                // 恢复之前选中的模型
                                restoreSelectedModel()

                                Toast.makeText(this@ModelSelectionActivity, "成功加载 ${allModels.size} 个模型（含1个内置模型）", Toast.LENGTH_SHORT).show()
                            }
                        } else {
                            Log.w(TAG_NET, "响应体为空")
                            runOnUiThread {
                                Toast.makeText(this@ModelSelectionActivity, "服务器返回空数据", Toast.LENGTH_SHORT).show()
                            }
                        }
                    } else {
                        Log.e(TAG_NET, "模型列表请求失败，响应码: ${response.code}")
                        runOnUiThread {
                            Toast.makeText(this@ModelSelectionActivity, "加载模型列表失败: ${response.code}，请检查服务器地址配置", Toast.LENGTH_SHORT).show()
                            // 响应失败时，跳转到设置界面
                            startSettingsActivity()
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG_NET, "解析模型列表失败: ${e.message}", e)
                    runOnUiThread {
                        Toast.makeText(this@ModelSelectionActivity, "解析模型列表失败: ${e.message}，请检查服务器地址配置", Toast.LENGTH_SHORT).show()
                        // 解析失败时，跳转到设置界面
                        startSettingsActivity()
                    }
                }
            }
        })
    }

    /**
     * 刷新模型列表
     */
    private fun refreshModels() {
        Log.i(TAG_NET, "========== 刷新模型列表 ==========")

        // 显示刷新提示
        Toast.makeText(this, "正在刷新模型列表...", Toast.LENGTH_SHORT).show()

        // 清空当前数据
        allModels.clear()
        filteredModels.clear()
        selectedModel = null

        // 先添加内置模型，确保始终可用
        val builtInModel = createBuiltInModel()
        allModels.add(builtInModel)

        modelAdapter.notifyDataSetChanged()
        updateCurrentModelDisplay()

        // 重新加载服务器模型
        loadModels()
    }

    /**
     * 创建内置的"知心姐姐"基础模型
     */
    private fun createBuiltInModel(): ModelInfo {
        return ModelInfo(
            id = -1, // 使用负数ID表示内置模型
            name = "知心姐姐",
            description = "内置基础模型，温暖贴心的AI助手",
            category = "female",
            orientation = "horizontal",
            localPath = null,
            url = "https://digital-public.obs.cn-east-3.myhuaweicloud.com/duix/digital/model/1719193451931/lengyan_20240407.zip", // 使用默认女性模型
            thumbnail = null,
            fileSize = 50 * 1024 * 1024L, // 约50MB
            isShow = true,
            createdAt = "2024-01-01T00:00:00Z",
            updatedAt = "2024-01-01T00:00:00Z",
            localUrl = null,
            thumbnailUrl = null,
            fileSizeFormatted = "50.0 MB"
        )
    }

    private fun applyFilter(filter: ModelFilter) {
        Log.i(TAG_NET, "应用筛选器: ${filter.label}")
        currentFilter = filter
        filteredModels.clear()
        val filtered = allModels.filter { it.matchesFilter(filter) }
        filteredModels.addAll(filtered)
        Log.i(TAG_NET, "筛选后模型数量: ${filteredModels.size}")
        modelAdapter.notifyDataSetChanged()
    }

    private fun selectModel(model: ModelInfo) {
        selectedModel = model
        modelAdapter.setSelectedModel(model)
        updateCurrentModelDisplay()
    }

    private fun saveSelectedModel() {
        selectedModel?.let { model ->
            sharedPrefs.edit()
                .putString(KEY_SELECTED_MODEL_URL, model.url)
                .putString(KEY_SELECTED_MODEL_CATEGORY, model.category)
                .apply()
        }
    }

    private fun restoreSelectedModel() {
        val savedModelUrl = sharedPrefs.getString(KEY_SELECTED_MODEL_URL, "") ?: ""
        if (savedModelUrl.isNotEmpty()) {
            val model = allModels.find { it.url == savedModelUrl }
            if (model != null) {
                selectModel(model)
            }
        }
    }

    /**
     * 更新当前选中模型的显示
     */
    private fun updateCurrentModelDisplay() {
        if (selectedModel != null) {
            val model = selectedModel!!
            val displayText = "(当前模型：${model.getCategoryLabel()} ${model.name})"
            binding.tvCurrentModel.text = displayText
            binding.tvCurrentModel.visibility = android.view.View.VISIBLE
            Log.i(TAG_NET, "更新当前模型显示: $displayText")
        } else {
            binding.tvCurrentModel.text = "(当前模型：未选择)"
            binding.tvCurrentModel.visibility = android.view.View.GONE
            Log.i(TAG_NET, "清除当前模型显示")
        }
    }

    private fun startDigitalHumanActivity() {
        // 检查是否有API Key
        val apiKey = sharedPrefs.getString(SettingsActivity.KEY_APIKEY, "") ?: ""
        if (apiKey.isEmpty()) {
            Log.w(TAG_NET, "API Key为空，提示用户先选择应用")
            showApiKeyMissingDialog()
            return
        }

        val intent = Intent(this, CallActivity::class.java)
        // 传递选中的模型信息给数字人界面
        selectedModel?.let { model ->
            // 确保modelDir是正确的
            val modelFileName = model.url?.substring(model.url.lastIndexOf("/") + 1)?.replace(".zip", "") ?: ""
            val duixDir = getExternalFilesDir("duix")
            modelDir = File(duixDir, modelFileName) // 修复：模型目录应该在duixDir下，不是baseDir下

            Log.i(TAG_NET, "准备启动CallActivity:")
            Log.i(TAG_NET, "baseDir: ${baseDir.absolutePath}")
            Log.i(TAG_NET, "modelDir: ${modelDir.absolutePath}")
            Log.i(TAG_NET, "modelUrl: ${model.url}")
            Log.i(TAG_NET, "modelCategory: ${model.category}")
            Log.i(TAG_NET, "apiKey: $apiKey")

            intent.putExtra("baseDir", baseDir.absolutePath)
            intent.putExtra("modelDir", modelDir.absolutePath)
            intent.putExtra("selectedModelUrl", model.url)  // 传递url字段
            intent.putExtra("selectedModelCategory", model.category)
            intent.putExtra("apiKey", apiKey)  // 传递API Key
            intent.putExtra("forceReload", true) // 强制重新加载以清理重影
        }
        startActivity(intent)
        finish()
    }

    /**
     * 初始化目录
     */
    private fun initDirectories() {
        val duixDir = getExternalFilesDir("duix")
        // 修复：与其他Activity保持一致，使用gj_dh_res作为baseDir
        val baseConfigUrl = "https://cdn.guiji.ai/duix/location/gj_dh_res.zip"
        baseDir = File(duixDir, baseConfigUrl.substring(baseConfigUrl.lastIndexOf("/") + 1).replace(".zip", ""))
        if (!baseDir.exists()) {
            baseDir.mkdirs()
        }
        Log.i(TAG_NET, "基础目录: ${baseDir.absolutePath}")
    }

    /**
     * 检查模型是否已下载（检查uuid文件，与SettingsActivity保持一致）
     */
    private fun isModelDownloaded(model: ModelInfo): Boolean {
        val modelFileName = model.url?.substring(model.url.lastIndexOf("/") + 1)?.replace(".zip", "") ?: return false
        val duixDir = getExternalFilesDir("duix")
        modelDir = File(duixDir, modelFileName) // 修复：模型目录应该在duixDir下，不是baseDir下

        // 检查目录是否存在
        if (!modelDir.exists() || !modelDir.isDirectory) {
            Log.i(TAG_NET, "检查模型 ${model.name}: 目录不存在, 路径: ${modelDir.absolutePath}")
            return false
        }

        // 检查uuid文件是否存在且内容正确
        val uuidFile = File(modelDir, "uuid")
        if (!uuidFile.exists()) {
            Log.i(TAG_NET, "检查模型 ${model.name}: uuid文件不存在")
            return false
        }

        try {
            val fileUuid = uuidFile.readText().trim()
            val isValid = fileUuid == modelUUID
            Log.i(TAG_NET, "检查模型 ${model.name}: uuid匹配=$isValid (文件:$fileUuid, 期望:$modelUUID)")
            return isValid
        } catch (e: Exception) {
            Log.e(TAG_NET, "检查模型 ${model.name}: 读取uuid文件失败: ${e.message}")
            return false
        }
    }

    /**
     * 下载模型（使用StorageService，与SettingsActivity保持一致）
     */
    private fun downloadModel(model: ModelInfo) {
        if (model.url.isNullOrEmpty()) {
            Toast.makeText(this, "模型下载地址无效", Toast.LENGTH_SHORT).show()
            return
        }

        isDownloading = true
        binding.btnEnterDigitalHuman.text = "准备下载..."
        binding.btnEnterDigitalHuman.isEnabled = false

        Log.i(TAG_NET, "开始下载模型: ${model.name}, URL: ${model.url}")
        Toast.makeText(this, "开始下载模型: ${model.name}", Toast.LENGTH_SHORT).show()

        // 计算模型目录路径
        val modelFileName = model.url.substring(model.url.lastIndexOf("/") + 1).replace(".zip", "")
        val duixDir = getExternalFilesDir("duix")
        modelDir = File(duixDir, modelFileName) // 修复：模型目录应该在duixDir下，不是baseDir下

        Log.i(TAG_NET, "模型将下载到: ${modelDir.absolutePath}")

        StorageService.downloadAndUnzip(this, model.url, modelDir.absolutePath, modelUUID, object : StorageService.Callback {
            override fun onDownloadProgress(progress: Int) {
                runOnUiThread {
                    binding.btnEnterDigitalHuman.text = "下载中... $progress%"
                    Log.d(TAG_NET, "下载进度: $progress%")
                }
            }

            override fun onUnzipProgress(progress: Int) {
                runOnUiThread {
                    binding.btnEnterDigitalHuman.text = "解压中... $progress%"
                    Log.d(TAG_NET, "解压进度: $progress%")
                }
            }

            override fun onComplete(path: String?) {
                runOnUiThread {
                    isDownloading = false
                    binding.btnEnterDigitalHuman.text = getString(R.string.btn_enter_digital_human)
                    binding.btnEnterDigitalHuman.isEnabled = true

                    Log.i(TAG_NET, "模型下载解压完成: ${model.name}, 路径: $path")
                    Toast.makeText(this@ModelSelectionActivity, "模型下载完成！", Toast.LENGTH_SHORT).show()

                    // 刷新适配器以更新下载状态指示器
                    modelAdapter.notifyDataSetChanged()

                    // 检查API Key后再决定是否自动进入数字人界面
                    val apiKey = sharedPrefs.getString(SettingsActivity.KEY_APIKEY, "") ?: ""
                    if (apiKey.isEmpty()) {
                        Log.w(TAG_NET, "模型下载完成但API Key为空，提示用户选择应用")
                        showApiKeyMissingDialog()
                    } else {
                        Log.i(TAG_NET, "模型下载完成且API Key已配置，自动启动数字人")
                        saveSelectedModel()
                        startDigitalHumanActivity()
                    }
                }
            }

            override fun onError(msg: String?) {
                Log.e(TAG_NET, "模型下载失败: $msg")
                runOnUiThread {
                    isDownloading = false
                    binding.btnEnterDigitalHuman.text = getString(R.string.btn_enter_digital_human)
                    binding.btnEnterDigitalHuman.isEnabled = true
                    Toast.makeText(this@ModelSelectionActivity, "模型下载失败: $msg", Toast.LENGTH_SHORT).show()
                }
            }
        }, true) // deleteZip = true，下载完成后删除zip文件
    }

    /**
     * 跳转到设置界面
     */
    private fun startSettingsActivity() {
        val intent = Intent(this, SettingsActivity::class.java)
        startActivity(intent)
        finish()
    }

    /**
     * 显示API Key缺失的对话框
     */
    private fun showApiKeyMissingDialog() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("缺少应用配置")
            .setMessage("模型已下载完成，但还需要选择应用才能使用数字人功能。是否返回设置界面选择应用？")
            .setPositiveButton("去设置界面") { _, _ ->
                startSettingsActivity()
            }
            .setNegativeButton("稍后设置") { _, _ ->
                // 用户选择稍后设置，关闭当前界面
                finish()
            }
            .setCancelable(false)  // 不允许点击外部取消
            .show()
    }

}
