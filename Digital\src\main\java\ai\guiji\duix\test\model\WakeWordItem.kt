package ai.guiji.duix.test.model

import com.google.gson.annotations.SerializedName

/**
 * 唤醒词数据模型
 */
data class WakeWordItem(
    @SerializedName("id")
    val id: Int,

    @SerializedName("word")
    val word: String,

    @SerializedName("description")
    val description: String,

    @SerializedName("created_at")
    val createdAt: String,

    @SerializedName("updated_at")
    val updatedAt: String,

    @SerializedName("pinyin_format")
    val pinyinFormat: String? = null
)
