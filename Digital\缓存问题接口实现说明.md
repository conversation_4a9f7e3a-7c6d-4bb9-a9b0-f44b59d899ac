# 缓存问题接口实现说明

## 📋 功能概述

新增了 `/v2/get-cached-questions` 接口的完整实现，用于获取用户之前提问过的缓存问题，并与现有的本地历史问题功能进行集成。

## 🔧 实现内容

### 1. 数据模型 (SseResponse.kt)

```kotlin
/**
 * 缓存问题响应数据模型
 * 用于 /v2/get-cached-questions 接口的响应解析
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class CachedQuestionsResponse(
    @JsonProperty("success")
    val success: <PERSON><PERSON>an,
    
    @JsonProperty("questions")
    val questions: List<String>,
    
    @JsonProperty("count")
    val count: Int,
    
    @JsonProperty("cache_key")
    val cacheKey: String
)
```

### 2. API配置 (ApiConfig.kt)

```kotlin
object LLM {
    const val LLM_STREAMING_ENDPOINT = "/v2/llm-streaming"
    const val GET_CACHED_QUESTIONS_ENDPOINT = "/v2/get-cached-questions"  // 新增
}
```

### 3. 核心实现 (CallActivity.kt)

#### 新增方法：
- `getCachedQuestions()`: 调用缓存问题接口
- `updateHistoryQuestionsDisplay()`: 更新历史问题UI显示

#### 请求参数：
- **dify_api_key**: 从设置界面选择的应用获取
- **reference_id**: 根据模型类别动态设置 (male→man, female→woman)
- **user_id**: 从设置界面用户输入获取

#### 响应处理：
- 解析JSON响应数据
- 清空当前本地历史问题
- 将缓存问题添加到本地历史记录
- 更新UI显示历史问题标签云

## 🚀 调用时机

### 1. 应用启动时
在 `initOk()` 方法中，数字人初始化完成后自动调用：
```kotlin
// 数字人初始化完成后，调用parameters接口获取开场白和推荐问题
fetchParameters()

// 获取缓存的历史问题
getCachedQuestions()
```

### 2. 重置按钮点击时
在 `resetToWelcomeState()` 方法中，重置界面时调用：
```kotlin
// 10. 重新请求开场白
fetchParameters()

// 11. 获取缓存的历史问题
getCachedQuestions()
```

## 📊 与现有功能的集成

### 本地历史问题机制
- **存储方式**: `LinkedHashSet<String>` 内存存储
- **容量限制**: 最多15个问题
- **去重机制**: 自动避免重复问题
- **UI显示**: 3行滚动标签云

### 缓存问题集成
- **数据来源**: 服务器缓存的历史问题
- **加载时机**: 应用启动和重置时
- **合并策略**: 清空本地问题，加载缓存问题
- **UI更新**: 复用现有的历史问题显示逻辑

## 🔄 工作流程

1. **应用启动** → 数字人初始化 → 获取开场白 + 获取缓存问题
2. **缓存问题加载** → 清空本地历史 → 添加缓存问题 → 更新UI显示
3. **用户交互** → 新问题添加到本地历史 → 显示在标签云中
4. **重置操作** → 重新获取开场白 + 重新获取缓存问题

## 📝 API请求示例

```bash
curl --location --request GET 'http://192.168.2.89:8080/v2/get-cached-questions' \
--header 'dify_api_key: app-i5ss1nLXXnFKVPkL7WANzE0Y' \
--header 'reference_id: woman' \
--header 'user_id: demo'
```

## 📋 响应格式

```json
{
    "success": true,
    "questions": [
        "晚上好",
        "你好啊", 
        "你好"
    ],
    "count": 3,
    "cache_key": "question:app-i5ss1nLXXnFKVPkL7WANzE0Y:demo:woman"
}
```

## ✅ 实现特点

1. **完整的错误处理**: 网络请求失败、解析错误、空数据等情况
2. **详细的日志记录**: 便于调试和问题排查
3. **用户友好提示**: Toast消息提示操作结果
4. **无缝集成**: 复用现有的UI组件和显示逻辑
5. **参数自动获取**: 从SharedPreferences自动读取必需参数

## 🎯 使用效果

- 用户重新打开应用时，可以看到之前提问过的问题
- 点击历史问题标签可以重新提问
- 支持跨设备/会话的问题历史同步
- 提供更好的用户体验和连续性
