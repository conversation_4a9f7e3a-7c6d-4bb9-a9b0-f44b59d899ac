plugins {
    id 'com.android.library'
}

android {
    namespace 'ai.guiji.duix.sdk.client'
    compileSdk 33

    defaultConfig {
        minSdk 24
        versionCode 4
        versionName '3.0.3'

        // 暂时注释CMake配置以解决构建问题
        // externalNativeBuild {
        //     cmake {
        //         abiFilters 'arm64-v8a'  // 移除armeabi-v7a，只支持arm64-v8a架构
        //         cppFlags "-std=c++17", "-fexceptions"
        //         // 添加16KB页面大小支持
        //         arguments "-DANDROID_STL=c++_shared", "-DANDROID_TOOLCHAIN=clang"
        //         // 添加链接器标志支持16KB页面大小
        //         arguments "-DCMAKE_SHARED_LINKER_FLAGS=-Wl,-z,max-page-size=16384"
        //         arguments "-DCMAKE_EXE_LINKER_FLAGS=-Wl,-z,max-page-size=16384"
        //     }
        // }
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            buildConfigField("String", "VERSION_NAME", "\"${defaultConfig.versionName}\"")
            buildConfigField('int', 'VERSION_CODE', "${defaultConfig.versionCode}")
        }

        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            buildConfigField("String", "VERSION_NAME", "\"${defaultConfig.versionName}\"")
            buildConfigField('int', 'VERSION_CODE', "${defaultConfig.versionCode}")

            android.libraryVariants.all { variant ->
                variant.outputs.all {
                    outputFileName = "duix_client_sdk_${buildType.name}_${defaultConfig.versionName}.aar"
                }
            }
        }
    }

    // CMake配置暂时注释以解决构建问题
    // externalNativeBuild {
    //     cmake {
    //         path "src/main/cpp/CMakeLists.txt"
    //         version "3.22.1"  // 使用更新的CMake版本
    //     }
    // }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
//    kotlinOptions {
//        jvmTarget = '1.8'
//    }
}

dependencies {

    implementation 'com.google.android.exoplayer:exoplayer:2.14.2'
    implementation "org.java-websocket:Java-WebSocket:1.5.1"
    implementation 'com.squareup.okhttp3:okhttp-sse:4.10.0'
}