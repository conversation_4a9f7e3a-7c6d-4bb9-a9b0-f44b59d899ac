package ai.guiji.duix.test.ui.activity

import ai.guiji.duix.test.R
import ai.guiji.duix.test.databinding.ActivityVideoPlayerBinding
import android.content.Context
import android.content.Intent
import android.media.MediaPlayer
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.widget.SeekBar
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity

/**
 * 视频播放器Activity
 * 功能：
 * 1. 支持视频播放控制（播放、暂停、进度控制）
 * 2. 提供播放进度显示和拖拽控制
 * 3. 自动隐藏控制栏
 */
class VideoPlayerActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "VideoPlayerActivity"
        private const val EXTRA_VIDEO_URL = "extra_video_url"
        private const val EXTRA_VIDEO_TITLE = "extra_video_title"
        private const val CONTROLS_HIDE_DELAY = 3000L // 3秒后自动隐藏控制栏

        /**
         * 启动视频播放器
         */
        fun start(context: Context, videoUrl: String, videoTitle: String = "视频播放") {
            val intent = Intent(context, VideoPlayerActivity::class.java).apply {
                putExtra(EXTRA_VIDEO_URL, videoUrl)
                putExtra(EXTRA_VIDEO_TITLE, videoTitle)
            }
            context.startActivity(intent)
        }

        /**
         * 创建视频播放器Intent
         */
        fun createIntent(context: Context, videoUrl: String, videoTitle: String = "视频播放"): Intent {
            return Intent(context, VideoPlayerActivity::class.java).apply {
                putExtra(EXTRA_VIDEO_URL, videoUrl)
                putExtra(EXTRA_VIDEO_TITLE, videoTitle)
            }
        }
    }

    private lateinit var binding: ActivityVideoPlayerBinding
    private var videoUrl: String = ""
    private var videoTitle: String = ""
    private var isPlaying = false
    private var currentPosition = 0
    private var videoDuration = 0

    private val handler = Handler(Looper.getMainLooper())
    private val hideControlsRunnable = Runnable { hideControls() }
    private val updateProgressRunnable = object : Runnable {
        override fun run() {
            updateProgress()
            handler.postDelayed(this, 1000)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.i(TAG, "========== VideoPlayerActivity onCreate ==========")

        binding = ActivityVideoPlayerBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 获取传递的参数
        videoUrl = intent.getStringExtra(EXTRA_VIDEO_URL) ?: ""
        videoTitle = intent.getStringExtra(EXTRA_VIDEO_TITLE) ?: "视频播放"

        Log.i(TAG, "视频URL: $videoUrl")
        Log.i(TAG, "视频标题: $videoTitle")

        if (videoUrl.isEmpty()) {
            Log.e(TAG, "视频URL为空，无法播放")
            showError("视频地址无效")
            return
        }

        initViews()
        setupVideoPlayer()
    }

    private fun initViews() {
        // 设置标题
        binding.tvTitle.text = videoTitle

        // 返回按钮点击事件
        binding.ivBack.setOnClickListener {
            Log.i(TAG, "返回按钮被点击")
            finish()
        }

        // 隐藏全屏按钮
        binding.ivFullscreen.visibility = View.GONE

        // 播放/暂停按钮点击事件
        binding.ivPlayPause.setOnClickListener {
            Log.i(TAG, "播放/暂停按钮被点击")
            togglePlayPause()
        }

        // 中央播放按钮点击事件
        binding.ivCenterPlay.setOnClickListener {
            Log.i(TAG, "中央播放按钮被点击")
            startPlayback()
        }

        // 隐藏音量按钮
        binding.ivVolume.visibility = View.GONE

        // 重试按钮点击事件
        binding.btnRetry.setOnClickListener {
            Log.i(TAG, "重试按钮被点击")
            setupVideoPlayer()
        }

        // 进度条拖拽事件
        binding.seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val position = (progress * videoDuration / 100)
                    binding.videoView.seekTo(position)
                    binding.tvCurrentTime.text = formatTime(position)
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                handler.removeCallbacks(updateProgressRunnable)
            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                handler.post(updateProgressRunnable)
            }
        })

        // 视频区域点击事件（显示/隐藏控制栏）
        binding.videoView.setOnClickListener {
            toggleControlsVisibility()
        }

        Log.i(TAG, "✓ 视图初始化完成")
    }

    private fun setupVideoPlayer() {
        Log.i(TAG, "开始设置视频播放器: $videoUrl")

        // 显示加载状态
        binding.pbLoading.visibility = View.VISIBLE
        binding.llError.visibility = View.GONE
        binding.ivCenterPlay.visibility = View.GONE

        try {
            // 设置视频URI
            val uri = Uri.parse(videoUrl)
            binding.videoView.setVideoURI(uri)

            // 设置准备完成监听器
            binding.videoView.setOnPreparedListener { mediaPlayer ->
                Log.i(TAG, "视频准备完成")
                videoDuration = mediaPlayer.duration
                binding.tvTotalTime.text = formatTime(videoDuration)
                
                binding.pbLoading.visibility = View.GONE
                binding.ivCenterPlay.visibility = View.VISIBLE
                
                // 设置循环播放
                mediaPlayer.isLooping = false
                
                Log.i(TAG, "✓ 视频设置完成，时长: ${formatTime(videoDuration)}")
            }

            // 设置播放完成监听器
            binding.videoView.setOnCompletionListener {
                Log.i(TAG, "视频播放完成")
                isPlaying = false
                binding.ivPlayPause.setImageResource(android.R.drawable.ic_media_play)
                binding.ivCenterPlay.visibility = View.VISIBLE
                showControls()
            }

            // 设置错误监听器
            binding.videoView.setOnErrorListener { _, what, extra ->
                Log.e(TAG, "视频播放错误: what=$what, extra=$extra")
                showError("视频播放失败")
                true
            }

        } catch (e: Exception) {
            Log.e(TAG, "设置视频播放器失败: ${e.message}", e)
            showError("视频设置失败: ${e.message}")
        }
    }

    private fun startPlayback() {
        Log.i(TAG, "开始播放视频")
        binding.videoView.start()
        isPlaying = true
        binding.ivPlayPause.setImageResource(android.R.drawable.ic_media_pause)
        binding.ivCenterPlay.visibility = View.GONE
        
        // 开始更新进度
        handler.post(updateProgressRunnable)
        
        // 自动隐藏控制栏
        scheduleHideControls()
    }

    private fun togglePlayPause() {
        if (isPlaying) {
            binding.videoView.pause()
            isPlaying = false
            binding.ivPlayPause.setImageResource(android.R.drawable.ic_media_play)
            handler.removeCallbacks(updateProgressRunnable)
            Log.i(TAG, "视频已暂停")
        } else {
            binding.videoView.start()
            isPlaying = true
            binding.ivPlayPause.setImageResource(android.R.drawable.ic_media_pause)
            handler.post(updateProgressRunnable)
            scheduleHideControls()
            Log.i(TAG, "视频已播放")
        }
    }

    // 全屏功能已移除

    private fun toggleControlsVisibility() {
        if (binding.llControls.visibility == View.VISIBLE) {
            hideControls()
        } else {
            showControls()
        }
    }

    private fun showControls() {
        binding.llControls.visibility = View.VISIBLE
        binding.llToolbar.visibility = View.VISIBLE
        scheduleHideControls()
    }

    private fun hideControls() {
        binding.llControls.visibility = View.GONE
        handler.removeCallbacks(hideControlsRunnable)
    }

    private fun scheduleHideControls() {
        handler.removeCallbacks(hideControlsRunnable)
        if (isPlaying) {
            handler.postDelayed(hideControlsRunnable, CONTROLS_HIDE_DELAY)
        }
    }

    private fun updateProgress() {
        if (binding.videoView.isPlaying) {
            currentPosition = binding.videoView.currentPosition
            val progress = if (videoDuration > 0) (currentPosition * 100 / videoDuration) else 0
            binding.seekBar.progress = progress
            binding.tvCurrentTime.text = formatTime(currentPosition)
        }
    }

    private fun formatTime(milliseconds: Int): String {
        val seconds = milliseconds / 1000
        val minutes = seconds / 60
        val remainingSeconds = seconds % 60
        return String.format("%02d:%02d", minutes, remainingSeconds)
    }

    private fun showError(message: String) {
        binding.pbLoading.visibility = View.GONE
        binding.ivCenterPlay.visibility = View.GONE
        binding.llError.visibility = View.VISIBLE
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
        Log.e(TAG, "显示错误: $message")
    }

    override fun onPause() {
        super.onPause()
        if (isPlaying) {
            binding.videoView.pause()
        }
        handler.removeCallbacks(updateProgressRunnable)
        handler.removeCallbacks(hideControlsRunnable)
    }

    override fun onResume() {
        super.onResume()
        if (isPlaying) {
            binding.videoView.start()
            handler.post(updateProgressRunnable)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        handler.removeCallbacks(updateProgressRunnable)
        handler.removeCallbacks(hideControlsRunnable)
        Log.i(TAG, "VideoPlayerActivity onDestroy")
    }
}
