/**
 * 背景选择适配器
 * 主要功能：展示可用的背景图片，支持横屏/竖屏筛选和选择功能
 */
package ai.guiji.duix.test.ui.adapter

import ai.guiji.duix.test.databinding.ItemBackgroundBinding
import ai.guiji.duix.test.model.MediaItem
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide

class BackgroundAdapter(
    private val mediaItems: List<MediaItem>,
    private val onItemClick: (MediaItem) -> Unit
) : RecyclerView.Adapter<BackgroundAdapter.BackgroundViewHolder>() {

    companion object {
        private const val TAG_NET = "DUIX_NET"
    }

    private var selectedItem: MediaItem? = null

    inner class BackgroundViewHolder(private val binding: ItemBackgroundBinding) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(mediaItem: MediaItem) {
            Log.d(TAG_NET, "绑定背景: ${mediaItem.name}, ID: ${mediaItem.id}")

            // 加载背景图片
            Log.d(TAG_NET, "加载背景图片: ${mediaItem.url}, 方向: ${mediaItem.orientation}")

            // 根据媒体方向动态调整图片显示参数
            val layoutParams = binding.ivBackground.layoutParams as androidx.constraintlayout.widget.ConstraintLayout.LayoutParams

            when (mediaItem.orientation) {
                "vertical" -> {
                    // 竖屏图片：使用3:4比例，适合竖屏图片
                    layoutParams.dimensionRatio = "H,3:4"
                    binding.ivBackground.scaleType = android.widget.ImageView.ScaleType.FIT_CENTER
                }
                "horizontal" -> {
                    // 横屏图片：使用更高的比例，让图片更大
                    layoutParams.dimensionRatio = "H,1:1"
                    binding.ivBackground.scaleType = android.widget.ImageView.ScaleType.CENTER_CROP
                }
                else -> {
                    // 默认使用3:4比例
                    layoutParams.dimensionRatio = "H,3:4"
                    binding.ivBackground.scaleType = android.widget.ImageView.ScaleType.FIT_CENTER
                }
            }
            binding.ivBackground.layoutParams = layoutParams

            // 根据缩放类型选择Glide加载方式
            val glideRequest = Glide.with(binding.root.context).load(mediaItem.url)
            when (binding.ivBackground.scaleType) {
                android.widget.ImageView.ScaleType.CENTER_CROP -> glideRequest.centerCrop()
                else -> glideRequest.fitCenter()
            }.into(binding.ivBackground)

            // 设置选中状态
            val isSelected = selectedItem?.id == mediaItem.id
            binding.viewSelectedOverlay.visibility = if (isSelected) View.VISIBLE else View.GONE
            binding.ivSelectedIcon.visibility = if (isSelected) View.VISIBLE else View.GONE

            // 设置点击事件
            binding.root.setOnClickListener {
                Log.i(TAG_NET, "背景被点击: ${mediaItem.name}")
                onItemClick(mediaItem)
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BackgroundViewHolder {
        Log.d(TAG_NET, "创建背景ViewHolder")
        val binding = ItemBackgroundBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return BackgroundViewHolder(binding)
    }

    override fun onBindViewHolder(holder: BackgroundViewHolder, position: Int) {
        Log.d(TAG_NET, "绑定背景位置: $position")
        holder.bind(mediaItems[position])
    }

    override fun getItemCount(): Int {
        Log.d(TAG_NET, "背景总数: ${mediaItems.size}")
        return mediaItems.size
    }

    /**
     * 设置选中的背景
     */
    fun setSelectedItem(mediaItem: MediaItem) {
        val previousSelected = selectedItem
        selectedItem = mediaItem
        
        // 刷新之前选中的项和新选中的项
        if (previousSelected != null) {
            val previousIndex = mediaItems.indexOfFirst { it.id == previousSelected.id }
            if (previousIndex != -1) {
                notifyItemChanged(previousIndex)
            }
        }
        
        val newIndex = mediaItems.indexOfFirst { it.id == mediaItem.id }
        if (newIndex != -1) {
            notifyItemChanged(newIndex)
        }
    }
}
