package ai.guiji.duix.test.model

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 模型信息数据模型
 */
data class ModelInfo(
    @JsonProperty("id")
    val id: Int,

    @JsonProperty("name")
    val name: String,

    @JsonProperty("description")
    val description: String?,

    @JsonProperty("category")
    val category: String?, // "male" 或 "female"，可能为null

    @JsonProperty("orientation")
    val orientation: String?, // "horizontal" 或 "vertical"，对于is_show=true的模型不为null

    @JsonProperty("local_path")
    val localPath: String?,

    @JsonProperty("url")
    val url: String?, // 模型下载地址，可能为null

    @JsonProperty("thumbnail")
    val thumbnail: String?,

    @JsonProperty("file_size")
    val fileSize: Long?, // 文件大小（字节），可能为null

    @JsonProperty("is_show")
    val isShow: Bo<PERSON>an,

    @JsonProperty("created_at")
    val createdAt: String,

    @JsonProperty("updated_at")
    val updatedAt: String,

    @JsonProperty("local_url")
    val localUrl: String?,

    @JsonProperty("thumbnail_url")
    val thumbnailUrl: String?, // 封面图片地址，可能为null

    @JsonProperty("file_size_formatted")
    val fileSizeFormatted: String?
) {
    /**
     * 获取格式化的文件大小（MB）
     */
    fun getFileSizeMB(): String {
        return if (fileSize != null) {
            val sizeInMB = fileSize / (1024.0 * 1024.0)
            String.format("%.1f MB", sizeInMB)
        } else {
            "未知大小"
        }
    }

    /**
     * 获取模型分类标签
     * 注意：此方法主要用于is_show=true的模型，此时orientation和category不应为null
     */
    fun getCategoryLabel(): String {
        val genderLabel = when (category) {
            "male" -> "男"
            "female" -> "女"
            else -> "未知"
        }
        val orientationLabel = when (orientation) {
            "horizontal" -> "横屏"
            "vertical" -> "竖屏"
            else -> "未知"
        }
        return "$orientationLabel-$genderLabel"
    }

    /**
     * 检查是否匹配筛选条件
     */
    fun matchesFilter(filter: ModelFilter): Boolean {
        return when (filter) {
            ModelFilter.HORIZONTAL_MALE -> orientation == "horizontal" && category == "male"
            ModelFilter.HORIZONTAL_FEMALE -> orientation == "horizontal" && category == "female"
            ModelFilter.VERTICAL_MALE -> orientation == "vertical" && category == "male"
            ModelFilter.VERTICAL_FEMALE -> orientation == "vertical" && category == "female"
        }
    }
}

/**
 * 模型筛选枚举
 */
enum class ModelFilter(val label: String) {
    HORIZONTAL_MALE("横屏-男"),
    HORIZONTAL_FEMALE("横屏-女"),
    VERTICAL_MALE("竖屏-男"),
    VERTICAL_FEMALE("竖屏-女")
}

/**
 * 媒体项数据模型（用于背景选择）
 */
data class MediaItem(
    @JsonProperty("id")
    val id: Int,

    @JsonProperty("name")
    val name: String,

    @JsonProperty("file_path")
    val filePath: String,

    @JsonProperty("file_hash")
    val fileHash: String,

    @JsonProperty("description")
    val description: String?,

    @JsonProperty("media_type")
    val mediaType: String, // "image" 或 "video"

    @JsonProperty("orientation")
    val orientation: String?, // "horizontal" 或 "vertical"，可能为null

    @JsonProperty("is_show")
    val isShow: Boolean,

    @JsonProperty("is_delete")
    val isDelete: Boolean,

    @JsonProperty("created_at")
    val createdAt: String,

    @JsonProperty("updated_at")
    val updatedAt: String,

    @JsonProperty("url")
    val url: String // 完整的媒体URL
) {
    /**
     * 检查是否匹配筛选条件（只处理图片类型的媒体）
     */
    fun matchesFilter(filter: MediaFilter): Boolean {
        // 只处理图片类型且is_show为true的媒体
        if (mediaType != "image" || !isShow) {
            return false
        }

        return when (filter) {
            MediaFilter.HORIZONTAL -> orientation == "horizontal"
            MediaFilter.VERTICAL -> orientation == "vertical"
        }
    }
}

/**
 * 媒体筛选枚举（用于背景选择）
 */
enum class MediaFilter(val label: String) {
    HORIZONTAL("横屏"),
    VERTICAL("竖屏")
}

/**
 * 模型API响应数据模型
 */
data class ModelResponse(
    @JsonProperty("code")
    val code: Int,

    @JsonProperty("message")
    val message: String,

    @JsonProperty("data")
    val data: List<ModelInfo>
)