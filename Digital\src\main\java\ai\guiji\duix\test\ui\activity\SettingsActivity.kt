package ai.guiji.duix.test.ui.activity

import ai.guiji.duix.sdk.client.BuildConfig
import ai.guiji.duix.test.R
import ai.guiji.duix.test.config.ApiConfig
import ai.guiji.duix.test.databinding.ActivitySettingsBinding
import ai.guiji.duix.test.model.AppInfo
import ai.guiji.duix.test.model.DeviceResponse
import ai.guiji.duix.test.model.LogoItem
import ai.guiji.duix.test.service.StorageService
import android.Manifest
import android.annotation.SuppressLint
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.text.Editable
import android.text.TextWatcher
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.bumptech.glide.Glide
import com.fasterxml.jackson.databind.ObjectMapper
import okhttp3.Call
import okhttp3.Callback
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import java.io.BufferedReader
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.io.InputStreamReader


class SettingsActivity : BaseActivity() {

    companion object {
        const val PERMISSION_REQUEST_CODE = 1001
        const val PREF_NAME = "duix_settings"
        const val KEY_TTS_URL = "tts_url"
        const val KEY_APIKEY = "apikey"
        const val KEY_USER_ID = "user_id"
        const val KEY_SELECTED_MODEL_URL = "selected_model_url"
        const val KEY_SELECTED_MODEL_CATEGORY = "selected_model_category"
        const val DEFAULT_TTS_URL = ""
        const val DEFAULT_USER_ID = "admin-web"
        const val REFERENCE_ID = "reference_id"
        const val KEY_ENABLE_LOCAL_ASR = "enable_local_asr"  // 新增：是否启用本地ASR
        const val KEY_TTS_SPEED = "tts_speed"  // 新增：TTS语速设置
        const val DEFAULT_TTS_SPEED = 1.0f  // 默认语速
        const val KEY_LANGUAGE = "language"  // 新增：回复语言设置
        const val DEFAULT_LANGUAGE = "zh"  // 默认中文
        const val KEY_WAKE_WORD = "wake_word"  // 新增：唤醒词设置
        const val KEY_WAKEUP_REPLY = "wakeup_reply"  // 新增：唤醒词回复设置
        const val KEY_WAKEUP_DELAY = "wakeup_delay"  // 新增：唤醒词回复等待时间（毫秒）
        const val DEFAULT_WAKE_WORD = "小灵小灵"  // 默认唤醒词
        const val KEY_SELECTED_LOGO_URL = "selected_logo_url"  // 新增：选中的Logo URL
        const val KEY_SELECTED_LOGO_NAME = "selected_logo_name"  // 新增：选中的Logo名称
        const val KEY_RETURN_TO_GREETING_TIME = "return_to_greeting_time"  // 新增：返回开场白时间（秒）
        const val DEFAULT_RETURN_TO_GREETING_TIME = 30  // 默认30秒
        const val KEY_MULTI_TURN_ENABLED = "multi_turn_enabled"  // 新增：是否启用多轮对话
        const val KEY_MULTI_TURN_TIMEOUT = "multi_turn_timeout"  // 新增：多轮对话超时时间（秒）
        const val DEFAULT_MULTI_TURN_TIMEOUT = 30  // 默认30秒

        const val man_key = "man"
        const val woman_key = "woman"
    }

    private lateinit var binding: ActivitySettingsBinding
    private val sharedPrefs by lazy { getSharedPreferences(PREF_NAME, MODE_PRIVATE) }

    // 网络请求相关
    private val okHttpClient = OkHttpClient()
    private val objectMapper = ObjectMapper()
    private var appsList = mutableListOf<AppInfo>()
    private lateinit var appsAdapter: android.widget.ArrayAdapter<AppInfo>

    // Logo相关
    private var logosList = mutableListOf<LogoItem>()
    private lateinit var logosAdapter: android.widget.ArrayAdapter<LogoItem>

    private val baseConfigUrl = "https://cdn.guiji.ai/duix/location/gj_dh_res.zip"
    private lateinit var baseDir: File
    private val baseConfigUUID = "d39caddd-488b-4682-b6d1-13549b135dd1"     // 可以用来控制模型文件版本
    private var baseConfigReady = false

    // 连接状态管理
    private var isConnected = false

    // https://cdn.guiji.ai/duix/digital/model/1706009711636/liangwei_540s.zip
    // https://cdn.guiji.ai/duix/digital/model/1706009766199/mingzhi_540s.zip
    private var modelUrl = "" //        "https://digital-public.obs.cn-east-3.myhuaweicloud.com/duix/digital/model/1719193748558/airuike_20240409.zip"   // ** 在这里更新模型地址 ** //        "https://digital-public.obs.cn-east-3.myhuaweicloud.com/duix/digital/model/1719194036608/zixuan_20240411v2.zip"   // ** 在这里更新模型地址 ** //        "https://digital-public.obs.cn-east-3.myhuaweicloud.com/duix/digital/model/1719193425133/sufei_20240409.zip"   // ** 在这里更新模型地址 **
    //        "https://digital-public.obs.cn-east-3.myhuaweicloud.com/duix/digital/model/1719193451931/lengyan_20240407.zip"   // ** 在这里更新模型地址 **

    // 默认模型URL（作为备用）
    private val modelUrl_male = "https://digital-public.obs.cn-east-3.myhuaweicloud.com/duix/digital/model/1719193748558/airuike_20240409.zip"
    private val modelUrl_female = "https://digital-public.obs.cn-east-3.myhuaweicloud.com/duix/digital/model/1719193451931/lengyan_20240407.zip"

    // 移除init块，在onCreate中初始化modelUrl

    /**
     * 获取当前选中的模型URL
     * 优先使用用户在模型选择界面选择的模型，如果没有则使用默认模型
     */
    private fun getCurrentModelUrl(): String {
        val selectedModelUrl = sharedPrefs.getString(KEY_SELECTED_MODEL_URL, "") ?: ""
        if (selectedModelUrl.isNotEmpty()) {
            return selectedModelUrl
        }

        // 如果没有选择模型，根据当前性别设置返回默认模型
        val savedReferenceId = sharedPrefs.getString(REFERENCE_ID, man_key) ?: man_key
        return if (savedReferenceId == woman_key) modelUrl_female else modelUrl_male
    }

    private lateinit var modelDir: File
    private val liangweiUUID = "d39caddd-488b-4682-b6d1-13549b135dd1"       // 可以用来控制模型文件版本
    private var modelReady = false

    // 添加需要检查的权限列表
    private val requiredPermissions = arrayOf(Manifest.permission.RECORD_AUDIO, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE)

    @SuppressLint("SetTextI18n")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.i("DUIX_NET", "SettingsActivity onCreate 开始")

        try {
            // 初始化modelUrl
            modelUrl = getCurrentModelUrl()
            Log.i("DUIX_NET", "SettingsActivity modelUrl初始化完成: $modelUrl")

            // 尝试简化的布局加载
            Log.i("DUIX_NET", "开始加载布局...")
            binding = ActivitySettingsBinding.inflate(layoutInflater)
            Log.i("DUIX_NET", "布局加载完成，开始设置内容视图...")
            setContentView(binding.root)
            Log.i("DUIX_NET", "内容视图设置完成")
            Log.i("DUIX_NET", "SettingsActivity 布局设置完成")

            binding.tvSdkVersion.text = "SDK Version: ${BuildConfig.VERSION_NAME}"
            Log.i("DUIX_NET", "SettingsActivity SDK版本设置完成")

            // 初始化本地ASR开关状态
            val enableLocalAsr = sharedPrefs.getBoolean(KEY_ENABLE_LOCAL_ASR, false)
            binding.switchLocalAsr?.isChecked = enableLocalAsr

            // 监听开关变化并保存
            binding.switchLocalAsr?.setOnCheckedChangeListener { _, isChecked ->
                sharedPrefs.edit().putBoolean(KEY_ENABLE_LOCAL_ASR, isChecked).apply()
            }

            // 初始化唤醒词回复开关状态
            val enableWakeupReply = sharedPrefs.getBoolean(KEY_WAKEUP_REPLY, true)  // 默认开启
            binding.switchWakeupReply?.isChecked = enableWakeupReply

            // 监听唤醒词回复开关变化并保存
            binding.switchWakeupReply?.setOnCheckedChangeListener { _, isChecked ->
                sharedPrefs.edit().putBoolean(KEY_WAKEUP_REPLY, isChecked).apply()
                Log.i("DUIX_NET", "唤醒词回复设置已保存: $isChecked")
            }

            // 初始化唤醒词回复等待时间
            val wakeupDelay = sharedPrefs.getInt(KEY_WAKEUP_DELAY, 1500)  // 默认1500ms
            val seekBarProgress = ((wakeupDelay - 1000) / 100).coerceIn(0, 40)  // 1000-5000ms映射到0-40
            binding.seekbarWakeupDelay?.progress = seekBarProgress
            binding.tvWakeupDelayValue?.text = "${wakeupDelay}ms"

            // 监听等待时间滑块变化
            binding.seekbarWakeupDelay?.setOnSeekBarChangeListener(object : android.widget.SeekBar.OnSeekBarChangeListener {
                override fun onProgressChanged(seekBar: android.widget.SeekBar?, progress: Int, fromUser: Boolean) {
                    if (fromUser) {
                        val delayMs = 1000 + (progress * 100)  // 1000-5000ms
                        binding.tvWakeupDelayValue?.text = "${delayMs}ms"
                        sharedPrefs.edit().putInt(KEY_WAKEUP_DELAY, delayMs).apply()
                        Log.i("DUIX_NET", "唤醒词回复等待时间已保存: ${delayMs}ms")
                    }
                }
                override fun onStartTrackingTouch(seekBar: android.widget.SeekBar?) {}
                override fun onStopTrackingTouch(seekBar: android.widget.SeekBar?) {}
            })

            // 初始化语速设置
            initTtsSpeedSetting()

            // 初始化语言选择
            initLanguageSelector()

            // 初始化唤醒词选择
            initWakeWordSelector()

            // 初始化Logo选择
            initLogoSelector()

            // 初始化返回开场白时间设置
            initReturnToGreetingTimeSetting()

            // 初始化多轮对话设置
            initMultiTurnSettings()

            // 加载设置界面的动态Logo
            loadSettingsLogo()

            // 更新模型URL（优先使用用户选择的模型）
            modelUrl = getCurrentModelUrl()

            // 设置默认的reference_id为男声
            sharedPrefs.edit().putString(REFERENCE_ID, man_key).apply()

            val duixDir = mContext.getExternalFilesDir("duix")
            if (!duixDir!!.exists()) {
                duixDir.mkdirs()
            }
            baseDir = File(duixDir, baseConfigUrl.substring(baseConfigUrl.lastIndexOf("/") + 1)
                .replace(".zip", ""))
            modelDir = File(duixDir, modelUrl.substring(modelUrl.lastIndexOf("/") + 1)
                .replace(".zip", ""))        // 这里要求存放模型的文件夹的名字和下载的zip文件的一致以对应解压的文件夹路径

            // 从 SharedPreferences 加载上次保存的 URL
            val savedUrl = sharedPrefs.getString(KEY_TTS_URL, DEFAULT_TTS_URL) ?: DEFAULT_TTS_URL
            binding.etTtsUrl?.setText(savedUrl)

            // 从 SharedPreferences 加载上次保存的用户ID
            val savedUserId = sharedPrefs.getString(KEY_USER_ID, DEFAULT_USER_ID) ?: DEFAULT_USER_ID
            binding.etUserId?.setText(savedUserId)

            // 初始化应用下拉列表
            initAppsDropdown()

            // 从 SharedPreferences 加载上次保存的 API Key
            val savedApiKey = sharedPrefs.getString(KEY_APIKEY, "") ?: ""
            // 如果有保存的API Key，尝试在列表中选中对应的应用
            if (savedApiKey.isNotEmpty()) {
                selectAppByApiKey(savedApiKey)
            }

            binding.btnBaseConfigDownload?.setOnClickListener {
                downloadBaseConfig()
            }

            // 配置模型按钮
            binding.btnModelConfig?.setOnClickListener {
                val ttsUrl = binding.etTtsUrl.text.toString().trim()
                if (ttsUrl.isEmpty()) {
                    Toast.makeText(mContext, "请先输入TTS服务地址", Toast.LENGTH_SHORT).show()
                    return@setOnClickListener
                }

                // 保存当前输入的 URL
                sharedPrefs.edit().putString(KEY_TTS_URL, ttsUrl).apply()

                // 检查是否已选择应用
                val apiKey = sharedPrefs.getString(KEY_APIKEY, "") ?: ""
                if (apiKey.isEmpty()) {
                    // 提示用户建议先选择应用，但仍允许继续
                    AlertDialog.Builder(this)
                        .setTitle("建议先选择应用")
                        .setMessage("建议先选择应用，这样下载模型后可以直接使用数字人功能。是否继续选择模型？")
                        .setPositiveButton("继续选择模型") { _, _ ->
                            startModelSelectionActivity()
                        }
                        .setNegativeButton("先选择应用", null)
                        .show()
                } else {
                    // 已选择应用，直接跳转
                    startModelSelectionActivity()
                }
            }

            // 进入数字人页面按钮
            binding.btnModelPlay?.setOnClickListener {
                if (!baseConfigReady) {
                    Toast.makeText(mContext, "请先下载基础配置文件", Toast.LENGTH_SHORT).show()
                } else if (checkPermissions()) {
                    // 获取并验证当前输入的 URL
                    val ttsUrl = binding.etTtsUrl.text.toString().trim()
                    if (ttsUrl.isEmpty()) {
                        Toast.makeText(mContext, "请输入TTS服务地址", Toast.LENGTH_SHORT).show()
                        return@setOnClickListener
                    }

                    // 保存当前输入的 URL
                    sharedPrefs.edit().putString(KEY_TTS_URL, ttsUrl).apply()

                    startCallActivity(ttsUrl)
                } else {
                    requestPermissions()
                }
            }


            // 刷新应用列表按钮
            binding.btnRefreshApps?.setOnClickListener {
                refreshAppsList()
            }

            // 添加 URL 输入框的文本变化监听
            binding.etTtsUrl.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                override fun afterTextChanged(s: Editable?) { // 当用户输入完成后自动保存
                    s?.toString()?.trim()?.let { url ->
                        if (url.isNotEmpty()) {
                            sharedPrefs.edit().putString(KEY_TTS_URL, url).apply()
                        }
                    }
                    // 不需要更新配置进度
                }
            })

            // 添加用户ID输入框的文本变化监听
            binding.etUserId.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                override fun afterTextChanged(s: Editable?) { // 当用户输入完成后自动保存
                    s?.toString()?.trim()?.let { userId ->
                        if (userId.isNotEmpty()) {
                            sharedPrefs.edit().putString(KEY_USER_ID, userId).apply()
                            // 用户ID改变时，清空应用列表并提示刷新
                            appsList.clear()
                            appsAdapter.notifyDataSetChanged()
                            binding.actApikey.setText("", false)
                        }
                    }
                }
            })

            // 注释掉按钮点击事件，因为按钮已从界面移除
            // binding.voice2textTest?.setOnClickListener {
            //     // TODO: 暂时禁用，因为依赖被注释了
            //     // Toast.makeText(this, "语音识别功能暂时禁用", Toast.LENGTH_SHORT).show()
            //     val intent = Intent(this, com.k2fsa.sherpa.onnx.vad.asr.MainAsrActivity::class.java)
            //     startActivity(intent)
            // }

            // binding.kws?.setOnClickListener {
            //     val intent = Intent(this, com.k2fsa.sherpa.onnx.kws.MainKwsActivity::class.java)
            //     startActivity(intent)
            // }

                Log.i("DUIX_NET", "SettingsActivity 开始检查文件")
                checkFile()

                // 初始化连接状态为待连接
                updateConnectionStatus(false)

                Log.i("DUIX_NET", "SettingsActivity onCreate 完成")
            } catch (e: Exception) {
                Log.e("DUIX_NET", "SettingsActivity onCreate 失败: "+e.message, e)
            }
        }

    private fun checkPermissions(): Boolean {
        return requiredPermissions.all { permission ->
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && permission in arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE)) { // Android 10 及以上不需要存储权限
                true
            } else {
                ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED
            }
        }
    }

    private fun requestPermissions() {
        val permissionsToRequest = requiredPermissions.filter { permission ->
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && permission in arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE)) { // Android 10 及以上不需要存储权限
                false
            } else {
                ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED
            }
        }.toTypedArray()

        if (permissionsToRequest.isNotEmpty()) {
            ActivityCompat.requestPermissions(this, permissionsToRequest, PERMISSION_REQUEST_CODE)
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int,
                                            permissions: Array<out String>,
                                            grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PERMISSION_REQUEST_CODE) {
            if (grantResults.isNotEmpty() && grantResults.all { it == PackageManager.PERMISSION_GRANTED }) {
                if (!modelReady) {
                    downloadModel()
                } else if (!baseConfigReady) {
                    Toast.makeText(mContext, "您必须正确安装基础配置文件", Toast.LENGTH_SHORT)
                        .show()
                } else { // 获取并验证当前输入的 URL
                    val ttsUrl = binding.etTtsUrl.text.toString().trim()
                    if (ttsUrl.isNotEmpty()) {
                        startCallActivity(ttsUrl)
                    } else {
                        Toast.makeText(mContext, "请输入TTS服务地址", Toast.LENGTH_SHORT).show()
                    }
                }
            } else {
                Toast.makeText(this, "需要录音和存储权限才能使用该功能", Toast.LENGTH_SHORT).show()
                showPermissionExplanationDialog()
            }
        }
    }

    private fun showPermissionExplanationDialog() {
        AlertDialog.Builder(this).setTitle("需要权限")
            .setMessage("此功能需要录音和存储权限才能正常使用。请在设置中开启相关权限。")
            .setPositiveButton("去设置") { _, _ -> // 跳转到应用设置页面
                openAppSettings()
            }.setNegativeButton("取消", null).show()
    }

    private fun openAppSettings() {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = Uri.fromParts("package", packageName, null)
        }
        startActivity(intent)
    }

    private fun startCallActivity(ttsUrl: String? = null) {
        // 获取选中的应用的API Key
        val selectedApp = appsList.find { it.name == binding.actApikey.text.toString() }
        val apiKey = selectedApp?.apiKey ?: ""

        val intent = Intent(this, CallActivity::class.java).apply {
            putExtra("baseDir", baseDir.absolutePath)
            putExtra("modelDir", modelDir.absolutePath)
            putExtra("ttsUrl", ttsUrl)  // 添加 ttsUrl 参数
            putExtra("apiKey", apiKey)  // 添加选中的 apiKey 参数
            putExtra("forceReload", true) // 强制重新加载以清理重影
        }
        startActivity(intent)
    }

    private fun downloadBaseConfig() {
        binding.btnBaseConfigDownload.isEnabled = false
        binding.progressBaseConfig.progress = 0
        StorageService.downloadAndUnzip(mContext, baseConfigUrl, baseDir.absolutePath, baseConfigUUID, object :
            StorageService.Callback {
            override fun onDownloadProgress(progress: Int) {
                runOnUiThread {
                    binding.progressBaseConfig.progress = progress / 2
                }
            }

            override fun onUnzipProgress(progress: Int) {
                runOnUiThread {
                    binding.progressBaseConfig.progress = 50 + progress / 2
                }
            }

            override fun onComplete(path: String?) {
                runOnUiThread {
                    binding.btnBaseConfigDownload.isEnabled = false
                    binding.btnBaseConfigDownload.text = getString(R.string.ready)
                    binding.progressBaseConfig.progress = 100
                    baseConfigReady = true
                    // 基础配置下载完成
                    checkAndAutoLaunch()
                }
            }

            override fun onError(msg: String?) {
                runOnUiThread {
                    binding.btnBaseConfigDownload.isEnabled = true
                    binding.progressBaseConfig.progress = 0
                    Toast.makeText(mContext, "文件下载异常: $msg", Toast.LENGTH_SHORT).show()
                }
            }
        }, true)
    }

    private fun downloadModel() { // 检查模型文件是否存在且完整
        if (modelDir.exists() && File(modelDir, "/uuid").exists() && liangweiUUID == BufferedReader(InputStreamReader(FileInputStream(File(modelDir, "/uuid")))).readLine()) {
            modelReady = true
            return
        }

        // 如果文件不存在或不完整，开始下载
        StorageService.downloadAndUnzip(mContext, modelUrl, modelDir.absolutePath, liangweiUUID, object :
            StorageService.Callback {
            override fun onDownloadProgress(progress: Int) {
                // 模型下载进度，可以在这里添加日志
                Log.d("DUIX_NET", "模型下载进度: $progress%")
            }

            override fun onUnzipProgress(progress: Int) {
                // 模型解压进度，可以在这里添加日志
                Log.d("DUIX_NET", "模型解压进度: $progress%")
            }

            override fun onComplete(path: String?) {
                runOnUiThread {
                    modelReady = true
                    // 模型下载完成
                    checkAndAutoLaunch()
                }
            }

            override fun onError(msg: String?) {
                runOnUiThread {
                    Toast.makeText(mContext, "文件下载异常: $msg", Toast.LENGTH_SHORT).show()
                }
            }
        }, false        // for debug
        )
    }

    private fun checkAndAutoLaunch() {
        // 检查所有必要文件是否都准备好了
        if (baseConfigReady && modelReady) {
            // 获取当前输入的 URL
            val ttsUrl = binding.etTtsUrl.text.toString().trim()
            if (ttsUrl.isNotEmpty()) {
                // 显示提示信息
                Toast.makeText(this, "文件下载完成，正在启动数字人...", Toast.LENGTH_SHORT).show()
                // 延迟1秒后自动跳转，让用户看到提示信息
                Handler(Looper.getMainLooper()).postDelayed({
                    startCallActivity(ttsUrl)
                }, 1000)
            } else {
                Toast.makeText(this, "文件下载完成，请输入TTS服务地址后点击启动数字人", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun checkFile() {
        // 检查基础配置文件
        if (baseDir.exists() && File(baseDir, "/uuid").exists() && baseConfigUUID == BufferedReader(InputStreamReader(FileInputStream(File(baseDir, "/uuid")))).readLine()) {
            binding.btnBaseConfigDownload.isEnabled = false
            binding.btnBaseConfigDownload.text = getString(R.string.ready)
            baseConfigReady = true
            Log.i("DUIX_NET", "基础配置文件检查完成: 已存在")
        } else {
            // 基础配置不存在，自动开始下载
            binding.btnBaseConfigDownload.isEnabled = false
            binding.btnBaseConfigDownload.text = "自动下载中..."
            baseConfigReady = false

            Log.i("DUIX_NET", "基础配置文件不存在，自动开始下载")
            Toast.makeText(this, "正在自动下载基础配置文件...", Toast.LENGTH_SHORT).show()

            // 自动开始下载基础配置
            downloadBaseConfig()
        }

        // 检查模型文件
        if (modelDir.exists() && File(modelDir, "/uuid").exists() && liangweiUUID == BufferedReader(InputStreamReader(FileInputStream(File(modelDir, "/uuid")))).readLine()) {
            modelReady = true
            Log.i("DUIX_NET", "模型文件检查完成: 已存在")
        } else {
            modelReady = false
            Log.i("DUIX_NET", "模型文件检查完成: 不存在或不完整")

            // 如果模型不完整且有服务器配置，提示用户并询问是否跳转到模型选择界面
            val ttsUrl = binding.etTtsUrl.text.toString().trim()
            if (ttsUrl.isNotEmpty()) {
                Log.i("DUIX_NET", "检测到模型下载不完整，有服务器配置，提示用户跳转到模型选择界面")

                // 延迟一下再显示对话框，避免在onCreate时立即弹出
                Handler(Looper.getMainLooper()).postDelayed({
                    showModelIncompleteDialog()
                }, 500)
            }
        }

        // 进度条只反映基础配置下载状态，不需要额外处理
    }



    /**
     * 更新连接状态显示
     */
    private fun updateConnectionStatus(connected: Boolean) {
        isConnected = connected
        runOnUiThread {
            try {
                if (connected) {
                    binding.tvStatusBadge?.text = "已连接"
                    binding.tvStatusBadge?.setTextColor(ContextCompat.getColor(this, R.color.md3_success))
                } else {
                    binding.tvStatusBadge?.text = "待连接"
                    binding.tvStatusBadge?.setTextColor(ContextCompat.getColor(this, R.color.md3_on_surface_variant))
                }
                Log.i("DUIX_NET", "连接状态更新: ${if (connected) "已连接" else "待连接"}")
            } catch (e: Exception) {
                Log.e("DUIX_NET", "更新连接状态失败: ${e.message}", e)
            }
        }
    }

    /**
     * 初始化应用下拉列表
     */
    private fun initAppsDropdown() {
        // 创建适配器
        appsAdapter = android.widget.ArrayAdapter(
            this,
            android.R.layout.simple_dropdown_item_1line,
            appsList
        )

        // 设置适配器
        binding.actApikey.setAdapter(appsAdapter)

        // 设置为不可编辑，只能选择
        binding.actApikey.keyListener = null
        binding.actApikey.isFocusable = false
        binding.actApikey.isFocusableInTouchMode = false

        // 设置点击监听器
        binding.actApikey.setOnClickListener {
            if (appsList.isNotEmpty()) {
                // 显示应用选择对话框
                showAppSelectionDialog()
            } else {
                Toast.makeText(this, "请先刷新应用列表", Toast.LENGTH_SHORT).show()
            }
        }

        // 自动加载应用列表 - 暂时注释掉以避免网络请求阻塞
        // refreshAppsList()
    }

    /**
     * 显示应用选择对话框
     */
    private fun showAppSelectionDialog() {
        val appNames = appsList.map { it.name }.toTypedArray()

        AlertDialog.Builder(this)
            .setTitle("选择应用")
            .setItems(appNames) { _, which ->
                if (which < appsList.size) {
                    val selectedApp = appsList[which]

                    // 更新显示
                    binding.actApikey.setText(selectedApp.name)

                    // 保存选中的API Key
                    sharedPrefs.edit().putString(KEY_APIKEY, selectedApp.apiKey).apply()

                    Log.i("DUIX_NET", "选择应用: ${selectedApp.name} (${selectedApp.apiKey})")
                    Toast.makeText(this, "已选择应用: ${selectedApp.name}", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 刷新应用列表
     */
    private fun refreshAppsList() {
        val userId = binding.etUserId.text.toString().trim()
        if (userId.isEmpty()) {
            Toast.makeText(this, "请先输入用户ID", Toast.LENGTH_SHORT).show()
            return
        }

        val baseUrl = binding.etTtsUrl.text.toString().trim()
        if (baseUrl.isEmpty()) {
            Toast.makeText(this, "请先输入服务器地址", Toast.LENGTH_SHORT).show()
            return
        }

        // 构建设备API URL
        val deviceApiPath = ApiConfig.Device.getDeviceAppsPath(userId)
        val fullUrl = ApiConfig.buildUrl(baseUrl, deviceApiPath)

        // 发送网络请求
        val request = Request.Builder()
            .url(fullUrl)
            .get()
            .build()

        binding.btnRefreshApps.isEnabled = false
        binding.btnRefreshApps.text = "加载中..."

        okHttpClient.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    binding.btnRefreshApps.isEnabled = true
                    binding.btnRefreshApps.text = "刷新应用列表"
                    // 网络请求失败，更新连接状态为待连接
                    updateConnectionStatus(false)
                    Toast.makeText(this@SettingsActivity, "获取应用列表失败: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                // 在后台线程中读取响应体
                try {
                    if (response.isSuccessful) {
                        val responseBody = response.body?.string()
                        if (responseBody != null) {
                            val deviceResponse = objectMapper.readValue(responseBody, DeviceResponse::class.java)

                            // 切换到主线程更新UI
                            runOnUiThread {
                                binding.btnRefreshApps.isEnabled = true
                                binding.btnRefreshApps.text = "刷新应用列表"

                                // 网络请求成功，更新连接状态为已连接
                                updateConnectionStatus(true)

                                // 更新应用列表
                                appsList.clear()
                                appsList.addAll(deviceResponse.apps)
                                appsAdapter.notifyDataSetChanged()

                                Log.i("DUIX_NET", "应用列表更新完成，共${appsList.size}个应用: ${appsList.map { it.name }}")
                                Toast.makeText(this@SettingsActivity, "成功加载 ${appsList.size} 个应用，点击应用选择框进行选择", Toast.LENGTH_SHORT).show()

                                // 如果有保存的API Key，尝试选中对应的应用
                                val savedApiKey = sharedPrefs.getString(KEY_APIKEY, "") ?: ""
                                if (savedApiKey.isNotEmpty()) {
                                    selectAppByApiKey(savedApiKey)
                                }
                            }
                        } else {
                            runOnUiThread {
                                binding.btnRefreshApps.isEnabled = true
                                binding.btnRefreshApps.text = "刷新应用列表"
                                // 服务器返回空数据，连接状态设为待连接
                                updateConnectionStatus(false)
                                Toast.makeText(this@SettingsActivity, "服务器返回空数据", Toast.LENGTH_SHORT).show()
                            }
                        }
                    } else {
                        runOnUiThread {
                            binding.btnRefreshApps.isEnabled = true
                            binding.btnRefreshApps.text = "刷新应用列表"
                            // HTTP错误，连接状态设为待连接
                            updateConnectionStatus(false)
                            Toast.makeText(this@SettingsActivity, "获取应用列表失败: ${response.code}", Toast.LENGTH_SHORT).show()
                        }
                    }
                } catch (e: Exception) {
                    runOnUiThread {
                        binding.btnRefreshApps.isEnabled = true
                        binding.btnRefreshApps.text = "刷新应用列表"
                        // 解析异常，连接状态设为待连接
                        updateConnectionStatus(false)
                        Toast.makeText(this@SettingsActivity, "解析应用列表失败: ${e.message}", Toast.LENGTH_SHORT).show()
                    }
                }
            }
        })
    }

    /**
     * 根据API Key选中对应的应用
     */
    private fun selectAppByApiKey(apiKey: String) {
        val app = appsList.find { it.apiKey == apiKey }
        if (app != null) {
            binding.actApikey.setText(app.name, false)
        }
    }

    /**
     * 跳转到模型选择界面
     */
    private fun startModelSelectionActivity() {
        val intent = Intent(this, ModelSelectionActivity::class.java)
        startActivity(intent)
    }

    /**
     * 显示模型不完整的对话框
     */
    private fun showModelIncompleteDialog() {
        AlertDialog.Builder(this)
            .setTitle("模型文件不完整")
            .setMessage("检测到模型下载不完整，请重新下载。是否跳转到模型选择界面？")
            .setPositiveButton("去选择模型") { _, _ ->
                startModelSelectionActivity()
            }
            .setNegativeButton("稍后处理", null)
            .show()
    }

    /**
     * 初始化语速设置
     */
    private fun initTtsSpeedSetting() {
        // 从 SharedPreferences 加载保存的语速值
        val savedSpeed = sharedPrefs.getFloat(KEY_TTS_SPEED, DEFAULT_TTS_SPEED)

        // 将语速值(1.0-2.0)转换为SeekBar进度值(0-100)
        val progress = ((savedSpeed - 1.0f) * 100).toInt()
        binding.seekbarTtsSpeed.progress = progress

        // 更新显示的语速值
        updateTtsSpeedDisplay(savedSpeed)

        // 设置SeekBar监听器
        binding.seekbarTtsSpeed.setOnSeekBarChangeListener(object : android.widget.SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: android.widget.SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    // 将SeekBar进度值(0-100)转换为语速值(1.0-2.0)
                    val speed = 1.0f + (progress / 100.0f)
                    updateTtsSpeedDisplay(speed)
                }
            }

            override fun onStartTrackingTouch(seekBar: android.widget.SeekBar?) {}

            override fun onStopTrackingTouch(seekBar: android.widget.SeekBar?) {
                // 用户停止拖动时保存设置
                val progress = seekBar?.progress ?: 0
                val speed = 1.0f + (progress / 100.0f)
                saveTtsSpeed(speed)
            }
        })

        Log.i("DUIX_NET", "语速设置初始化完成，当前语速: ${savedSpeed}x")
    }

    /**
     * 更新语速显示
     */
    private fun updateTtsSpeedDisplay(speed: Float) {
        binding.tvTtsSpeedValue.text = String.format("%.1fx", speed)
    }

    /**
     * 保存语速设置
     */
    private fun saveTtsSpeed(speed: Float) {
        sharedPrefs.edit().putFloat(KEY_TTS_SPEED, speed).apply()
        Log.i("DUIX_NET", "语速设置已保存: ${speed}x")
    }

    /**
     * 初始化返回开场白时间设置
     */
    private fun initReturnToGreetingTimeSetting() {
        // 从 SharedPreferences 加载保存的时间值（秒）
        val savedTime = sharedPrefs.getInt(KEY_RETURN_TO_GREETING_TIME, DEFAULT_RETURN_TO_GREETING_TIME)

        // 将时间值(5-120秒)转换为SeekBar进度值(0-115)
        val progress = (savedTime - 5).coerceIn(0, 115)
        binding.seekbarReturnToGreeting.progress = progress

        // 更新显示的时间值
        updateReturnToGreetingTimeDisplay(savedTime)

        // 设置SeekBar监听器
        binding.seekbarReturnToGreeting.setOnSeekBarChangeListener(object : android.widget.SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: android.widget.SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    // 将SeekBar进度值(0-115)转换为时间值(5-120秒)
                    val timeSeconds = progress + 5
                    updateReturnToGreetingTimeDisplay(timeSeconds)
                }
            }

            override fun onStartTrackingTouch(seekBar: android.widget.SeekBar?) {}

            override fun onStopTrackingTouch(seekBar: android.widget.SeekBar?) {
                // 用户停止拖动时保存设置
                val progress = seekBar?.progress ?: 0
                val timeSeconds = progress + 5
                saveReturnToGreetingTime(timeSeconds)
            }
        })

        Log.i("DUIX_NET", "返回开场白时间设置初始化完成，当前时间: ${savedTime}秒")
    }

    /**
     * 更新返回开场白时间显示
     */
    private fun updateReturnToGreetingTimeDisplay(timeSeconds: Int) {
        binding.tvReturnToGreetingValue.text = "${timeSeconds}秒"
    }

    /**
     * 保存返回开场白时间设置
     */
    private fun saveReturnToGreetingTime(timeSeconds: Int) {
        sharedPrefs.edit().putInt(KEY_RETURN_TO_GREETING_TIME, timeSeconds).apply()
        Log.i("DUIX_NET", "返回开场白时间设置已保存: ${timeSeconds}秒")
    }

    /**
     * 初始化语言选择器
     */
    private fun initLanguageSelector() {
        // 语言映射
        val languageMapping = mapOf(
            "zh" to "中文",
            "en" to "英文",
            "ja" to "日文",
            "ko" to "韩文",
            "fr" to "法文",
            "de" to "德文",
            "es" to "西班牙文",
            "ar" to "阿拉伯文"
        )

        // 创建语言选项列表
        val languageOptions = languageMapping.values.toList()
        val languageCodes = languageMapping.keys.toList()

        // 创建适配器
        val adapter = android.widget.ArrayAdapter(
            this,
            android.R.layout.simple_spinner_item,
            languageOptions
        )
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.spinnerLanguage.adapter = adapter

        // 加载保存的语言设置
        val savedLanguage = sharedPrefs.getString(KEY_LANGUAGE, DEFAULT_LANGUAGE) ?: DEFAULT_LANGUAGE
        val savedIndex = languageCodes.indexOf(savedLanguage)
        if (savedIndex >= 0) {
            binding.spinnerLanguage.setSelection(savedIndex)
        }

        // 设置选择监听器
        binding.spinnerLanguage.onItemSelectedListener = object : android.widget.AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: android.widget.AdapterView<*>?, view: android.view.View?, position: Int, id: Long) {
                if (position >= 0 && position < languageCodes.size) {
                    val selectedLanguageCode = languageCodes[position]
                    val selectedLanguageName = languageOptions[position]

                    // 保存选择的语言
                    sharedPrefs.edit().putString(KEY_LANGUAGE, selectedLanguageCode).apply()

                    Log.i("DUIX_NET", "语言设置已保存: $selectedLanguageCode ($selectedLanguageName)")
                }
            }

            override fun onNothingSelected(parent: android.widget.AdapterView<*>?) {
                // 不处理
            }
        }

        Log.i("DUIX_NET", "语言选择器初始化完成，当前语言: $savedLanguage")
    }

    /**
     * 初始化唤醒词选择器
     */
    private fun initWakeWordSelector() {
        // 先设置默认选项
        val defaultOptions = listOf("小灵小灵")
        val defaultAdapter = android.widget.ArrayAdapter(
            this,
            android.R.layout.simple_spinner_item,
            defaultOptions
        )
        defaultAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.spinnerWakeWord.adapter = defaultAdapter
        
        // 设置刷新按钮点击事件
        binding.btnRefreshWakeWord?.setOnClickListener {
            // 显示加载中提示
            Toast.makeText(this, "正在获取最新唤醒词...", Toast.LENGTH_SHORT).show()
            // 从后端获取唤醒词列表
            fetchWakeWordsFromServer()
        }

        // 添加测试功能（长按刷新按钮触发）
        binding.btnRefreshWakeWord?.setOnLongClickListener {
            testAddWakeWord()
            true
        }

        // 从后端获取唤醒词列表
        fetchWakeWordsFromServer()
    }

    /**
     * 从服务器获取唤醒词列表
     */
    private fun fetchWakeWordsFromServer() {
        val ttsBaseUrl = sharedPrefs.getString(KEY_TTS_URL, "") ?: ""
        if (ttsBaseUrl.isEmpty()) {
            Log.w("DUIX_NET", "TTS服务器地址为空，使用默认唤醒词")
            return
        }

        try {
            val wakeWordsUrl = ai.guiji.duix.test.config.ApiConfig.buildUrl(ttsBaseUrl, ai.guiji.duix.test.config.ApiConfig.WakeWord.GET_WAKE_WORDS_ENDPOINT)
            Log.i("DUIX_NET", "获取唤醒词列表: $wakeWordsUrl")

            val request = okhttp3.Request.Builder()
                .url(wakeWordsUrl)
                .get()
                .addHeader("User-Agent", "Android App")
                .addHeader("Accept", "application/json")
                .build()

            okHttpClient.newCall(request).enqueue(object : okhttp3.Callback {
                override fun onFailure(call: okhttp3.Call, e: java.io.IOException) {
                    Log.e("DUIX_NET", "获取唤醒词列表失败: ${e.message}", e)
                    runOnUiThread {
                        android.widget.Toast.makeText(this@SettingsActivity, "获取唤醒词列表失败", android.widget.Toast.LENGTH_SHORT).show()
                    }
                }

                override fun onResponse(call: okhttp3.Call, response: okhttp3.Response) {
                    try {
                        if (response.isSuccessful) {
                            response.body?.string()?.let { responseBody ->
                                Log.i("DUIX_NET", "唤醒词列表响应: $responseBody")

                                val gson = com.google.gson.Gson()
                                val wakeWordItems = gson.fromJson(responseBody, Array<ai.guiji.duix.test.model.WakeWordItem>::class.java).toList()

                                if (wakeWordItems.isNotEmpty()) {
                                    runOnUiThread {
                                        updateWakeWordSpinner(wakeWordItems)
                                        // 更新本地唤醒词文件
                                        updateLocalWakeWordFile(wakeWordItems)
                                    }
                                } else {
                                    Log.w("DUIX_NET", "唤醒词列表为空")
                                }
                            }
                        } else {
                            Log.e("DUIX_NET", "获取唤醒词列表失败，状态码: ${response.code}")
                        }
                    } catch (e: Exception) {
                        Log.e("DUIX_NET", "解析唤醒词列表失败: ${e.message}", e)
                    }
                }
            })
        } catch (e: Exception) {
            Log.e("DUIX_NET", "发送唤醒词列表请求失败: ${e.message}", e)
        }
    }

    /**
     * 更新唤醒词下拉菜单
     */
    private fun updateWakeWordSpinner(wakeWordItems: List<ai.guiji.duix.test.model.WakeWordItem>) {
        val wakeWords = wakeWordItems.map { it.word }

        // 创建适配器
        val adapter = android.widget.ArrayAdapter(
            this,
            android.R.layout.simple_spinner_item,
            wakeWords
        )
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.spinnerWakeWord.adapter = adapter

        // 加载保存的唤醒词设置
        val savedWakeWord = sharedPrefs.getString(KEY_WAKE_WORD, DEFAULT_WAKE_WORD) ?: DEFAULT_WAKE_WORD
        val savedIndex = wakeWords.indexOf(savedWakeWord)
        if (savedIndex >= 0) {
            binding.spinnerWakeWord.setSelection(savedIndex)
        }

        // 设置选择监听器
        binding.spinnerWakeWord.onItemSelectedListener = object : android.widget.AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: android.widget.AdapterView<*>?, view: android.view.View?, position: Int, id: Long) {
                if (position >= 0 && position < wakeWords.size) {
                    val selectedWakeWord = wakeWords[position]

                    // 保存选择的唤醒词
                    sharedPrefs.edit().putString(KEY_WAKE_WORD, selectedWakeWord).apply()

                    Log.i("DUIX_NET", "唤醒词设置已保存: $selectedWakeWord")
                    android.widget.Toast.makeText(this@SettingsActivity, "唤醒词已设置为: $selectedWakeWord", android.widget.Toast.LENGTH_SHORT).show()
                }
            }

            override fun onNothingSelected(parent: android.widget.AdapterView<*>?) {
                // 不处理
            }
        }

        Log.i("DUIX_NET", "唤醒词选择器更新完成，当前唤醒词: $savedWakeWord")
    }

    /**
     * 更新本地唤醒词文件
     */
    private fun updateLocalWakeWordFile(wakeWordItems: List<ai.guiji.duix.test.model.WakeWordItem>) {
        try {
            // 获取assets目录下的keywords.txt文件路径
            val keywordsFilePath = "sherpa-onnx-kws-zipformer-wenetspeech-3.3M-2024-01-01/keywords.txt"

            // 读取现有的keywords.txt文件内容
            val existingKeywords = mutableSetOf<String>()
            try {
                assets.open(keywordsFilePath).use { inputStream ->
                    inputStream.bufferedReader().useLines { lines ->
                        lines.forEach { line ->
                            if (line.contains("@")) {
                                val keyword = line.substringAfter("@").trim()
                                existingKeywords.add(keyword)
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Log.w("DUIX_NET", "读取现有唤醒词文件失败: ${e.message}")
            }

            // 准备新的唤醒词内容
            val newKeywords = wakeWordItems.map { it.word }.toSet()
            val allKeywords = existingKeywords + newKeywords

            // 生成新的keywords.txt内容
            val newContent = StringBuilder()

            // 保留原有的唤醒词（从assets读取的）
            try {
                assets.open(keywordsFilePath).use { inputStream ->
                    inputStream.bufferedReader().useLines { lines ->
                        lines.forEach { line ->
                            if (line.trim().isNotEmpty()) {
                                newContent.appendLine(line)
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Log.w("DUIX_NET", "读取原始唤醒词文件失败: ${e.message}")
            }

            // 添加从后端获取的新唤醒词（如果不存在的话）
            wakeWordItems.forEach { wakeWordItem ->
                val keyword = wakeWordItem.word
                if (!existingKeywords.contains(keyword)) {
                    // 直接使用后端返回的pinyin_format字段
                    val pinyinLine = wakeWordItem.pinyinFormat ?: generatePinyinForKeyword(keyword)
                    newContent.appendLine(pinyinLine)
                    Log.i("DUIX_NET", "添加新唤醒词: $keyword -> $pinyinLine")
                }
            }

            // 创建私有模型目录
            val privateModelDir = java.io.File(filesDir, "sherpa-onnx-kws-zipformer-wenetspeech-3.3M-2024-01-01")
            if (!privateModelDir.exists()) {
                privateModelDir.mkdirs()
                Log.i("DUIX_NET", "创建私有模型目录: ${privateModelDir.absolutePath}")

                // 复制模型文件到私有目录（除了keywords.txt）
                val copySuccess = copyAssetDirToPrivate("sherpa-onnx-kws-zipformer-wenetspeech-3.3M-2024-01-01", privateModelDir)
                if (!copySuccess) {
                    Log.e("DUIX_NET", "模型文件复制失败，将只更新keywords.txt到应用根目录")
                    // 如果复制失败，将keywords.txt写入到应用根目录
                    val fallbackKeywordsFile = java.io.File(filesDir, "keywords.txt")
                    fallbackKeywordsFile.writeText(newContent.toString())
                    Log.i("DUIX_NET", "备用唤醒词文件路径: ${fallbackKeywordsFile.absolutePath}")
                    return
                }
            }

            // 将更新后的keywords.txt写入到私有模型目录
            val privateKeywordsFile = java.io.File(privateModelDir, "keywords.txt")
            privateKeywordsFile.writeText(newContent.toString())

            Log.i("DUIX_NET", "唤醒词文件更新完成，共${allKeywords.size}个唤醒词")
            Log.i("DUIX_NET", "私有唤醒词文件路径: ${privateKeywordsFile.absolutePath}")

        } catch (e: Exception) {
            Log.e("DUIX_NET", "更新本地唤醒词文件失败: ${e.message}", e)
        }
    }

    /**
     * 为唤醒词生成拼音（备用方法，主要使用后端返回的pinyin_format）
     */
    private fun generatePinyinForKeyword(keyword: String): String {
        // 这个方法现在主要作为备用，优先使用后端返回的pinyin_format字段
        Log.w("DUIX_NET", "后端未提供拼音格式，使用备用转换: $keyword")

        // 简化的备用格式，建议在后端完善pinyin_format字段
        return "# 需要后端提供拼音格式: $keyword"
    }

    /**
     * 复制assets目录到私有目录
     * @return 复制是否成功
     */
    private fun copyAssetDirToPrivate(assetDirPath: String, targetDir: java.io.File): Boolean {
        return try {
            val assetFiles = assets.list(assetDirPath) ?: return false
            var successCount = 0
            var totalFiles = 0

            for (filename in assetFiles) {
                if (filename == "keywords.txt") {
                    // 跳过keywords.txt，我们会单独处理
                    continue
                }

                val assetFilePath = "$assetDirPath/$filename"
                val targetFile = java.io.File(targetDir, filename)

                // 检查是否是目录
                val subAssets = try {
                    assets.list(assetFilePath)
                } catch (e: Exception) {
                    null
                }

                if (subAssets != null && subAssets.isNotEmpty()) {
                    // 是目录，递归复制
                    targetFile.mkdirs()
                    if (copyAssetDirToPrivate(assetFilePath, targetFile)) {
                        successCount++
                    }
                    totalFiles++
                } else {
                    // 是文件，直接复制
                    try {
                        assets.open(assetFilePath).use { inputStream ->
                            targetFile.outputStream().use { outputStream ->
                                inputStream.copyTo(outputStream)
                            }
                        }
                        Log.d("DUIX_NET", "✓ 复制文件成功: $assetFilePath -> ${targetFile.absolutePath}")
                        successCount++
                    } catch (e: Exception) {
                        Log.e("DUIX_NET", "✗ 复制文件失败: $assetFilePath - ${e.message}")
                    }
                    totalFiles++
                }
            }

            val success = successCount == totalFiles && totalFiles > 0
            Log.i("DUIX_NET", "目录复制完成: $successCount/$totalFiles 个文件成功")
            success
        } catch (e: Exception) {
            Log.e("DUIX_NET", "复制assets目录失败: ${e.message}", e)
            false
        }
    }

    /**
     * 测试添加唤醒词功能（长按刷新按钮触发）
     */
    private fun testAddWakeWord() {
        try {
            // 创建测试唤醒词数据
            val testWakeWords = listOf(
                ai.guiji.duix.test.model.WakeWordItem(
                    id = 999,
                    word = "小二小二",
                    description = "测试唤醒词",
                    createdAt = "2025-07-22T14:00:00Z",
                    updatedAt = "2025-07-22T14:00:00Z",
                    pinyinFormat = "x iǎo è r x iǎo è r @小二小二"
                )
            )

            // 更新本地唤醒词文件
            updateLocalWakeWordFile(testWakeWords)

            // 更新下拉菜单
            updateWakeWordSpinner(testWakeWords)

            Toast.makeText(this, "测试唤醒词已添加：小二小二", Toast.LENGTH_LONG).show()
            Log.i("DUIX_NET", "测试唤醒词添加完成")

        } catch (e: Exception) {
            Log.e("DUIX_NET", "测试添加唤醒词失败: ${e.message}", e)
            Toast.makeText(this, "测试失败: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    /**
     * 初始化Logo选择器
     */
    private fun initLogoSelector() {
        // 先设置默认选项
        val defaultOptions = listOf("默认Logo")
        val defaultAdapter = android.widget.ArrayAdapter(
            this,
            android.R.layout.simple_spinner_item,
            defaultOptions
        )
        defaultAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.spinnerLogo.adapter = defaultAdapter

        // 设置刷新按钮点击事件
        binding.btnRefreshLogo?.setOnClickListener {
            // 显示加载中提示
            Toast.makeText(this, "正在获取最新Logo列表...", Toast.LENGTH_SHORT).show()
            // 从后端获取Logo列表
            fetchLogosFromServer()
        }

        // 从后端获取Logo列表
        fetchLogosFromServer()
    }

    /**
     * 从服务器获取Logo列表
     */
    private fun fetchLogosFromServer() {
        val ttsBaseUrl = sharedPrefs.getString(KEY_TTS_URL, "") ?: ""
        if (ttsBaseUrl.isEmpty()) {
            Log.w("DUIX_NET", "TTS服务器地址为空，无法获取Logo列表")
            return
        }

        try {
            val logosUrl = ApiConfig.buildUrl(ttsBaseUrl, ApiConfig.Logo.LOGO_LIST_ENDPOINT)
            Log.i("DUIX_NET", "获取Logo列表: $logosUrl")

            val request = Request.Builder()
                .url(logosUrl)
                .get()
                .addHeader("User-Agent", "Android App")
                .addHeader("Accept", "application/json")
                .build()

            okHttpClient.newCall(request).enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    Log.e("DUIX_NET", "获取Logo列表失败: ${e.message}", e)
                    runOnUiThread {
                        Toast.makeText(this@SettingsActivity, "获取Logo列表失败", Toast.LENGTH_SHORT).show()
                    }
                }

                override fun onResponse(call: Call, response: Response) {
                    try {
                        Log.i("DUIX_NET", "Logo列表响应状态码: ${response.code}")
                        if (response.isSuccessful) {
                            response.body?.string()?.let { responseBody ->
                                Log.i("DUIX_NET", "Logo列表响应内容: $responseBody")
                                Log.i("DUIX_NET", "响应内容长度: ${responseBody.length}")

                                try {
                                    val logoItems = objectMapper.readValue(responseBody, Array<LogoItem>::class.java).toList()
                                    Log.i("DUIX_NET", "解析到 ${logoItems.size} 个Logo项")

                                    logoItems.forEachIndexed { index, logo ->
                                        Log.i("DUIX_NET", "Logo[$index]: id=${logo.id}, name=${logo.name}, isActive=${logo.isActive}, url=${logo.url}")
                                    }

                                    if (logoItems.isNotEmpty()) {
                                        runOnUiThread {
                                            Log.i("DUIX_NET", "开始更新Logo下拉菜单")
                                            updateLogoSpinner(logoItems)
                                        }
                                    } else {
                                        Log.w("DUIX_NET", "Logo列表为空")
                                        runOnUiThread {
                                            Toast.makeText(this@SettingsActivity, "服务器返回的Logo列表为空", Toast.LENGTH_SHORT).show()
                                        }
                                    }
                                } catch (parseException: Exception) {
                                    Log.e("DUIX_NET", "JSON解析失败: ${parseException.message}", parseException)
                                    runOnUiThread {
                                        Toast.makeText(this@SettingsActivity, "数据解析失败: ${parseException.message}", Toast.LENGTH_SHORT).show()
                                    }
                                }
                            } ?: run {
                                Log.e("DUIX_NET", "响应体为空")
                                runOnUiThread {
                                    Toast.makeText(this@SettingsActivity, "服务器响应为空", Toast.LENGTH_SHORT).show()
                                }
                            }
                        } else {
                            Log.e("DUIX_NET", "获取Logo列表失败，状态码: ${response.code}")
                            runOnUiThread {
                                Toast.makeText(this@SettingsActivity, "获取Logo列表失败，状态码: ${response.code}", Toast.LENGTH_SHORT).show()
                            }
                        }
                    } catch (e: Exception) {
                        Log.e("DUIX_NET", "处理Logo列表响应失败: ${e.message}", e)
                        runOnUiThread {
                            Toast.makeText(this@SettingsActivity, "处理响应失败: ${e.message}", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            })
        } catch (e: Exception) {
            Log.e("DUIX_NET", "发送Logo列表请求失败: ${e.message}", e)
        }
    }

    /**
     * 更新Logo下拉菜单
     */
    private fun updateLogoSpinner(logoItems: List<LogoItem>) {
        // 暂时不过滤is_active字段，显示所有logo
        // 如果需要过滤，可以取消下面这行注释，注释掉下下行
        // val availableLogos = logoItems.filter { it.isActive }
        val availableLogos = logoItems

        if (availableLogos.isEmpty()) {
            Log.w("DUIX_NET", "没有可用的Logo")
            Toast.makeText(this, "没有可用的Logo", Toast.LENGTH_SHORT).show()
            return
        }

        Log.i("DUIX_NET", "准备显示${availableLogos.size}个Logo")

        // 更新logo列表
        logosList.clear()
        logosList.addAll(availableLogos)

        // 创建适配器
        logosAdapter = android.widget.ArrayAdapter(
            this,
            android.R.layout.simple_spinner_item,
            logosList
        )
        logosAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.spinnerLogo.adapter = logosAdapter

        // 加载保存的Logo设置
        val savedLogoName = sharedPrefs.getString(KEY_SELECTED_LOGO_NAME, "") ?: ""
        if (savedLogoName.isNotEmpty()) {
            val savedIndex = logosList.indexOfFirst { it.name == savedLogoName }
            if (savedIndex >= 0) {
                binding.spinnerLogo.setSelection(savedIndex)
            }
        }

        // 设置选择监听器
        binding.spinnerLogo.onItemSelectedListener = object : android.widget.AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: android.widget.AdapterView<*>?, view: android.view.View?, position: Int, id: Long) {
                if (position >= 0 && position < logosList.size) {
                    val selectedLogo = logosList[position]

                    // 保存选择的Logo
                    sharedPrefs.edit()
                        .putString(KEY_SELECTED_LOGO_URL, selectedLogo.url)
                        .putString(KEY_SELECTED_LOGO_NAME, selectedLogo.name)
                        .apply()

                    Log.i("DUIX_NET", "Logo设置已保存: ${selectedLogo.name} -> ${selectedLogo.url}")
                    Toast.makeText(this@SettingsActivity, "Logo已设置为: ${selectedLogo.name}", Toast.LENGTH_SHORT).show()

                    // 立即更新设置界面的Logo显示
                    loadSettingsLogo()
                }
            }

            override fun onNothingSelected(parent: android.widget.AdapterView<*>?) {
                // 不处理
            }
        }

        Log.i("DUIX_NET", "Logo选择器更新完成，共${availableLogos.size}个可用Logo")
        Toast.makeText(this, "成功加载 ${availableLogos.size} 个Logo", Toast.LENGTH_SHORT).show()

        // 更新设置界面的Logo显示
        loadSettingsLogo()
    }

    /**
     * 加载设置界面左上角的动态Logo
     */
    private fun loadSettingsLogo() {
        val logoUrl = sharedPrefs.getString(KEY_SELECTED_LOGO_URL, "")
        val logoName = sharedPrefs.getString(KEY_SELECTED_LOGO_NAME, "")
        Log.i("DUIX_NET", "加载设置界面Logo: $logoUrl (名称: $logoName)")

        // 移除背景，只显示logo图片
        binding.ivAppIcon.background = null

        if (!logoUrl.isNullOrEmpty()) {
            // 加载用户选择的Logo
            try {
                Glide.with(this)
                    .load(logoUrl)
                    .centerInside()
                    .placeholder(binding.ivAppIcon.drawable) // 使用当前图片作为占位符
                    .error(R.drawable.logo) // 加载失败时使用默认logo
                    .into(binding.ivAppIcon)
                Log.i("DUIX_NET", "✓ 设置界面已加载用户选择的Logo: $logoUrl")
            } catch (e: Exception) {
                Log.e("DUIX_NET", "设置界面Logo加载失败: ${e.message}", e)
                // 加载失败时使用默认logo
                binding.ivAppIcon.setImageResource(R.drawable.logo)
            }
        } else {
            // 使用默认logo
            binding.ivAppIcon.setImageResource(R.drawable.logo)
            Log.i("DUIX_NET", "✓ 设置界面已加载默认Logo")
        }
    }

    /**
     * 初始化多轮对话设置
     */
    private fun initMultiTurnSettings() {
        // 初始化多轮对话开关状态
        val multiTurnEnabled = sharedPrefs.getBoolean(KEY_MULTI_TURN_ENABLED, true)  // 默认开启
        binding.switchMultiTurn?.isChecked = multiTurnEnabled

        // 监听开关变化并保存
        binding.switchMultiTurn?.setOnCheckedChangeListener { _, isChecked ->
            sharedPrefs.edit().putBoolean(KEY_MULTI_TURN_ENABLED, isChecked).apply()
            // 更新超时时间设置的可用状态
            updateMultiTurnTimeoutEnabled(isChecked)
            Log.i("DUIX_NET", "多轮对话设置已保存: $isChecked")
        }

        // 初始化多轮对话超时时间
        val multiTurnTimeout = sharedPrefs.getInt(KEY_MULTI_TURN_TIMEOUT, DEFAULT_MULTI_TURN_TIMEOUT)
        val seekBarProgress = (multiTurnTimeout - 10).coerceIn(0, 110)  // 10-120秒映射到0-110
        binding.seekbarMultiTurnTimeout?.progress = seekBarProgress
        binding.tvMultiTurnTimeoutValue?.text = "${multiTurnTimeout}秒"

        // 监听超时时间滑块变化
        binding.seekbarMultiTurnTimeout?.setOnSeekBarChangeListener(object : android.widget.SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: android.widget.SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val timeoutSeconds = 10 + progress  // 10-120秒
                    binding.tvMultiTurnTimeoutValue?.text = "${timeoutSeconds}秒"
                }
            }
            override fun onStartTrackingTouch(seekBar: android.widget.SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: android.widget.SeekBar?) {
                val progress = seekBar?.progress ?: 0
                val timeoutSeconds = 10 + progress
                sharedPrefs.edit().putInt(KEY_MULTI_TURN_TIMEOUT, timeoutSeconds).apply()
                Log.i("DUIX_NET", "多轮对话超时时间已保存: ${timeoutSeconds}秒")
            }
        })

        // 初始化超时时间设置的可用状态
        updateMultiTurnTimeoutEnabled(multiTurnEnabled)

        Log.i("DUIX_NET", "多轮对话设置初始化完成，开启: $multiTurnEnabled, 超时: ${multiTurnTimeout}秒")
    }

    /**
     * 更新多轮对话超时时间设置的可用状态
     */
    private fun updateMultiTurnTimeoutEnabled(enabled: Boolean) {
        binding.seekbarMultiTurnTimeout?.isEnabled = enabled
        binding.tvMultiTurnTimeoutValue?.alpha = if (enabled) 1.0f else 0.5f
        binding.tvMultiTurnTimeoutLabel?.alpha = if (enabled) 1.0f else 0.5f
        binding.tvMultiTurnTimeoutDesc?.alpha = if (enabled) 1.0f else 0.5f
    }

}
