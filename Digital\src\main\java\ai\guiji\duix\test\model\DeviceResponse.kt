package ai.guiji.duix.test.model

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 设备API响应数据模型
 */
data class DeviceResponse(
    @JsonProperty("id")
    val id: Int,
    
    @JsonProperty("name")
    val name: String,
    
    @JsonProperty("description")
    val description: String,
    
    @JsonProperty("created_at")
    val createdAt: String,
    
    @JsonProperty("updated_at")
    val updatedAt: String,
    
    @JsonProperty("is_active")
    val isActive: Boolean,
    
    @JsonProperty("apps")
    val apps: List<AppInfo>
)

/**
 * 应用信息数据模型
 */
data class AppInfo(
    @JsonProperty("id")
    val id: Int,
    
    @JsonProperty("name")
    val name: String,
    
    @JsonProperty("description")
    val description: String,
    
    @JsonProperty("api_key")
    val apiKey: String,
    
    @JsonProperty("created_at")
    val createdAt: String,
    
    @JsonProperty("updated_at")
    val updatedAt: String
) {
    /**
     * 用于下拉框显示的文本
     */
    override fun toString(): String {
        return name
    }
}
