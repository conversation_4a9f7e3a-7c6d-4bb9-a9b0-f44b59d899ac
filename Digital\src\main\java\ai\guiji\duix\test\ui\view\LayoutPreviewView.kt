/**
 * 布局预览视图
 * 用于在布局调整界面中实时预览各个UI元素的位置
 */
package ai.guiji.duix.test.ui.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat
import ai.guiji.duix.test.R

class LayoutPreviewView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 画笔
    private val backgroundPaint = Paint().apply {
        color = Color.parseColor("#1A1A1A") // 深色背景
        style = Paint.Style.FILL
    }
    
    private val elementPaint = Paint().apply {
        style = Paint.Style.FILL
    }
    
    private val textPaint = Paint().apply {
        color = Color.WHITE
        textSize = 24f
        textAlign = Paint.Align.CENTER
    }
    
    private val outlinePaint = Paint().apply {
        style = Paint.Style.STROKE
        color = Color.WHITE
        strokeWidth = 1f
    }
    
    // 各元素位置百分比
    private var openingStatementPosition = 0.35f
    private var greetingQuestionsPosition = 0.72f
    private var greetingQuestionsWidth = 0.30f  // 新增：推荐问题宽度
    private var userQuestionPosition = 0.45f
    private var aiAnswerPosition = 0.55f
    private var bottomSuggestionsPosition = 0.88f
    private var recordButtonPosition = 0.25f
    private var interruptHintPosition = 0.40f
    
    // 元素颜色
    private val openingStatementColor = Color.parseColor("#4CAF50") // 绿色
    private val greetingQuestionsColor = Color.parseColor("#2196F3") // 蓝色
    private val userQuestionColor = Color.parseColor("#FFC107") // 黄色
    private val aiAnswerColor = Color.parseColor("#9C27B0") // 紫色
    private val bottomSuggestionsColor = Color.parseColor("#FF5722") // 橙色
    private val recordButtonColor = Color.parseColor("#E91E63") // 粉色
    private val interruptHintColor = Color.parseColor("#00BCD4") // 青色
    
    // 数字人轮廓
    private val digitalHumanOutline = RectF()
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        val width = width.toFloat()
        val height = height.toFloat()
        
        // 绘制背景
        canvas.drawRect(0f, 0f, width, height, backgroundPaint)
        
        // 绘制数字人轮廓（居中）
        val humanWidth = width * 0.6f
        val humanHeight = height * 0.7f
        digitalHumanOutline.set(
            (width - humanWidth) / 2,
            height * 0.1f,
            (width + humanWidth) / 2,
            height * 0.8f
        )
        outlinePaint.color = Color.parseColor("#80FFFFFF") // 半透明白色
        canvas.drawRect(digitalHumanOutline, outlinePaint)
        canvas.drawText("数字人区域", width / 2, height * 0.45f, textPaint)
        
        // 绘制开场白卡片
        drawElement(canvas, "开场白", openingStatementPosition, 0.15f, openingStatementColor)
        
        // 绘制问候问题（支持动态宽度）
        drawElementWithWidth(canvas, "问候问题", greetingQuestionsPosition, 0.1f, greetingQuestionsColor, greetingQuestionsWidth)
        
        // 绘制用户问题
        drawElement(canvas, "用户问题", userQuestionPosition, 0.08f, userQuestionColor)
        
        // 绘制AI回答
        drawElement(canvas, "AI回答", aiAnswerPosition, 0.12f, aiAnswerColor)
        
        // 绘制底部推荐问题
        drawElement(canvas, "推荐问题", bottomSuggestionsPosition, 0.08f, bottomSuggestionsColor)
        
        // 绘制录音按钮（右侧）
        drawButton(canvas, "录音", recordButtonPosition, recordButtonColor)
        
        // 绘制打断提示条
        drawElement(canvas, "打断提示", interruptHintPosition, 0.05f, interruptHintColor)
    }
    
    private fun drawElement(canvas: Canvas, text: String, yPosition: Float, height: Float, color: Int) {
        val width = width.toFloat()
        val y = yPosition * this.height

        elementPaint.color = color
        canvas.drawRect(width * 0.1f, y - 2, width * 0.9f, y + this.height * height, elementPaint)

        textPaint.color = Color.BLACK
        canvas.drawText(text, width / 2, y + this.height * height / 2, textPaint)
    }

    private fun drawElementWithWidth(canvas: Canvas, text: String, yPosition: Float, height: Float, color: Int, widthPercent: Float) {
        val width = width.toFloat()
        val y = yPosition * this.height
        val elementWidth = width * widthPercent

        elementPaint.color = color
        canvas.drawRect(width * 0.1f, y - 2, width * 0.1f + elementWidth, y + this.height * height, elementPaint)

        textPaint.color = Color.BLACK
        canvas.drawText(text, width * 0.1f + elementWidth / 2, y + this.height * height / 2, textPaint)
    }
    
    private fun drawButton(canvas: Canvas, text: String, yPosition: Float, color: Int) {
        val width = width.toFloat()
        val y = yPosition * this.height
        val buttonSize = width * 0.15f
        
        elementPaint.color = color
        canvas.drawCircle(width * 0.85f, y, buttonSize / 2, elementPaint)
        
        textPaint.color = Color.WHITE
        canvas.drawText(text, width * 0.85f, y + 4, textPaint)
    }
    
    // 更新各元素位置
    fun updatePositions(
        openingStatement: Float,
        greetingQuestions: Float,
        greetingQuestionsWidth: Float,
        userQuestion: Float,
        aiAnswer: Float,
        bottomSuggestions: Float,
        recordButton: Float,
        interruptHint: Float
    ) {
        this.openingStatementPosition = openingStatement
        this.greetingQuestionsPosition = greetingQuestions
        this.greetingQuestionsWidth = greetingQuestionsWidth
        this.userQuestionPosition = userQuestion
        this.aiAnswerPosition = aiAnswer
        this.bottomSuggestionsPosition = bottomSuggestions
        this.recordButtonPosition = recordButton
        this.interruptHintPosition = interruptHint

        invalidate()
    }
}