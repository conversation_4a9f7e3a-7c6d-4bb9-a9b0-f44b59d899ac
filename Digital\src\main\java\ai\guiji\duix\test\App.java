package ai.guiji.duix.test;

import android.text.TextUtils;
import android.util.Log;

import com.lodz.android.pandora.base.application.BaseApplication;

import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;

public class App extends BaseApplication {

    private static final String TAG = "App";
    public static App mApp;
    private static OkHttpClient mOkHttpClient;

    static {
        // 统一管理所有native库的加载，避免冲突
        loadNativeLibraries();
    }

    private static void loadNativeLibraries() {
        try {
            // 1. 首先加载SherpaOnnxKws的ONNX Runtime库
            System.loadLibrary("onnxruntime");
            Log.d(TAG, "Loaded onnxruntime library");
            
            // 2. 加载Sherpa相关库
            System.loadLibrary("sherpa-onnx-jni");
            Log.d(TAG, "Loaded sherpa-onnx-jni library");
            
            // 3. 加载duix-sdk的库（跳过它自己的ONNX Runtime）
            System.loadLibrary("scrfdncnn");
            Log.d(TAG, "Loaded scrfdncnn library");
            
        } catch (UnsatisfiedLinkError e) {
            Log.e(TAG, "Failed to load native libraries: " + e.getMessage(), e);
        }
    }


    public static OkHttpClient getOkHttpClient() {
        if (mOkHttpClient == null) {
            mOkHttpClient = new OkHttpClient.Builder()
                    .connectTimeout(15, TimeUnit.SECONDS)
                    .writeTimeout(15, TimeUnit.SECONDS)
                    .readTimeout(15, TimeUnit.SECONDS)
                    .build();
        }
        return mOkHttpClient;
    }

    public static String addBaseUrl(String url, String baseUrl) {
        String u = url;
        if (!TextUtils.isEmpty(u) && !u.startsWith("http")) {
            u = baseUrl + u;
        }
        return u;
    }

    @Override
    public void onExit() {

    }

    @Override
    public void onStartCreate() {
        mApp = this;
    }
}
