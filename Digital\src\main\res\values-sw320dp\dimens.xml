<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 小屏幕设备适配 - 针对320dp及以上的小屏幕设备 -->
    
    <!-- 文字尺寸 - 在小屏幕上适当减小以确保文字能完整显示 -->
    <dimen name="text_size_caption">11sp</dimen>
    <dimen name="text_size_body">13sp</dimen>
    <dimen name="text_size_subtitle">14sp</dimen>
    <dimen name="text_size_title">18sp</dimen>
    <dimen name="text_size_headline">22sp</dimen>
    <dimen name="text_size_button">12sp</dimen>  <!-- 从14sp减小到12sp -->
    
    <!-- 按钮相关 - 在小屏幕上保持合适的高度但减小内边距 -->
    <dimen name="button_height_small">32dp</dimen>
    <dimen name="button_height_medium">44dp</dimen>  <!-- 从48dp减小到44dp -->
    <dimen name="button_height_large">52dp</dimen>
    
    <!-- 基础间距 - 在小屏幕上适当减小间距以节省空间 -->
    <dimen name="spacing_tiny">3dp</dimen>
    <dimen name="spacing_small">6dp</dimen>
    <dimen name="spacing_medium">10dp</dimen>
    <dimen name="spacing_large">14dp</dimen>
    <dimen name="spacing_xlarge">20dp</dimen>
    <dimen name="spacing_xxlarge">28dp</dimen>
</resources>
