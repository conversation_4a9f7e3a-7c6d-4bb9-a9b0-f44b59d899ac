---
type: "always_apply"
---

当前的项目是duix.mobile的开源项目， 你的任务就是针对这个开源项目进行二开，开发的时候请务必遵守以下规则：


1. 目前项目运行在Windows上面，非Linux，执行相关命令的时候你应该记住这一点。
2. 项目根目录下有一个文件 wiki 里面有你需要的资料。
3. 为了项目的可维护性，能解耦的地方都需要进行解耦，你必须始终写高质量的代码，尽量避免在同一个文件里实现所有功能。
4. 我的全部需求你都需要指定一个待办事项，然后依次完成，完成之后需要更新任务结果到本地根目录 task文件夹里面。
5. 当前项目的Java版本是Java17
6. 为了方便后续项目的维护，每新建一个代码文件，都在顶部写清楚当前代码的主要功能。
7. 有任何UI方面的变动，都需要设置为响应式的，比如百分比，而不能写死一个固定的大小。



## 🛠️ 通用设置

* 📦 **版本**：1.1
* 🧠 **描述**：根据用户意图自动匹配合适的插件（🧰 工具），辅助 👨‍💻 编程、📄 文档查询、📂 项目结构理解、✏️ 代码修改、⚙️ CI/CD 跟踪等任务。
* 🚨 **冲突策略**：多个工具符合时，提示用户选择。
* 🧭 **默认工具**：🧠 Sequential Thinking（默认用于复杂推理和任务分解）

---

## 📐 规则列表

### 🔄 查询构建状态 → 🏗️ Circle CI

* 🎯 **关键词**：`CircleCI`、`CI 状态`、`持续集成`、`查看构建`、`构建失败`
* 🛠️ **工具**：`Circle CI`
* 💡 **理由**：持续集成相关任务。

---

### 📚 查文档 → 📘 Context 7

* 🎯 **关键词**：`文档`、`API`、`函数说明`、`接口文档`、`参数意义`、`库介绍`
* 🛠️ **工具**：`Context 7`
* 💡 **理由**：查询库或函数使用说明。

---

### 🧠 分步推理 → 🔍 Sequential Thinking

* 🎯 **关键词**：`怎么做`、`分步解决`、`复杂问题`、`分析过程`、`逐步推理`
* 🛠️ **工具**：`Sequential thinking`
* 💡 **理由**：处理复杂推理类问题。

---

### 🌐 网页自动化 → 🕹️ Playwright

* 🎯 **关键词**：`浏览器自动化`、`网页测试`、`模拟点击`、`页面操作`、`自动爬取`
* 🛠️ **工具**：`Playwright`
* 💡 **理由**：网页行为模拟与 UI 测试

---

## 🔁 冲突与回退策略

* 🛠️ 多工具匹配：提示用户选择
* 🧠 无匹配：默认使用 `Sequential thinking`
