<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:padding="10dp">

        <TextView
            android:id="@+id/save_path_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/simple_save_path"
            android:textColor="@color/color_00a1d5" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/delete_all_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="10dp"
            android:paddingTop="5dp"
            android:paddingEnd="10dp"
            android:paddingBottom="5dp"
            android:text="@string/simple_delete_all" />

    </LinearLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/audio_rv"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <com.lodz.android.pandora.widget.base.NoDataLayout
            android:id="@+id/audio_no_data_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <com.lodz.android.pandora.widget.base.LoadingLayout
            android:id="@+id/audio_loading_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </FrameLayout>

</LinearLayout>