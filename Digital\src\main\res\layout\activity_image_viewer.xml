<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">

    <!-- 图片查看器 - 支持缩放和手势操作 -->
    <ImageView
        android:id="@+id/iv_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:scaleType="fitCenter" />

    <!-- 加载指示器 -->
    <ProgressBar
        android:id="@+id/pb_loading"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_centerInParent="true"
        android:indeterminateTint="@color/digital_primary_light"
        android:visibility="visible" />

    <!-- 顶部工具栏 -->
    <LinearLayout
        android:id="@+id/ll_toolbar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_alignParentTop="true"
        android:background="@drawable/bg_digital_overlay"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="8dp">

        <!-- 返回按钮 -->
        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/bg_digital_icon_button"
            android:padding="8dp"
            android:src="@drawable/ic_arrow_back"
            android:contentDescription="返回" />

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            android:text="图片查看"
            android:textColor="@color/digital_text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end" />

        <!-- 下载按钮 -->
        <ImageView
            android:id="@+id/iv_download"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/bg_digital_icon_button"
            android:padding="8dp"
            android:src="@drawable/ic_download"
            android:contentDescription="下载" />

    </LinearLayout>

    <!-- 底部信息栏 -->
    <LinearLayout
        android:id="@+id/ll_bottom_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@drawable/bg_digital_overlay"
        android:orientation="vertical"
        android:padding="12dp"
        android:visibility="gone">

        <TextView
            android:id="@+id/tv_image_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="图片信息"
            android:textColor="@color/digital_text_secondary"
            android:textSize="12sp"
            android:maxLines="2"
            android:ellipsize="end" />

    </LinearLayout>

    <!-- 错误提示 -->
    <LinearLayout
        android:id="@+id/ll_error"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <ImageView
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:src="@drawable/ic_error"
            android:tint="@color/digital_text_secondary" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="图片加载失败"
            android:textColor="@color/digital_text_secondary"
            android:textSize="14sp" />

        <Button
            android:id="@+id/btn_retry"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="重试"
            android:textColor="@color/digital_text_primary"
            android:background="@drawable/bg_digital_button" />

    </LinearLayout>
    
    <!-- 页码指示器 -->
    <TextView
        android:id="@+id/tv_page_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="20dp"
        android:background="@drawable/bg_digital_overlay"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        android:text="1/1"
        android:textColor="@color/digital_text_primary"
        android:textSize="14sp"
        android:visibility="gone" />
    
    <!-- 滑动提示 -->
    <TextView
        android:id="@+id/tv_swipe_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/bg_digital_overlay"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:text="左右滑动查看更多图片"
        android:textColor="@color/digital_text_primary"
        android:textSize="16sp"
        android:visibility="gone" />

</RelativeLayout>
