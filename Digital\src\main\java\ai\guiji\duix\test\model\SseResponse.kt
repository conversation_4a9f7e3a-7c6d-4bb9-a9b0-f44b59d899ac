package ai.guiji.duix.test.model

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

/**
 * SSE响应数据模型
 * 用于解析服务器返回的Server-Sent Events数据
 * 
 * 支持的事件类型：
 * - message: 包含答案文本和音频URL
 * - parameters: 包含开场白和推荐问题
 * - data: 包含推荐问题数组
 */

/**
 * SSE消息基础模型
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class SseMessage(
    @JsonProperty("event")
    val event: String? = null,
    
    @JsonProperty("answer")
    val answer: String? = null,
    
    @JsonProperty("question")
    val question: String? = null,
    
    @JsonProperty("greeting")
    val greeting: Boolean? = null,
    
    @JsonProperty("status")
    val status: String? = null,
    
    @JsonProperty("url")
    val url: String? = null,
    
    @JsonProperty("opening_statement")
    val openingStatement: String? = null,
    
    @JsonProperty("suggested_questions")
    val suggestedQuestions: List<String>? = null,
    
    @JsonProperty("data")
    val data: List<String>? = null,

    @JsonProperty("link_data")
    val linkData: LinkData? = null
)

/**
 * 开场白和推荐问题数据模型
 * 用于parameters事件的数据解析
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class ParametersData(
    @JsonProperty("opening_statement")
    val openingStatement: String,
    
    @JsonProperty("suggested_questions")
    val suggestedQuestions: List<String>
)

/**
 * 消息事件数据模型
 * 用于message事件的数据解析
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class MessageData(
    @JsonProperty("event")
    val event: String,
    
    @JsonProperty("answer")
    val answer: String? = null,
    
    @JsonProperty("question")
    val question: String? = null,
    
    @JsonProperty("greeting")
    val greeting: Boolean? = null,
    
    @JsonProperty("status")
    val status: String,
    
    @JsonProperty("url")
    val url: String? = null
)

/**
 * 链接数据模型
 * 用于image_link和video_link事件的数据解析
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class LinkData(
    @JsonProperty("title")
    val title: String,

    @JsonProperty("url")
    val url: String
)

/**
 * 缓存问题响应数据模型
 * 用于 /v2/get-cached-questions 接口的响应解析
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class CachedQuestionsResponse(
    @JsonProperty("success")
    val success: Boolean,

    @JsonProperty("questions")
    val questions: List<String>,

    @JsonProperty("count")
    val count: Int,

    @JsonProperty("cache_key")
    val cacheKey: String
)

/**
 * SSE事件类型枚举
 */
enum class SseEventType(val value: String) {
    MESSAGE("message"),
    PARAMETERS("parameters"),
    DATA("data"),
    IMAGE_LINK("image_link"),
    VIDEO_LINK("video_link");

    companion object {
        fun fromValue(value: String?): SseEventType? {
            return values().find { it.value == value }
        }
    }
}

/**
 * SSE消息状态枚举
 */
enum class SseStatus(val value: String) {
    OK("ok"),
    READY("ready"),
    ERROR("error");
    
    companion object {
        fun fromValue(value: String?): SseStatus? {
            return values().find { it.value == value }
        }
    }
}
