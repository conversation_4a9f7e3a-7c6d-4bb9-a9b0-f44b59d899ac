<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 背景图片 -->
    <ImageView
        android:id="@+id/iv_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop" />

    <!-- 渐变遮罩层 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_digital_overlay" />

    <!-- 数字人渲染视图 -->
    <ai.guiji.duix.sdk.client.render.DUIXTextureView
        android:id="@+id/glTextureView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!-- 顶部工具栏 - 响应式百分比定位 -->
    <LinearLayout
        android:id="@+id/ll_top_toolbar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/guideline_top_toolbar">

        <!-- Logo图标 - 默认显示 -->
        <ImageView
            android:id="@+id/iv_logo"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:contentDescription="菜单"
            android:padding="8dp"
            android:src="@drawable/logo"
            android:scaleType="centerInside"
            android:clickable="true"
            android:focusable="true" />

        <!-- 功能菜单容器 - 默认隐藏 -->
        <LinearLayout
            android:id="@+id/ll_menu_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:visibility="gone"
            android:layout_marginStart="12dp">

            <!-- 设置图标 -->
            <ImageButton
                android:id="@+id/btn_settings"
                android:layout_width="56dp"
                android:layout_height="56dp"
                android:layout_marginEnd="12dp"
                android:background="@drawable/bg_digital_icon_button"
                android:contentDescription="设置"
                android:padding="16dp"
                android:src="@drawable/ic_settings"
                android:tint="@color/digital_text_primary" />

            <!-- 模型选择图标 -->
            <ImageButton
                android:id="@+id/btn_model"
                android:layout_width="56dp"
                android:layout_height="56dp"
                android:layout_marginEnd="12dp"
                android:background="@drawable/bg_digital_icon_button"
                android:contentDescription="选择模型"
                android:padding="16dp"
                android:src="@drawable/ic_model"
                android:tint="@color/digital_text_primary" />

            <!-- 背景选择图标 -->
            <ImageButton
                android:id="@+id/btn_background"
                android:layout_width="56dp"
                android:layout_height="56dp"
                android:layout_marginEnd="12dp"
                android:background="@drawable/bg_digital_icon_button"
                android:contentDescription="选择背景"
                android:padding="16dp"
                android:src="@drawable/ic_image"
                android:tint="@color/digital_text_primary" />

            <!-- 重置按钮 -->
            <ImageButton
                android:id="@+id/btn_reset"
                android:layout_width="56dp"
                android:layout_height="56dp"
                android:layout_marginEnd="12dp"
                android:background="@drawable/bg_digital_icon_button"
                android:contentDescription="重置界面"
                android:padding="16dp"
                android:src="@android:drawable/ic_menu_revert"
                android:tint="@color/digital_text_primary" />

            <!-- 布局调整按钮 -->
            <ImageButton
                android:id="@+id/btn_layout_adjustment"
                android:layout_width="56dp"
                android:layout_height="56dp"
                android:background="@drawable/bg_digital_icon_button"
                android:contentDescription="布局调整"
                android:padding="16dp"
                android:src="@drawable/ic_tune"
                android:tint="@color/digital_text_primary" />

        </LinearLayout>

    </LinearLayout>

    <!-- 左侧推荐问题区域 - 约束到开场白问题基准线 -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/suggested_questions_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="20dp"
        android:maxHeight="240dp"
        android:visibility="gone"
        app:layout_constraintWidth_percent="0.5"
        app:layout_constraintStart_toStartOf="@id/guideline_greeting_questions_left"
        app:layout_constraintTop_toTopOf="@id/guideline_greeting_questions"
        app:layout_constraintVertical_bias="0.0">

        <LinearLayout
            android:id="@+id/ll_suggested_questions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="8dp" />

    </androidx.core.widget.NestedScrollView>



    <!-- 欢迎卡片 - 响应式百分比定位 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/cv_opening_statement"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:visibility="gone"
        app:cardBackgroundColor="@android:color/transparent"
        app:cardCornerRadius="24dp"
        app:cardElevation="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/guideline_opening_statement">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_digital_welcome_card"
            android:orientation="vertical"
            android:padding="32dp">

            <TextView
                android:id="@+id/tv_opening_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="💬 说出 &quot;小灵小灵&quot; 开始对话"
                android:textColor="@color/digital_text_primary"
                android:textSize="22sp"
                android:textStyle="bold"
                android:letterSpacing="0.05" />

            <TextView
                android:id="@+id/tv_opening_statement"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="center"
                android:lineSpacingExtra="6dp"
                android:textColor="@color/digital_text_secondary"
                android:textSize="16sp" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 历史问题标签云 - 3行左右滚动 -->
    <LinearLayout
        android:id="@+id/cv_history_questions"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:layout_marginTop="16dp"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cv_opening_statement">

        <!-- 第1行：向左滚动 -->
        <HorizontalScrollView
            android:id="@+id/hsv_history_row_1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:scrollbars="none"
            android:fadingEdge="horizontal"
            android:fadingEdgeLength="20dp">

            <LinearLayout
                android:id="@+id/ll_history_row_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingStart="10dp"
                android:paddingEnd="10dp" />

        </HorizontalScrollView>

        <!-- 第2行：向右滚动 -->
        <HorizontalScrollView
            android:id="@+id/hsv_history_row_2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:scrollbars="none"
            android:fadingEdge="horizontal"
            android:fadingEdgeLength="20dp">

            <LinearLayout
                android:id="@+id/ll_history_row_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingStart="10dp"
                android:paddingEnd="10dp" />

        </HorizontalScrollView>

        <!-- 第3行：向左滚动 -->
        <HorizontalScrollView
            android:id="@+id/hsv_history_row_3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none"
            android:fadingEdge="horizontal"
            android:fadingEdgeLength="20dp">

            <LinearLayout
                android:id="@+id/ll_history_row_3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingStart="10dp"
                android:paddingEnd="10dp" />

        </HorizontalScrollView>

    </LinearLayout>

    <!-- 底部LLM建议问题列表 - 响应式百分比定位 -->
    <LinearLayout
        android:id="@+id/ll_llm_suggestions_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:orientation="horizontal"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/guideline_bottom_suggestions">

        <!-- 三个建议问题按钮将通过代码动态添加 -->

    </LinearLayout>

    <!-- 打断提示条 - 大气样式，类似开场白 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/cv_interrupt_hint"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="16dp"
        android:visibility="gone"
        app:cardBackgroundColor="@android:color/transparent"
        app:cardCornerRadius="24dp"
        app:cardElevation="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/guideline_interrupt_hint"
        app:layout_constraintBottom_toTopOf="@id/cv_conversation">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_digital_welcome_card"
            android:orientation="vertical"
            android:id="@+id/ll_interrupt_hint_container">

            <TextView
                android:id="@+id/tv_interrupt_hint"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="请点击红色按钮进行打断"
                android:textColor="@color/digital_text_primary"
                android:textSize="22sp"
                android:textStyle="bold"
                android:letterSpacing="0.05"
                android:lineSpacingExtra="6dp" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 统一对话框区域 - 包含用户问题和AI回复 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/cv_conversation"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:visibility="gone"
        app:cardBackgroundColor="@android:color/transparent"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/guideline_user_question"
        app:layout_constraintBottom_toTopOf="@id/guideline_ai_answer_end">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_digital_welcome_card"
            android:orientation="vertical">

            <!-- 标题栏 - 显示用户的实际问题 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:padding="16dp">

                <TextView
                    android:id="@+id/tv_conversation_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="用户的问题"
                    android:textColor="@android:color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:gravity="center_horizontal" />

            </LinearLayout>

            <!-- 分隔线 -->
            <View
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:background="#60FFFFFF" />

            <!-- 内容区域 -->
            <androidx.core.widget.NestedScrollView
                android:id="@+id/nested_scroll_view_conversation"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:fillViewport="false"
                android:scrollbars="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- AI回答区域 - 直接显示内容，不需要标签 -->
                    <LinearLayout
                        android:id="@+id/ll_ai_answer_section"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <!-- AI回答显示（支持Markdown） -->
                        <TextView
                            android:id="@+id/tv_ai_answer"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textColor="@color/digital_text_primary"
                            android:textSize="15sp"
                            android:lineSpacingExtra="4dp" />

                        <!-- 媒体展示区域 -->
                        <LinearLayout
                            android:id="@+id/media_container"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:layout_marginTop="16dp"
                            android:visibility="gone">

                            <!-- 媒体标题和滑动提示 -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:layout_marginBottom="8dp">

                                <TextView
                                    android:id="@+id/tv_media_title"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="相关媒体"
                                    android:textColor="@color/digital_text_secondary"
                                    android:textSize="14sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="← 左右滑动预览(点开查看大图/视频，会终止播放) →"
                                    android:textColor="@color/digital_text_hint"
                                    android:textSize="12sp"
                                    android:textStyle="italic" />

                            </LinearLayout>

                            <!-- 媒体横向滚动区域 -->
                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/rv_media_items"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:scrollbars="horizontal" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </androidx.core.widget.NestedScrollView>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 音色选择（隐藏） -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_voice_type"
        android:visibility="gone"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:hint="选择音色"
        app:layout_constraintBottom_toTopOf="@id/btnPlay"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <AutoCompleteTextView
            android:id="@+id/act_voice_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="none"
            android:textSize="14sp"
            android:padding="12dp"/>

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 播放按钮（隐藏） -->
    <Button
        android:id="@+id/btnPlay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:text="播放音频"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@id/btnRecord"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <!-- 状态指示器 -->
    <TextView
        android:id="@+id/tv_status_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:text="准备就绪"
        android:textColor="@color/digital_text_primary"
        android:textSize="14sp"
        android:textStyle="bold"
        android:background="@drawable/bg_digital_status_indicator"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/btnRecord"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />



    <!-- 响应式百分比基准线系统 -->

    <!-- 顶部工具栏基准线 -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_top_toolbar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="@dimen/top_toolbar_position_percent" />

    <!-- 开场白位置基准线 -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_opening_statement"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="@dimen/opening_statement_position_percent" />

    <!-- 打断提示条位置基准线 -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_interrupt_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="@dimen/interrupt_hint_position_percent" />

    <!-- 用户问题位置基准线 -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_user_question"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="@dimen/user_question_position_percent" />

    <!-- AI回复区域基准线 -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_ai_answer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="@dimen/ai_answer_position_percent" />

    <!-- AI回复区域结束基准线 - 控制AI回复框高度 -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_ai_answer_end"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="@dimen/ai_answer_end_percent" />

    <!-- 底部推荐问题基准线 -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_bottom_suggestions"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="@dimen/bottom_suggestions_position_percent" />

    <!-- 录音按钮基准线 -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_record_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="@dimen/record_button_position_percent" />

    <!-- 开场白左侧3个问题位置基准线 -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_greeting_questions"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="@dimen/greeting_questions_top_percent" />

    <!-- 开场白左侧3个问题左边距基准线 -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_greeting_questions_left"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="@dimen/greeting_questions_left_percent" />

    <!-- 录音按钮 - 移动到右上角偏下位置 -->
    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnRecord"
        android:layout_width="72dp"
        android:layout_height="72dp"
        android:text="🎤"
        android:textColor="@color/digital_text_primary"
        android:textSize="24sp"
        android:textStyle="bold"
        android:background="@drawable/bg_digital_record_button"
        android:elevation="12dp"
        android:layout_marginEnd="24dp"
        app:layout_constraintTop_toTopOf="@id/guideline_record_button"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 音频波浪可视化组件 - 屏幕底部 -->
    <ai.guiji.duix.test.ui.view.AudioWaveView
        android:id="@+id/audio_wave_view"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_marginBottom="0dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>