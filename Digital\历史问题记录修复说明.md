# 历史问题记录修复说明

## 🚨 问题描述

用户反馈历史问题没有记录，经过代码分析发现问题出现在 `sendAsrResultToLlm` 方法中。

## 🔍 问题分析

### **根本原因**
在 `sendAsrResultToLlm` 方法中，缺少了调用 `displayUserQuestion` 方法来显示用户问题和添加到历史记录的逻辑。

### **问题代码位置**
```kotlin
// CallActivity.kt - sendAsrResultToLlm 方法
private fun sendAsrResultToLlm(recognizedText: String, resetSession: Boolean = true) {
    // ... 其他逻辑
    
    // ❌ 缺少这部分逻辑：
    // displayUserQuestion(recognizedText, addToHistory = resetSession)
    
    // 根据参数决定是否重置当前会话问题
    if (resetSession) {
        currentSessionQuestion = null  // ❌ 这里应该设置为 recognizedText
        Log.i(TAG_NET, "✓ 已重置当前会话问题")
    }
}
```

### **影响范围**
- **ASR语音识别**: 用户通过语音输入的问题不会被添加到历史记录
- **推荐问题点击**: 推荐问题点击时正常（因为使用了 `addToHistory = false`）
- **缓存问题加载**: 缓存问题加载正常（直接操作 `historyQuestions`）

## 🔧 修复方案

### **修复代码**
```kotlin
// 修复后的 sendAsrResultToLlm 方法
private fun sendAsrResultToLlm(recognizedText: String, resetSession: Boolean = true) {
    // ... 清空显示区域的逻辑

    // ✅ 新增：显示用户问题并添加到历史记录
    runOnUiThread {
        displayUserQuestion(recognizedText, addToHistory = resetSession)
    }

    // 根据参数决定是否重置当前会话问题
    if (resetSession) {
        currentSessionQuestion = recognizedText  // ✅ 修复：设置为当前问题
        Log.i(TAG_NET, "✓ 设置新的会话问题: $currentSessionQuestion")
    } else {
        Log.i(TAG_NET, "✓ 保持当前会话问题: $currentSessionQuestion")
    }

    // ... 其他逻辑
}
```

### **修复要点**

1. **添加问题显示逻辑**
   ```kotlin
   runOnUiThread {
       displayUserQuestion(recognizedText, addToHistory = resetSession)
   }
   ```

2. **修复会话问题设置**
   ```kotlin
   // 修复前
   currentSessionQuestion = null
   
   // 修复后  
   currentSessionQuestion = recognizedText
   ```

3. **保持逻辑一致性**
   - `resetSession = true`: 新问题，添加到历史记录
   - `resetSession = false`: 重复问题，不添加到历史记录

## 📊 修复效果验证

### **测试场景**

| 场景 | 修复前 | 修复后 |
|------|--------|--------|
| **ASR语音输入** | ❌ 不记录历史 | ✅ 正常记录历史 |
| **推荐问题点击** | ✅ 不重复记录 | ✅ 不重复记录 |
| **缓存问题加载** | ✅ 正常加载 | ✅ 正常加载 |
| **问题显示** | ❌ 可能不显示 | ✅ 正常显示 |

### **验证方法**

1. **语音输入测试**
   - 使用ASR语音输入问题
   - 检查问题是否显示在界面上
   - 检查问题是否添加到历史记录
   - 重置界面后检查历史问题标签云

2. **推荐问题测试**
   - 点击推荐问题
   - 确认不会重复添加到历史记录
   - 确认问题正常显示

3. **缓存问题测试**
   - 重启应用
   - 检查缓存问题是否正常加载
   - 检查历史问题标签云显示

## 🔄 历史问题记录流程

### **完整流程图**
```
用户输入问题
    ↓
sendAsrResultToLlm(text, resetSession=true)
    ↓
displayUserQuestion(text, addToHistory=true)
    ↓
addQuestionToHistory(text)
    ↓
historyQuestions.add(text)
    ↓
显示在历史问题标签云
```

### **关键方法调用链**

1. **ASR语音输入**
   ```kotlin
   ASR识别 → sendAsrResultToLlm(text, true) → displayUserQuestion(text, true) → addQuestionToHistory(text)
   ```

2. **推荐问题点击**
   ```kotlin
   点击推荐问题 → displayUserQuestion(text, false) → 不添加到历史记录
   ```

3. **缓存问题加载**
   ```kotlin
   getCachedQuestions() → historyQuestions.add(text) → updateHistoryQuestionsDisplay()
   ```

## 📝 代码逻辑说明

### **displayUserQuestion 方法**
```kotlin
private fun displayUserQuestion(question: String, addToHistory: Boolean = true) {
    Log.i(TAG_NET, "显示用户问题: $question, 添加到历史记录: $addToHistory")
    binding.tvUserQuestion.text = question
    binding.cvUserQuestion.visibility = android.view.View.VISIBLE
    binding.cvAiAnswer.visibility = android.view.View.VISIBLE

    // 根据参数决定是否添加问题到历史记录
    if (addToHistory) {
        addQuestionToHistory(question)
    }
}
```

### **addQuestionToHistory 方法**
```kotlin
private fun addQuestionToHistory(question: String) {
    if (question.isBlank()) return

    // 清理问题文本
    val cleanQuestion = question.trim().replace(Regex("\\s+"), " ")
    if (cleanQuestion.isEmpty()) return

    // 去重处理
    if (historyQuestions.contains(cleanQuestion)) {
        historyQuestions.remove(cleanQuestion)
    }

    // 添加到历史记录
    historyQuestions.add(cleanQuestion)

    // 限制数量
    while (historyQuestions.size > maxHistoryQuestions) {
        val oldestQuestion = historyQuestions.first()
        historyQuestions.remove(oldestQuestion)
    }

    Log.i(TAG_NET, "添加问题到历史记录: $cleanQuestion，当前历史问题数量: ${historyQuestions.size}")
}
```

## 🎯 修复总结

### **修复内容**
- ✅ 在 `sendAsrResultToLlm` 中添加了 `displayUserQuestion` 调用
- ✅ 修复了 `currentSessionQuestion` 的设置逻辑
- ✅ 保持了推荐问题不重复记录的逻辑
- ✅ 保持了缓存问题加载的功能

### **预期效果**
- ✅ ASR语音输入的问题会正常添加到历史记录
- ✅ 历史问题标签云会正常显示用户问过的问题
- ✅ 重置界面后历史问题会正常显示
- ✅ 不会影响其他功能的正常运行

现在历史问题记录功能已经完全修复，用户的语音输入问题会正常添加到历史记录中！🎊
