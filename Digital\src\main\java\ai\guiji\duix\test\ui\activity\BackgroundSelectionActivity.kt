/**
 * 背景选择Activity
 * 主要功能：从API获取背景媒体列表，提供横屏/竖屏筛选，支持背景选择和应用
 */
package ai.guiji.duix.test.ui.activity

import ai.guiji.duix.test.R
import ai.guiji.duix.test.config.ApiConfig
import ai.guiji.duix.test.databinding.ActivityBackgroundSelectionBinding
import ai.guiji.duix.test.model.MediaFilter
import ai.guiji.duix.test.model.MediaItem
import ai.guiji.duix.test.ui.adapter.BackgroundAdapter
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.recyclerview.widget.GridLayoutManager
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import okhttp3.Call
import okhttp3.Callback
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import java.io.IOException

class BackgroundSelectionActivity : BaseActivity() {

    companion object {
        const val PREF_NAME = "duix_settings"
        const val KEY_SELECTED_BACKGROUND_URL = "selected_background_url"
        const val KEY_SELECTED_BACKGROUND_ID = "selected_background_id"
        const val KEY_TTS_URL = "tts_url"
        const val DEFAULT_TTS_URL = ""
        private const val TAG_NET = "DUIX_NET"
    }

    private lateinit var binding: ActivityBackgroundSelectionBinding
    private val sharedPrefs by lazy { getSharedPreferences(PREF_NAME, MODE_PRIVATE) }
    private val okHttpClient = OkHttpClient()
    private val objectMapper = ObjectMapper()
    
    private lateinit var backgroundAdapter: BackgroundAdapter
    private var allMediaItems = mutableListOf<MediaItem>()
    private var filteredMediaItems = mutableListOf<MediaItem>()
    private var currentFilter = MediaFilter.HORIZONTAL
    private var selectedMediaItem: MediaItem? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.i(TAG_NET, "BackgroundSelectionActivity onCreate 开始")

        try {
            binding = ActivityBackgroundSelectionBinding.inflate(layoutInflater)
            setContentView(binding.root)
            Log.i(TAG_NET, "BackgroundSelectionActivity 布局设置完成")

            initViews()
            Log.i(TAG_NET, "BackgroundSelectionActivity initViews 完成")

            setupRecyclerView()
            Log.i(TAG_NET, "BackgroundSelectionActivity setupRecyclerView 完成")

            setupFilterChips()
            Log.i(TAG_NET, "BackgroundSelectionActivity setupFilterChips 完成")

            loadBackgrounds()
            Log.i(TAG_NET, "BackgroundSelectionActivity loadBackgrounds 开始")
        } catch (e: Exception) {
            Log.e(TAG_NET, "BackgroundSelectionActivity onCreate 失败: ${e.message}", e)
        }
    }

    private fun initViews() {
        // 刷新按钮
        binding.btnRefresh.setOnClickListener {
            Log.i(TAG_NET, "刷新按钮被点击")
            refreshBackgrounds()
        }

        // 应用背景按钮
        binding.btnApplyBackground.setOnClickListener {
            if (selectedMediaItem != null) {
                Log.i(TAG_NET, "应用背景: ${selectedMediaItem!!.name}")
                saveSelectedBackground()
                finish() // 直接返回，不显示toast
            } else {
                Toast.makeText(this, "请先选择一个背景", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun setupRecyclerView() {
        backgroundAdapter = BackgroundAdapter(filteredMediaItems) { mediaItem ->
            selectBackground(mediaItem)
        }
        
        binding.rvBackgrounds.apply {
            layoutManager = GridLayoutManager(this@BackgroundSelectionActivity, 2)
            adapter = backgroundAdapter
        }
    }

    private fun setupFilterChips() {
        binding.chipHorizontal.setOnClickListener { applyFilter(MediaFilter.HORIZONTAL) }
        binding.chipVertical.setOnClickListener { applyFilter(MediaFilter.VERTICAL) }
    }

    private fun loadBackgrounds() {
        Log.i(TAG_NET, "loadBackgrounds 开始")
        val baseUrl = sharedPrefs.getString(KEY_TTS_URL, DEFAULT_TTS_URL) ?: DEFAULT_TTS_URL
        val fullUrl = ApiConfig.buildUrl(baseUrl, ApiConfig.Media.MEDIA_ENDPOINT)

        Log.i(TAG_NET, "媒体API - 基础URL: $baseUrl")
        Log.i(TAG_NET, "媒体API - 完整URL: $fullUrl")

        val request = Request.Builder()
            .url(fullUrl)
            .get()
            .build()

        Log.i(TAG_NET, "开始发送媒体列表请求")
        okHttpClient.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e(TAG_NET, "媒体列表请求失败: ${e.message}", e)
                runOnUiThread {
                    Toast.makeText(this@BackgroundSelectionActivity, "加载背景列表失败: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
            
            override fun onResponse(call: Call, response: Response) {
                Log.i(TAG_NET, "媒体列表请求响应: ${response.code}")

                try {
                    if (response.isSuccessful) {
                        val responseBody = response.body?.string()
                        Log.i(TAG_NET, "媒体列表响应体长度: ${responseBody?.length ?: 0}")

                        if (responseBody != null) {
                            val mediaItems = objectMapper.readValue(responseBody, object : TypeReference<List<MediaItem>>() {})
                            Log.i(TAG_NET, "解析到 ${mediaItems.size} 个媒体项")

                            // 只显示 is_show 为 true 且 media_type 为 image 的媒体
                            val visibleImages = mediaItems.filter { it.isShow && it.mediaType == "image" }
                            Log.i(TAG_NET, "其中 ${visibleImages.size} 个图片可显示 (is_show=true, media_type=image)")

                            runOnUiThread {
                                allMediaItems.clear()
                                allMediaItems.addAll(visibleImages)

                                Log.i(TAG_NET, "应用筛选器: $currentFilter")
                                applyFilter(currentFilter)

                                // 恢复之前选中的背景
                                restoreSelectedBackground()
                            }
                        } else {
                            Log.w(TAG_NET, "响应体为空")
                            runOnUiThread {
                                Toast.makeText(this@BackgroundSelectionActivity, "服务器返回空数据", Toast.LENGTH_SHORT).show()
                            }
                        }
                    } else {
                        Log.e(TAG_NET, "媒体列表请求失败，响应码: ${response.code}")
                        runOnUiThread {
                            Toast.makeText(this@BackgroundSelectionActivity, "加载背景列表失败: ${response.code}", Toast.LENGTH_SHORT).show()
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG_NET, "解析媒体列表失败: ${e.message}", e)
                    runOnUiThread {
                        Toast.makeText(this@BackgroundSelectionActivity, "解析背景列表失败: ${e.message}", Toast.LENGTH_SHORT).show()
                    }
                }
            }
        })
    }

    /**
     * 刷新背景列表
     */
    private fun refreshBackgrounds() {
        Log.i(TAG_NET, "========== 刷新背景列表 ==========")

        // 显示刷新提示
        Toast.makeText(this, "正在刷新背景列表...", Toast.LENGTH_SHORT).show()

        // 清空当前数据
        allMediaItems.clear()
        filteredMediaItems.clear()
        selectedMediaItem = null
        backgroundAdapter.notifyDataSetChanged()

        // 重新加载背景
        loadBackgrounds()
    }

    private fun applyFilter(filter: MediaFilter) {
        Log.i(TAG_NET, "应用筛选器: ${filter.label}")
        currentFilter = filter
        filteredMediaItems.clear()
        val filtered = allMediaItems.filter { it.matchesFilter(filter) }
        filteredMediaItems.addAll(filtered)
        Log.i(TAG_NET, "筛选后背景数量: ${filteredMediaItems.size}")
        backgroundAdapter.notifyDataSetChanged()
    }

    private fun selectBackground(mediaItem: MediaItem) {
        selectedMediaItem = mediaItem
        backgroundAdapter.setSelectedItem(mediaItem)
    }

    private fun saveSelectedBackground() {
        selectedMediaItem?.let { mediaItem ->
            sharedPrefs.edit()
                .putString(KEY_SELECTED_BACKGROUND_URL, mediaItem.url)
                .putInt(KEY_SELECTED_BACKGROUND_ID, mediaItem.id)
                .apply()
            Log.i(TAG_NET, "保存选中的背景: ${mediaItem.url}")
        }
    }

    private fun restoreSelectedBackground() {
        val savedBackgroundUrl = sharedPrefs.getString(KEY_SELECTED_BACKGROUND_URL, "") ?: ""
        if (savedBackgroundUrl.isNotEmpty()) {
            val mediaItem = allMediaItems.find { it.url == savedBackgroundUrl }
            if (mediaItem != null) {
                selectBackground(mediaItem)
            }
        }
    }
}
