<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <!-- 标题 -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="选择模型"
        android:textColor="@color/black"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 刷新按钮 -->
    <ImageButton
        android:id="@+id/btn_refresh"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="16dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:contentDescription="刷新模型列表"
        android:padding="8dp"
        android:src="@android:drawable/ic_popup_sync"
        android:tint="@color/black"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintStart_toEndOf="@id/tv_title"
        app:layout_constraintTop_toTopOf="@id/tv_title" />

    <!-- 当前选中模型显示 -->
    <TextView
        android:id="@+id/tv_current_model"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:text="(当前模型：未选择)"
        android:textColor="@color/design_default_color_on_surface"
        android:textSize="14sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btn_refresh"
        app:layout_constraintTop_toTopOf="@id/tv_title" />

    <!-- 筛选标签 -->
    <HorizontalScrollView
        android:id="@+id/hsv_filters"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:scrollbars="none"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title">

        <com.google.android.material.chip.ChipGroup
            android:id="@+id/chip_group_filters"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:selectionRequired="true"
            app:singleSelection="true">

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_horizontal_male"
                style="@style/Widget.MaterialComponents.Chip.Filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                android:text="横屏-男" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_horizontal_female"
                style="@style/Widget.MaterialComponents.Chip.Filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="横屏-女" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_vertical_male"
                style="@style/Widget.MaterialComponents.Chip.Filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="竖屏-男" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_vertical_female"
                style="@style/Widget.MaterialComponents.Chip.Filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="竖屏-女" />

        </com.google.android.material.chip.ChipGroup>

    </HorizontalScrollView>

    <!-- 模型列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_models"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintBottom_toTopOf="@id/btn_enter_digital_human"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/hsv_filters" />

    <!-- 进入数字人页面按钮 -->
    <Button
        android:id="@+id/btn_enter_digital_human"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/btn_enter_digital_human"
        android:textSize="@dimen/text_size_button"
        android:autoSizeTextType="uniform"
        android:autoSizeMinTextSize="10sp"
        android:autoSizeMaxTextSize="@dimen/text_size_button"
        android:autoSizeStepGranularity="1sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
