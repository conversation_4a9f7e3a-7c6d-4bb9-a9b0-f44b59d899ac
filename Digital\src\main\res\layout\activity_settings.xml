<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/md3_background">

    <!-- 顶部标题区域 - 使用 ConstraintLayout 替代 LinearLayout 提高性能 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_header"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/spacing_large"
        android:paddingVertical="@dimen/spacing_medium"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:id="@+id/iv_app_icon"
            android:layout_width="@dimen/icon_size_large"
            android:layout_height="@dimen/icon_size_large"
            android:background="@drawable/ic_launcher_background"
            android:src="@drawable/ic_launcher_foreground"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="索灵AI"
            android:textColor="@color/md3_on_surface"
            android:textSize="@dimen/text_size_title"
            android:textStyle="bold"
            android:layout_marginStart="@dimen/spacing_medium"
            app:layout_constraintStart_toEndOf="@id/iv_app_icon"
            app:layout_constraintTop_toTopOf="@id/iv_app_icon"
            app:layout_constraintEnd_toStartOf="@id/tv_status_badge" />

        <TextView
            android:id="@+id/tv_sdk_version"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="SDK 3.0.3"
            android:textColor="@color/md3_on_surface_variant"
            android:textSize="@dimen/text_size_caption"
            android:layout_marginTop="@dimen/spacing_tiny"
            app:layout_constraintStart_toStartOf="@id/tv_title"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            app:layout_constraintEnd_toEndOf="@id/tv_title" />

        <TextView
            android:id="@+id/tv_status_badge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="已连接"
            android:textColor="@color/md3_success"
            android:textSize="@dimen/text_size_caption"
            android:background="@drawable/bg_status_badge"
            android:paddingHorizontal="@dimen/spacing_small"
            android:paddingVertical="@dimen/spacing_tiny"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fillViewport="true"
        android:scrollbars="none"
        android:clipToPadding="false"
        android:paddingBottom="@dimen/spacing_large"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layout_header"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/spacing_large"
            android:paddingTop="@dimen/spacing_medium">

            <!-- 基础配置卡片 - 使用 Material Design 3 Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_base_config"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/spacing_large"
                app:cardBackgroundColor="@color/md3_surface"
                app:cardCornerRadius="@dimen/card_corner_radius"
                app:cardElevation="@dimen/card_elevation"
                app:strokeWidth="0dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/card_padding">

                    <ImageView
                        android:id="@+id/iv_base_config_icon"
                        android:layout_width="@dimen/icon_size_medium"
                        android:layout_height="@dimen/icon_size_medium"
                        android:src="@android:drawable/ic_menu_manage"
                        android:tint="@color/md3_on_surface_variant"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_base_config_title"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="基础配置"
                        android:textColor="@color/md3_on_surface"
                        android:textSize="@dimen/text_size_subtitle"
                        android:textStyle="bold"
                        android:layout_marginStart="@dimen/spacing_small"
                        app:layout_constraintStart_toEndOf="@id/iv_base_config_icon"
                        app:layout_constraintTop_toTopOf="@id/iv_base_config_icon"
                        app:layout_constraintEnd_toEndOf="parent" />

                    <TextView
                        android:id="@+id/tv_config_status"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="所有的模型需要配置"
                        android:textColor="@color/md3_on_surface_variant"
                        android:textSize="@dimen/text_size_caption"
                        android:layout_marginTop="@dimen/spacing_small"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/iv_base_config_icon"
                        app:layout_constraintEnd_toEndOf="parent" />

                    <ProgressBar
                        android:id="@+id/progress_base_config"
                        style="@style/Widget.AppCompat.ProgressBar.Horizontal"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/progress_bar_height"
                        android:max="100"
                        android:progress="0"
                        android:progressTint="@color/md3_primary"
                        android:progressBackgroundTint="@color/md3_outline_variant"
                        android:layout_marginTop="@dimen/spacing_medium"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_config_status"
                        app:layout_constraintEnd_toEndOf="parent" />

                    <Button
                        android:id="@+id/btn_base_config_download"
                        style="@style/Widget.MaterialComponents.Button"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/download"
                        android:textSize="@dimen/text_size_button"
                        android:enabled="false"
                        android:layout_marginTop="@dimen/spacing_medium"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/progress_base_config" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 快速操作卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_quick_actions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/spacing_large"
                app:cardBackgroundColor="@color/md3_surface"
                app:cardCornerRadius="@dimen/card_corner_radius"
                app:cardElevation="@dimen/card_elevation"
                app:strokeWidth="0dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/card_padding">

                    <ImageView
                        android:id="@+id/iv_quick_actions_icon"
                        android:layout_width="@dimen/icon_size_medium"
                        android:layout_height="@dimen/icon_size_medium"
                        android:src="@android:drawable/ic_media_play"
                        android:tint="@color/md3_on_surface_variant"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_quick_actions_title"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="快速操作"
                        android:textColor="@color/md3_on_surface"
                        android:textSize="@dimen/text_size_subtitle"
                        android:textStyle="bold"
                        android:layout_marginStart="@dimen/spacing_small"
                        app:layout_constraintStart_toEndOf="@id/iv_quick_actions_icon"
                        app:layout_constraintTop_toTopOf="@id/iv_quick_actions_icon"
                        app:layout_constraintEnd_toEndOf="parent" />

                    <Button
                        android:id="@+id/btn_model_config"
                        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/button_height_medium"
                        android:text="配置模型"
                        android:textSize="@dimen/text_size_button"
                        android:layout_marginTop="@dimen/spacing_large"
                        android:layout_marginEnd="@dimen/spacing_small"
                        app:icon="@android:drawable/ic_menu_manage"
                        app:iconGravity="start"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/iv_quick_actions_icon"
                        app:layout_constraintEnd_toStartOf="@id/btn_model_play"
                        app:layout_constraintHorizontal_weight="1" />

                    <Button
                        android:id="@+id/btn_model_play"
                        style="@style/Widget.MaterialComponents.Button"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/button_height_medium"
                        android:text="@string/btn_enter_digital_human"
                        android:textSize="@dimen/text_size_button"
                        android:autoSizeTextType="uniform"
                        android:autoSizeMinTextSize="10sp"
                        android:autoSizeMaxTextSize="@dimen/text_size_button"
                        android:autoSizeStepGranularity="1sp"
                        android:layout_marginTop="@dimen/spacing_large"
                        android:layout_marginStart="@dimen/spacing_small"
                        app:icon="@android:drawable/ic_media_play"
                        app:iconGravity="start"
                        app:layout_constraintStart_toEndOf="@id/btn_model_config"
                        app:layout_constraintTop_toBottomOf="@id/iv_quick_actions_icon"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_weight="1" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 服务配置卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_service_config"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/spacing_large"
                app:cardBackgroundColor="@color/md3_surface"
                app:cardCornerRadius="@dimen/card_corner_radius"
                app:cardElevation="@dimen/card_elevation"
                app:strokeWidth="0dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/card_padding">

                    <ImageView
                        android:id="@+id/iv_service_config_icon"
                        android:layout_width="@dimen/icon_size_medium"
                        android:layout_height="@dimen/icon_size_medium"
                        android:src="@android:drawable/ic_menu_preferences"
                        android:tint="@color/md3_on_surface_variant"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_service_config_title"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="服务配置"
                        android:textColor="@color/md3_on_surface"
                        android:textSize="@dimen/text_size_subtitle"
                        android:textStyle="bold"
                        android:layout_marginStart="@dimen/spacing_small"
                        app:layout_constraintStart_toEndOf="@id/iv_service_config_icon"
                        app:layout_constraintTop_toTopOf="@id/iv_service_config_icon"
                        app:layout_constraintEnd_toEndOf="parent" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_tts_url"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:hint="TTS服务地址"
                        android:layout_marginTop="@dimen/spacing_large"
                        app:boxBackgroundMode="outline"
                        app:boxCornerRadiusBottomEnd="@dimen/input_corner_radius"
                        app:boxCornerRadiusBottomStart="@dimen/input_corner_radius"
                        app:boxCornerRadiusTopEnd="@dimen/input_corner_radius"
                        app:boxCornerRadiusTopStart="@dimen/input_corner_radius"
                        app:boxStrokeColor="@color/md3_outline"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/iv_service_config_icon"
                        app:layout_constraintEnd_toEndOf="parent">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_tts_url"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textUri"
                            android:text="http://14.19.140.88:8280/v1/tts"
                            android:textSize="@dimen/text_size_body"
                            android:textColor="@color/md3_on_surface" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_user_id"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:hint="用户ID (英文)"
                        android:layout_marginTop="@dimen/spacing_medium"
                        app:boxBackgroundMode="outline"
                        app:boxCornerRadiusBottomEnd="@dimen/input_corner_radius"
                        app:boxCornerRadiusBottomStart="@dimen/input_corner_radius"
                        app:boxCornerRadiusTopEnd="@dimen/input_corner_radius"
                        app:boxCornerRadiusTopStart="@dimen/input_corner_radius"
                        app:boxStrokeColor="@color/md3_outline"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/til_tts_url"
                        app:layout_constraintEnd_toEndOf="parent">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_user_id"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:textColor="@color/md3_on_surface"
                            android:textSize="@dimen/text_size_body" />

                    </com.google.android.material.textfield.TextInputLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 应用设置卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_app_settings"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/spacing_large"
                app:cardBackgroundColor="@color/md3_surface"
                app:cardCornerRadius="@dimen/card_corner_radius"
                app:cardElevation="@dimen/card_elevation"
                app:strokeWidth="0dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/card_padding">

                    <ImageView
                        android:id="@+id/iv_app_settings_icon"
                        android:layout_width="@dimen/icon_size_medium"
                        android:layout_height="@dimen/icon_size_medium"
                        android:src="@android:drawable/ic_menu_info_details"
                        android:tint="@color/md3_on_surface_variant"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_app_settings_title"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="应用设置"
                        android:textColor="@color/md3_on_surface"
                        android:textSize="@dimen/text_size_subtitle"
                        android:textStyle="bold"
                        android:layout_marginStart="@dimen/spacing_small"
                        app:layout_constraintStart_toEndOf="@id/iv_app_settings_icon"
                        app:layout_constraintTop_toTopOf="@id/iv_app_settings_icon"
                        app:layout_constraintEnd_toEndOf="parent" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_apikey"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:hint="选择应用"
                        android:layout_marginTop="@dimen/spacing_large"
                        android:minHeight="@dimen/button_height_large"
                        app:boxBackgroundMode="outline"
                        app:boxCornerRadiusBottomEnd="@dimen/input_corner_radius"
                        app:boxCornerRadiusBottomStart="@dimen/input_corner_radius"
                        app:boxCornerRadiusTopEnd="@dimen/input_corner_radius"
                        app:boxCornerRadiusTopStart="@dimen/input_corner_radius"
                        app:boxStrokeColor="@color/md3_outline"
                        app:boxStrokeWidth="2dp"
                        app:hintTextColor="@color/md3_primary"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/iv_app_settings_icon"
                        app:layout_constraintEnd_toEndOf="parent">

                        <AutoCompleteTextView
                            android:id="@+id/act_apikey"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:minHeight="@dimen/button_height_large"
                            android:inputType="none"
                            android:textColor="@color/md3_on_surface"
                            android:textSize="@dimen/text_size_subtitle"
                            android:padding="@dimen/spacing_medium"
                            android:gravity="center_vertical" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <Button
                        android:id="@+id/btn_refresh_apps"
                        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/button_height_medium"
                        android:text="刷新应用列表"
                        android:textSize="@dimen/text_size_body"
                        android:layout_marginTop="@dimen/spacing_large"
                        android:paddingStart="@dimen/spacing_medium"
                        android:paddingEnd="@dimen/spacing_medium"
                        android:paddingTop="@dimen/spacing_small"
                        android:paddingBottom="@dimen/spacing_small"
                        android:maxLines="1"
                        android:ellipsize="end"
                        app:icon="@android:drawable/ic_menu_rotate"
                        app:iconGravity="start"
                        app:iconSize="@dimen/icon_size_small"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/til_apikey" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 语音控制卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_voice_control"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/spacing_large"
                app:cardBackgroundColor="@color/md3_surface"
                app:cardCornerRadius="@dimen/card_corner_radius"
                app:cardElevation="@dimen/card_elevation"
                app:strokeWidth="0dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/card_padding">

                    <ImageView
                        android:id="@+id/iv_voice_control_icon"
                        android:layout_width="@dimen/icon_size_medium"
                        android:layout_height="@dimen/icon_size_medium"
                        android:src="@android:drawable/ic_btn_speak_now"
                        android:tint="@color/md3_on_surface_variant"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_voice_control_title"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="语音控制"
                        android:textColor="@color/md3_on_surface"
                        android:textSize="@dimen/text_size_subtitle"
                        android:textStyle="bold"
                        android:layout_marginStart="@dimen/spacing_small"
                        app:layout_constraintStart_toEndOf="@id/iv_voice_control_icon"
                        app:layout_constraintTop_toTopOf="@id/iv_voice_control_icon"
                        app:layout_constraintEnd_toEndOf="parent" />



                    <TextView
                        android:id="@+id/tv_local_asr_label"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="启用本地ASR"
                        android:textColor="@color/md3_on_surface"
                        android:textSize="@dimen/text_size_body"
                        android:layout_marginTop="@dimen/spacing_large"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_voice_control_title"
                        app:layout_constraintEnd_toStartOf="@id/switch_local_asr" />

                    <TextView
                        android:id="@+id/tv_local_asr_desc"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="使用本地语音识别引擎"
                        android:textColor="@color/md3_on_surface_variant"
                        android:textSize="@dimen/text_size_caption"
                        android:layout_marginTop="@dimen/spacing_tiny"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_local_asr_label"
                        app:layout_constraintEnd_toStartOf="@id/switch_local_asr" />

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/switch_local_asr"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="false"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/tv_local_asr_label"
                        app:layout_constraintBottom_toBottomOf="@id/tv_local_asr_desc" />

                    <TextView
                        android:id="@+id/tv_wakeup_reply_label"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="唤醒词回复"
                        android:textColor="@color/md3_on_surface"
                        android:textSize="@dimen/text_size_body"
                        android:layout_marginTop="@dimen/spacing_large"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_local_asr_desc"
                        app:layout_constraintEnd_toStartOf="@id/switch_wakeup_reply" />

                    <TextView
                        android:id="@+id/tv_wakeup_reply_desc"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="@string/wakeup_reply_desc"
                        android:textColor="@color/md3_on_surface_variant"
                        android:textSize="@dimen/text_size_caption"
                        android:layout_marginTop="@dimen/spacing_tiny"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_wakeup_reply_label"
                        app:layout_constraintEnd_toStartOf="@id/switch_wakeup_reply" />

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/switch_wakeup_reply"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/tv_wakeup_reply_label"
                        app:layout_constraintBottom_toBottomOf="@id/tv_wakeup_reply_desc" />

                    <TextView
                        android:id="@+id/tv_wakeup_delay_label"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="回复等待时间"
                        android:textColor="@color/md3_on_surface"
                        android:textSize="@dimen/text_size_body"
                        android:layout_marginTop="@dimen/spacing_large"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_wakeup_reply_desc"
                        app:layout_constraintEnd_toStartOf="@id/tv_wakeup_delay_value" />

                    <TextView
                        android:id="@+id/tv_wakeup_delay_desc"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="调整唤醒回复音频播放完成后的等待时间"
                        android:textColor="@color/md3_on_surface_variant"
                        android:textSize="@dimen/text_size_caption"
                        android:layout_marginTop="@dimen/spacing_tiny"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_wakeup_delay_label"
                        app:layout_constraintEnd_toStartOf="@id/tv_wakeup_delay_value" />

                    <TextView
                        android:id="@+id/tv_wakeup_delay_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="1500ms"
                        android:textColor="@color/md3_primary"
                        android:textSize="@dimen/text_size_body"
                        android:textStyle="bold"
                        android:minWidth="80dp"
                        android:gravity="end"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/tv_wakeup_delay_label"
                        app:layout_constraintBottom_toBottomOf="@id/tv_wakeup_delay_label" />

                    <SeekBar
                        android:id="@+id/seekbar_wakeup_delay"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:max="40"
                        android:progress="15"
                        android:layout_marginTop="@dimen/spacing_small"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_wakeup_delay_desc" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="@dimen/spacing_tiny"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/seekbar_wakeup_delay">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="1000ms"
                            android:textColor="@color/md3_on_surface_variant"
                            android:textSize="@dimen/text_size_caption"
                            android:gravity="start" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="3000ms"
                            android:textColor="@color/md3_on_surface_variant"
                            android:textSize="@dimen/text_size_caption"
                            android:gravity="center" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="5000ms"
                            android:textColor="@color/md3_on_surface_variant"
                            android:textSize="@dimen/text_size_caption"
                            android:gravity="end" />

                    </LinearLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 语速设置卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_tts_speed"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/spacing_large"
                app:cardBackgroundColor="@color/md3_surface"
                app:cardCornerRadius="@dimen/card_corner_radius"
                app:cardElevation="@dimen/card_elevation"
                app:strokeWidth="0dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/card_padding">

                    <ImageView
                        android:id="@+id/iv_tts_speed_icon"
                        android:layout_width="@dimen/icon_size_medium"
                        android:layout_height="@dimen/icon_size_medium"
                        android:src="@android:drawable/ic_media_ff"
                        android:tint="@color/md3_on_surface_variant"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_tts_speed_label"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="语速设置"
                        android:textColor="@color/md3_on_surface"
                        android:textSize="@dimen/text_size_subtitle"
                        android:textStyle="bold"
                        android:layout_marginStart="@dimen/spacing_small"
                        app:layout_constraintStart_toEndOf="@id/iv_tts_speed_icon"
                        app:layout_constraintTop_toTopOf="@id/iv_tts_speed_icon"
                        app:layout_constraintEnd_toStartOf="@id/tv_tts_speed_value" />

                    <TextView
                        android:id="@+id/tv_tts_speed_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="1.2x"
                        android:textColor="@color/md3_on_surface_variant"
                        android:textSize="@dimen/text_size_body"
                        android:textStyle="bold"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/tv_tts_speed_label" />

                    <TextView
                        android:id="@+id/tv_tts_speed_desc"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="当前语速"
                        android:textColor="@color/md3_on_surface_variant"
                        android:textSize="@dimen/text_size_caption"
                        android:layout_marginTop="@dimen/spacing_large"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/iv_tts_speed_icon"
                        app:layout_constraintEnd_toEndOf="parent" />

                    <SeekBar
                        android:id="@+id/seekbar_tts_speed"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:max="100"
                        android:progress="20"
                        android:progressTint="@color/md3_primary"
                        android:thumbTint="@color/md3_primary"
                        android:layout_marginTop="@dimen/spacing_medium"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_tts_speed_desc"
                        app:layout_constraintEnd_toEndOf="parent" />

                    <TextView
                        android:id="@+id/tv_speed_min"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="1.0x"
                        android:textColor="@color/md3_on_surface_variant"
                        android:textSize="@dimen/text_size_caption"
                        android:layout_marginTop="@dimen/spacing_small"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/seekbar_tts_speed" />

                    <TextView
                        android:id="@+id/tv_tts_speed_range"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="2.0x"
                        android:textColor="@color/md3_on_surface_variant"
                        android:textSize="@dimen/text_size_caption"
                        android:layout_marginTop="@dimen/spacing_small"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/seekbar_tts_speed" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 语言设置卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_language_settings"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/spacing_large"
                app:cardBackgroundColor="@color/md3_surface"
                app:cardCornerRadius="@dimen/card_corner_radius"
                app:cardElevation="@dimen/card_elevation"
                app:strokeWidth="0dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/card_padding">

                    <ImageView
                        android:id="@+id/iv_language_settings_icon"
                        android:layout_width="@dimen/icon_size_medium"
                        android:layout_height="@dimen/icon_size_medium"
                        android:src="@android:drawable/ic_menu_sort_alphabetically"
                        android:tint="@color/md3_on_surface_variant"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_language_settings_title"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="语言设置"
                        android:textColor="@color/md3_on_surface"
                        android:textSize="@dimen/text_size_subtitle"
                        android:textStyle="bold"
                        android:layout_marginStart="@dimen/spacing_small"
                        app:layout_constraintStart_toEndOf="@id/iv_language_settings_icon"
                        app:layout_constraintTop_toTopOf="@id/iv_language_settings_icon"
                        app:layout_constraintEnd_toEndOf="parent" />

                    <TextView
                        android:id="@+id/tv_language_label"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="回复语言"
                        android:textColor="@color/md3_on_surface_variant"
                        android:textSize="@dimen/text_size_body"
                        android:layout_marginTop="@dimen/spacing_large"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/iv_language_settings_icon"
                        app:layout_constraintEnd_toEndOf="parent" />

                    <Spinner
                        android:id="@+id/spinner_language"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/input_height"
                        android:layout_marginTop="@dimen/spacing_small"
                        android:background="@drawable/bg_spinner"
                        android:paddingHorizontal="@dimen/spacing_medium"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_language_label"
                        app:layout_constraintEnd_toEndOf="parent" />

                    <TextView
                        android:id="@+id/tv_wake_word_label"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="唤醒词"
                        android:textColor="@color/md3_on_surface_variant"
                        android:textSize="@dimen/text_size_body"
                        android:layout_marginTop="@dimen/spacing_large"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/spinner_language"
                        app:layout_constraintEnd_toStartOf="@+id/btn_refresh_wake_word" />
                        
                    <ImageButton
                        android:id="@+id/btn_refresh_wake_word"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@android:drawable/ic_popup_sync"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:padding="@dimen/spacing_small"
                        android:contentDescription="刷新唤醒词"
                        android:tint="@color/md3_primary"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/tv_wake_word_label"
                        app:layout_constraintBottom_toBottomOf="@id/tv_wake_word_label" />

                    <Spinner
                        android:id="@+id/spinner_wake_word"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/input_height"
                        android:layout_marginTop="@dimen/spacing_small"
                        android:background="@drawable/bg_spinner"
                        android:paddingHorizontal="@dimen/spacing_medium"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_wake_word_label"
                        app:layout_constraintEnd_toEndOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Logo设置卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_logo_settings"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/spacing_large"
                app:cardBackgroundColor="@color/md3_surface"
                app:cardCornerRadius="@dimen/card_corner_radius"
                app:cardElevation="@dimen/card_elevation"
                app:strokeWidth="0dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/card_padding">

                    <ImageView
                        android:id="@+id/iv_logo_settings_icon"
                        android:layout_width="@dimen/icon_size_medium"
                        android:layout_height="@dimen/icon_size_medium"
                        android:src="@android:drawable/ic_menu_gallery"
                        android:tint="@color/md3_on_surface_variant"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_logo_settings_title"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="Logo设置"
                        android:textColor="@color/md3_on_surface"
                        android:textSize="@dimen/text_size_subtitle"
                        android:textStyle="bold"
                        android:layout_marginStart="@dimen/spacing_medium"
                        app:layout_constraintStart_toEndOf="@id/iv_logo_settings_icon"
                        app:layout_constraintTop_toTopOf="@id/iv_logo_settings_icon"
                        app:layout_constraintEnd_toEndOf="parent" />

                    <TextView
                        android:id="@+id/tv_logo_settings_description"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="选择应用显示的Logo图标"
                        android:textColor="@color/md3_on_surface_variant"
                        android:textSize="@dimen/text_size_body"
                        android:layout_marginStart="@dimen/spacing_medium"
                        app:layout_constraintStart_toEndOf="@id/iv_logo_settings_icon"
                        app:layout_constraintTop_toBottomOf="@id/tv_logo_settings_title"
                        app:layout_constraintEnd_toEndOf="parent" />

                    <TextView
                        android:id="@+id/tv_logo_label"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="Logo选择"
                        android:textColor="@color/md3_on_surface_variant"
                        android:textSize="@dimen/text_size_body"
                        android:layout_marginTop="@dimen/spacing_large"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_logo_settings_description"
                        app:layout_constraintEnd_toStartOf="@+id/btn_refresh_logo" />

                    <ImageButton
                        android:id="@+id/btn_refresh_logo"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@android:drawable/ic_popup_sync"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:padding="@dimen/spacing_small"
                        android:contentDescription="刷新Logo列表"
                        android:tint="@color/md3_primary"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/tv_logo_label"
                        app:layout_constraintBottom_toBottomOf="@id/tv_logo_label" />

                    <Spinner
                        android:id="@+id/spinner_logo"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/input_height"
                        android:layout_marginTop="@dimen/spacing_small"
                        android:background="@drawable/bg_spinner"
                        android:paddingHorizontal="@dimen/spacing_medium"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_logo_label"
                        app:layout_constraintEnd_toEndOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 其他设置卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_other_settings"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/spacing_large"
                app:cardBackgroundColor="@color/md3_surface"
                app:cardCornerRadius="@dimen/card_corner_radius"
                app:cardElevation="@dimen/card_elevation"
                app:strokeWidth="0dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/spacing_large">

                    <ImageView
                        android:id="@+id/iv_other_settings_icon"
                        android:layout_width="@dimen/icon_size_medium"
                        android:layout_height="@dimen/icon_size_medium"
                        android:src="@drawable/ic_settings"
                        android:tint="@color/md3_primary"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_other_settings_title"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="其他设置"
                        android:textColor="@color/md3_on_surface"
                        android:textSize="@dimen/text_size_subtitle"
                        android:textStyle="bold"
                        android:layout_marginStart="@dimen/spacing_medium"
                        app:layout_constraintStart_toEndOf="@id/iv_other_settings_icon"
                        app:layout_constraintTop_toTopOf="@id/iv_other_settings_icon"
                        app:layout_constraintEnd_toEndOf="parent" />

                    <TextView
                        android:id="@+id/tv_return_to_greeting_label"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="返回开场白时间"
                        android:textColor="@color/md3_on_surface"
                        android:textSize="@dimen/text_size_body"
                        android:layout_marginTop="@dimen/spacing_large"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_other_settings_title"
                        app:layout_constraintEnd_toStartOf="@id/tv_return_to_greeting_value" />

                    <TextView
                        android:id="@+id/tv_return_to_greeting_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="30秒"
                        android:textColor="@color/md3_primary"
                        android:textSize="@dimen/text_size_body"
                        android:textStyle="bold"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/tv_return_to_greeting_label"
                        app:layout_constraintBottom_toBottomOf="@id/tv_return_to_greeting_label" />

                    <SeekBar
                        android:id="@+id/seekbar_return_to_greeting"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:max="115"
                        android:min="0"
                        android:progress="25"
                        android:layout_marginTop="@dimen/spacing_medium"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_return_to_greeting_label" />

                    <TextView
                        android:id="@+id/tv_return_to_greeting_description"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="音频播报完毕后，自动返回开场白界面的延迟时间（5-120秒）"
                        android:textColor="@color/md3_on_surface_variant"
                        android:textSize="@dimen/text_size_caption"
                        android:layout_marginTop="@dimen/spacing_small"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/seekbar_return_to_greeting" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 多轮对话设置卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_multi_turn_settings"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/spacing_large"
                app:cardBackgroundColor="@color/md3_surface"
                app:cardCornerRadius="@dimen/card_corner_radius"
                app:cardElevation="@dimen/card_elevation"
                app:strokeWidth="0dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/card_padding">

                    <ImageView
                        android:id="@+id/iv_multi_turn_icon"
                        android:layout_width="@dimen/icon_size_medium"
                        android:layout_height="@dimen/icon_size_medium"
                        android:src="@android:drawable/ic_menu_recent_history"
                        android:tint="@color/md3_on_surface_variant"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_multi_turn_title"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="多轮对话（开发中）"
                        android:textColor="@color/md3_on_surface"
                        android:textSize="@dimen/text_size_subtitle"
                        android:textStyle="bold"
                        android:layout_marginStart="@dimen/spacing_small"
                        app:layout_constraintStart_toEndOf="@id/iv_multi_turn_icon"
                        app:layout_constraintTop_toTopOf="@id/iv_multi_turn_icon"
                        app:layout_constraintEnd_toEndOf="parent" />

                    <TextView
                        android:id="@+id/tv_multi_turn_enabled_label"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="启用多轮对话"
                        android:textColor="@color/md3_on_surface"
                        android:textSize="@dimen/text_size_body"
                        android:layout_marginTop="@dimen/spacing_large"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/iv_multi_turn_icon"
                        app:layout_constraintEnd_toStartOf="@id/switch_multi_turn" />

                    <TextView
                        android:id="@+id/tv_multi_turn_enabled_desc"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="允许连续对话而无需重复唤醒词"
                        android:textColor="@color/md3_on_surface_variant"
                        android:textSize="@dimen/text_size_caption"
                        android:layout_marginTop="@dimen/spacing_tiny"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_multi_turn_enabled_label"
                        app:layout_constraintEnd_toStartOf="@id/switch_multi_turn" />

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/switch_multi_turn"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/tv_multi_turn_enabled_label"
                        app:layout_constraintBottom_toBottomOf="@id/tv_multi_turn_enabled_desc" />

                    <TextView
                        android:id="@+id/tv_multi_turn_timeout_label"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="对话超时时间"
                        android:textColor="@color/md3_on_surface"
                        android:textSize="@dimen/text_size_body"
                        android:layout_marginTop="@dimen/spacing_large"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_multi_turn_enabled_desc"
                        app:layout_constraintEnd_toStartOf="@id/tv_multi_turn_timeout_value" />

                    <TextView
                        android:id="@+id/tv_multi_turn_timeout_desc"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="无语音输入后自动返回开场白的时间（10-120秒）"
                        android:textColor="@color/md3_on_surface_variant"
                        android:textSize="@dimen/text_size_caption"
                        android:layout_marginTop="@dimen/spacing_tiny"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_multi_turn_timeout_label"
                        app:layout_constraintEnd_toStartOf="@id/tv_multi_turn_timeout_value" />

                    <TextView
                        android:id="@+id/tv_multi_turn_timeout_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="30秒"
                        android:textColor="@color/md3_primary"
                        android:textSize="@dimen/text_size_body"
                        android:textStyle="bold"
                        android:minWidth="60dp"
                        android:gravity="end"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/tv_multi_turn_timeout_label"
                        app:layout_constraintBottom_toBottomOf="@id/tv_multi_turn_timeout_label" />

                    <SeekBar
                        android:id="@+id/seekbar_multi_turn_timeout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:max="110"
                        android:progress="20"
                        android:progressTint="@color/md3_primary"
                        android:thumbTint="@color/md3_primary"
                        android:layout_marginTop="@dimen/spacing_medium"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_multi_turn_timeout_desc" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="@dimen/spacing_tiny"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/seekbar_multi_turn_timeout">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="10秒"
                            android:textColor="@color/md3_on_surface_variant"
                            android:textSize="@dimen/text_size_caption"
                            android:gravity="start" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="65秒"
                            android:textColor="@color/md3_on_surface_variant"
                            android:textSize="@dimen/text_size_caption"
                            android:gravity="center" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="120秒"
                            android:textColor="@color/md3_on_surface_variant"
                            android:textSize="@dimen/text_size_caption"
                            android:gravity="end" />

                    </LinearLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>