package ai.guiji.duix.test.ui.adapter

import ai.guiji.duix.test.R
import ai.guiji.duix.test.model.MediaDisplayItem
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.VideoView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions

/**
 * 媒体项适配器
 * 支持图片和视频的横向滚动显示
 */
class MediaAdapter(
    private val context: Context,
    private val mediaItems: MutableList<MediaDisplayItem>
) : RecyclerView.Adapter<MediaAdapter.MediaViewHolder>() {

    companion object {
        private const val TAG = "DUIX_MEDIA" // 使用更明显的TAG
        const val REQUEST_CODE_IMAGE_VIEWER = 1001
        const val REQUEST_CODE_VIDEO_PLAYER = 1002
    }

    /**
     * 媒体点击回调接口
     */
    interface OnMediaClickListener {
        fun onMediaClicked(mediaItem: MediaDisplayItem)
        fun onMediaViewerStarted(requestCode: Int)
    }

    private var onMediaClickListener: OnMediaClickListener? = null

    /**
     * 设置媒体点击监听器
     */
    fun setOnMediaClickListener(listener: OnMediaClickListener) {
        this.onMediaClickListener = listener
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MediaViewHolder {
        val view = LayoutInflater.from(context).inflate(R.layout.item_media, parent, false)
        return MediaViewHolder(view)
    }

    override fun onBindViewHolder(holder: MediaViewHolder, position: Int) {
        Log.i(TAG, "========== onBindViewHolder 被调用 ==========")
        Log.i(TAG, "绑定位置: $position")
        Log.i(TAG, "媒体项总数: ${mediaItems.size}")

        if (position < mediaItems.size) {
            val mediaItem = mediaItems[position]
            Log.i(TAG, "绑定媒体项: ${mediaItem.title}")
            holder.bind(mediaItem)
        } else {
            Log.e(TAG, "位置超出范围: $position >= ${mediaItems.size}")
        }

        Log.i(TAG, "========== onBindViewHolder 完成 ==========")
    }

    override fun getItemCount(): Int = mediaItems.size

    /**
     * 添加媒体项
     */
    fun addMediaItem(mediaItem: MediaDisplayItem) {
        Log.i(TAG, "========== 添加媒体项到适配器 ==========")
        Log.i(TAG, "添加前媒体项数量: ${mediaItems.size}")
        Log.i(TAG, "新媒体项: ${mediaItem.title}, 类型: ${mediaItem.type}")

        mediaItems.add(mediaItem)
        val newPosition = mediaItems.size - 1

        Log.i(TAG, "添加后媒体项数量: ${mediaItems.size}")
        Log.i(TAG, "新项目位置: $newPosition")

        notifyItemInserted(newPosition)

        Log.i(TAG, "notifyItemInserted 已调用")
        Log.i(TAG, "========== 媒体项添加完成 ==========")
    }

    /**
     * 清空所有媒体项
     */
    fun clearMediaItems() {
        val size = mediaItems.size
        mediaItems.clear()
        notifyItemRangeRemoved(0, size)
        Log.i(TAG, "清空所有媒体项")
    }

    /**
     * 检查是否包含图片类型的媒体项
     */
    fun hasImages(): Boolean {
        return mediaItems.any { it.type == MediaDisplayItem.Type.IMAGE }
    }

    /**
     * 检查是否包含视频类型的媒体项
     */
    fun hasVideos(): Boolean {
        return mediaItems.any { it.type == MediaDisplayItem.Type.VIDEO }
    }

    inner class MediaViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val ivMediaImage: ImageView = itemView.findViewById(R.id.iv_media_image)
        private val vvMediaVideo: VideoView = itemView.findViewById(R.id.vv_media_video)
        private val ivPlayButton: ImageView = itemView.findViewById(R.id.iv_play_button)
        private val pbLoading: ProgressBar = itemView.findViewById(R.id.pb_loading)
        private val tvMediaTitle: TextView = itemView.findViewById(R.id.tv_media_title)

        fun bind(mediaItem: MediaDisplayItem) {
            Log.i(TAG, "========== 绑定媒体项 ==========")
            Log.i(TAG, "媒体标题: ${mediaItem.title}")
            Log.i(TAG, "媒体URL: ${mediaItem.url}")
            Log.i(TAG, "媒体类型: ${mediaItem.type}")

            tvMediaTitle.text = mediaItem.title

            when (mediaItem.type) {
                MediaDisplayItem.Type.IMAGE -> {
                    Log.i(TAG, "开始绑定图片类型媒体")
                    bindImage(mediaItem)
                }
                MediaDisplayItem.Type.VIDEO -> {
                    Log.i(TAG, "开始绑定视频类型媒体")
                    bindVideo(mediaItem)
                }
            }

            // 点击事件：使用内置查看器
            itemView.setOnClickListener {
                // 通知 CallActivity 用户点击了媒体
                onMediaClickListener?.onMediaClicked(mediaItem)
                openMediaInternally(mediaItem)
            }

            Log.i(TAG, "========== 媒体项绑定完成 ==========")
        }

        private fun bindImage(mediaItem: MediaDisplayItem) {
            Log.i(TAG, "========== bindImage 开始 ==========")
            Log.i(TAG, "图片URL: ${mediaItem.url}")

            // 显示图片相关视图
            ivMediaImage.visibility = View.VISIBLE
            vvMediaVideo.visibility = View.GONE
            ivPlayButton.visibility = View.GONE
            pbLoading.visibility = View.VISIBLE

            Log.i(TAG, "视图可见性设置完成:")
            Log.i(TAG, "  ivMediaImage.visibility = ${ivMediaImage.visibility} (VISIBLE=${View.VISIBLE})")
            Log.i(TAG, "  pbLoading.visibility = ${pbLoading.visibility}")
            Log.i(TAG, "  ivMediaImage.width = ${ivMediaImage.width}")
            Log.i(TAG, "  ivMediaImage.height = ${ivMediaImage.height}")

            Log.i(TAG, "开始加载图片: ${mediaItem.url}")

            // 使用Glide加载图片
            val requestOptions = RequestOptions()
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .placeholder(R.drawable.bg_digital_card)
                .error(R.drawable.bg_digital_card)
                .timeout(30000) // 30秒超时

            Glide.with(context)
                .load(mediaItem.url)
                .apply(requestOptions)
                .listener(object : com.bumptech.glide.request.RequestListener<android.graphics.drawable.Drawable> {
                    override fun onLoadFailed(
                        e: com.bumptech.glide.load.engine.GlideException?,
                        model: Any?,
                        target: com.bumptech.glide.request.target.Target<android.graphics.drawable.Drawable>?,
                        isFirstResource: Boolean
                    ): Boolean {
                        Log.e(TAG, "========== 图片加载失败 ==========")
                        Log.e(TAG, "失败URL: ${mediaItem.url}")
                        Log.e(TAG, "错误详情: ${e?.message}")
                        e?.logRootCauses(TAG)
                        Log.e(TAG, "目标视图: $target")
                        Log.e(TAG, "是否首次资源: $isFirstResource")
                        pbLoading.visibility = View.GONE
                        Log.e(TAG, "========== 图片加载失败处理完成 ==========")
                        return false // 让Glide处理错误显示
                    }

                    override fun onResourceReady(
                        resource: android.graphics.drawable.Drawable?,
                        model: Any?,
                        target: com.bumptech.glide.request.target.Target<android.graphics.drawable.Drawable>?,
                        dataSource: com.bumptech.glide.load.DataSource?,
                        isFirstResource: Boolean
                    ): Boolean {
                        Log.i(TAG, "========== 图片加载成功 ==========")
                        Log.i(TAG, "成功URL: ${mediaItem.url}")
                        Log.i(TAG, "资源: $resource")
                        Log.i(TAG, "数据源: $dataSource")
                        Log.i(TAG, "目标视图: $target")
                        Log.i(TAG, "是否首次资源: $isFirstResource")
                        pbLoading.visibility = View.GONE
                        Log.i(TAG, "========== 图片加载成功处理完成 ==========")
                        return false // 让Glide处理显示
                    }
                })
                .into(ivMediaImage)

            Log.i(TAG, "========== bindImage 完成 ==========")
        }

        private fun bindVideo(mediaItem: MediaDisplayItem) {
            // 显示视频相关视图
            ivMediaImage.visibility = View.GONE
            vvMediaVideo.visibility = View.VISIBLE
            ivPlayButton.visibility = View.VISIBLE
            pbLoading.visibility = View.GONE

            // 设置视频缩略图（如果有的话）
            // 这里可以使用视频的第一帧作为缩略图
            // 暂时使用播放按钮覆盖层
        }

        private fun openMediaInternally(mediaItem: MediaDisplayItem) {
            try {
                // 检查URL是否有效
                if (mediaItem.url.isEmpty()) {
                    Log.e(TAG, "媒体URL为空，无法打开")
                    android.widget.Toast.makeText(context, "媒体URL无效", android.widget.Toast.LENGTH_SHORT).show()
                    return
                }
                
                // 打印完整URL信息便于调试
                Log.i(TAG, "准备打开媒体 - 类型: ${mediaItem.type}, 标题: ${mediaItem.title}")
                Log.i(TAG, "媒体URL: ${mediaItem.url}")
                
                when (mediaItem.type) {
                    MediaDisplayItem.Type.IMAGE -> {
                        // 检查是否有多张图片
                        val imageItems = mediaItems.filter { it.type == MediaDisplayItem.Type.IMAGE }
                        if (imageItems.size > 1) {
                            // 多图片模式
                            val imageUrls = ArrayList<String>()
                            val imageTitles = ArrayList<String>()
                            var currentPosition = 0

                            // 收集所有图片URL和标题
                            imageItems.forEachIndexed { index, item ->
                                imageUrls.add(item.url)
                                imageTitles.add(item.title)
                                if (item.url == mediaItem.url) {
                                    currentPosition = index
                                }
                            }

                            // 启动多图片浏览器
                            val intent = ai.guiji.duix.test.ui.activity.ImageViewerActivity.createIntent(
                                context,
                                imageUrls,
                                imageTitles,
                                currentPosition
                            )
                            (context as? android.app.Activity)?.startActivityForResult(intent, REQUEST_CODE_IMAGE_VIEWER)
                            onMediaClickListener?.onMediaViewerStarted(REQUEST_CODE_IMAGE_VIEWER)
                            Log.i(TAG, "使用内置多图片查看器: 总数=${imageUrls.size}, 当前位置=$currentPosition")
                        } else {
                            // 单图片模式
                            val intent = ai.guiji.duix.test.ui.activity.ImageViewerActivity.createIntent(
                                context,
                                mediaItem.url,
                                mediaItem.title
                            )
                            (context as? android.app.Activity)?.startActivityForResult(intent, REQUEST_CODE_IMAGE_VIEWER)
                            onMediaClickListener?.onMediaViewerStarted(REQUEST_CODE_IMAGE_VIEWER)
                            Log.i(TAG, "使用内置图片查看器: ${mediaItem.title}")
                        }
                    }
                    MediaDisplayItem.Type.VIDEO -> {
                        // 使用内置视频播放器
                        val intent = ai.guiji.duix.test.ui.activity.VideoPlayerActivity.createIntent(
                            context,
                            mediaItem.url,
                            mediaItem.title
                        )
                        (context as? android.app.Activity)?.startActivityForResult(intent, REQUEST_CODE_VIDEO_PLAYER)
                        onMediaClickListener?.onMediaViewerStarted(REQUEST_CODE_VIDEO_PLAYER)
                        Log.i(TAG, "使用内置视频播放器: ${mediaItem.title}")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "打开媒体失败: ${e.message}", e)
                // 如果内置播放器失败，可以考虑回退到外部应用
                openMediaExternallyAsFallback(mediaItem)
            }
        }

        private fun openMediaExternallyAsFallback(mediaItem: MediaDisplayItem) {
            try {
                val intent = Intent(Intent.ACTION_VIEW).apply {
                    data = Uri.parse(mediaItem.url)
                    when (mediaItem.type) {
                        MediaDisplayItem.Type.IMAGE -> {
                            setDataAndType(Uri.parse(mediaItem.url), "image/*")
                        }
                        MediaDisplayItem.Type.VIDEO -> {
                            setDataAndType(Uri.parse(mediaItem.url), "video/*")
                        }
                    }
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                context.startActivity(intent)
                Log.i(TAG, "回退到外部应用查看媒体: ${mediaItem.title}")
            } catch (e: Exception) {
                Log.e(TAG, "外部应用也无法打开媒体: ${e.message}", e)
                // 显示错误提示
                android.widget.Toast.makeText(context, "无法打开媒体文件", android.widget.Toast.LENGTH_SHORT).show()
            }
        }
    }
}
