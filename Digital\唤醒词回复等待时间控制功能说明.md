# 唤醒词回复等待时间控制功能说明

## 🎯 功能概述

新增了唤醒词回复等待时间的动态控制功能，用户可以在设置界面自定义调整"我在"音频播放完成后的等待时间，以适应不同的音频文件长度和使用场景。

## 🚀 功能特点

### ✨ 主要特性
- **动态调整**: 用户可自定义等待时间，范围1000-5000毫秒
- **实时预览**: 滑块调整时实时显示当前设置的毫秒数
- **智能默认**: 默认1500毫秒，适合大多数使用场景
- **精确控制**: 100毫秒为单位的精确调整
- **持久保存**: 设置自动保存，重启应用后保持用户选择

### 🎛️ 控制界面
- **滑块控制**: 直观的滑块操作，范围覆盖1000-5000ms
- **实时反馈**: 拖动时实时显示当前毫秒数
- **范围标识**: 显示最小值(1000ms)、中间值(3000ms)、最大值(5000ms)
- **当前值显示**: 右侧显示当前设置的精确毫秒数

## 🎮 使用方法

### 调整等待时间
1. 进入**设置界面**
2. 找到**"语音控制"**卡片
3. 在卡片中找到**"回复等待时间"**控制区域
4. 拖动滑块调整等待时间：
   - **向左拖动**: 减少等待时间（最小1000ms）
   - **向右拖动**: 增加等待时间（最大5000ms）
5. 实时查看右侧显示的毫秒数
6. 设置会自动保存

### 推荐设置
- **短音频文件**: 1000-1500ms（适合简短的"我在"回复）
- **标准音频文件**: 1500-2500ms（默认推荐，适合大多数情况）
- **长音频文件**: 2500-4000ms（适合较长的回复音频）
- **网络延迟环境**: 3000-5000ms（考虑网络或处理延迟）

## 🔧 技术实现

### 配置管理
```kotlin
// 设置键名
const val KEY_WAKEUP_DELAY = "wakeup_delay"

// 读取设置（默认1500ms）
val wakeupDelay = sharedPrefs.getInt(KEY_WAKEUP_DELAY, 1500)

// 保存设置
sharedPrefs.edit().putInt(KEY_WAKEUP_DELAY, delayMs).apply()
```

### 滑块映射
```kotlin
// 进度值转换为毫秒数（0-40 映射到 1000-5000ms）
val delayMs = 1000 + (progress * 100)

// 毫秒数转换为进度值
val seekBarProgress = ((wakeupDelay - 1000) / 100).coerceIn(0, 40)
```

### 动态应用
```kotlin
private fun playWakeupReplyAudio() {
    // 从设置中获取等待时间
    val wakeupDelay = sharedPrefs.getInt(SettingsActivity.KEY_WAKEUP_DELAY, 1500).toLong()
    
    // 暂停ASR → 播放音频 → 延迟恢复ASR
    pauseAsrRecording()
    playWav("我在.mp3")
    
    Handler(Looper.getMainLooper()).postDelayed({
        resumeAsrRecording()
    }, wakeupDelay)
}
```

## 📱 用户界面

### 设置界面布局
```
语音控制
├── 启用本地ASR ────────────────── [开关]
├── 唤醒词回复 ────────────────── [开关]
└── 回复等待时间 ──────────── [1500ms]
    调整唤醒回复音频播放完成后的等待时间
    [1000ms] ═══●═══ [3000ms] ═══ [5000ms]
```

### 交互体验
- **拖动滑块**: 实时更新右侧毫秒数显示
- **自动保存**: 松开滑块后立即保存设置
- **即时生效**: 下次唤醒时使用新的等待时间

## ⚙️ 配置详情

### 时间范围
- **最小值**: 1000毫秒（1秒）
- **最大值**: 5000毫秒（5秒）
- **步进**: 100毫秒
- **默认值**: 1500毫秒（1.5秒）

### 映射关系
| 滑块位置 | 毫秒数 | 适用场景 |
|---------|--------|----------|
| 0 | 1000ms | 极短音频 |
| 5 | 1500ms | 标准音频（默认） |
| 15 | 2500ms | 较长音频 |
| 30 | 4000ms | 长音频/网络延迟 |
| 40 | 5000ms | 最长等待 |

## 🔄 工作流程

### 完整时序
```
用户唤醒 → 播放"我在" → 等待[用户设置时间] → 恢复ASR → 开始识别
```

### 时间计算
1. **基础等待**: 用户设置的毫秒数
2. **额外延迟**: 如果有音频打断，额外增加500ms
3. **总等待时间**: 基础等待 + 额外延迟（如适用）

## 🎨 用户体验优化

### 个性化适配
- **音频长度适配**: 根据实际"我在"音频长度调整
- **设备性能适配**: 低性能设备可适当增加等待时间
- **网络环境适配**: 网络延迟较大时增加等待时间

### 使用建议
1. **首次使用**: 保持默认1500ms，观察效果
2. **音频被截断**: 适当增加等待时间
3. **等待过长**: 适当减少等待时间
4. **网络不稳定**: 增加到3000ms以上

## 📊 性能影响

### 资源消耗
- **内存**: 几乎无额外内存消耗
- **CPU**: 仅在设置变更时有轻微计算
- **存储**: 仅占用4字节存储空间

### 响应性能
- **设置保存**: 即时保存，无延迟
- **设置读取**: 毫秒级读取速度
- **界面更新**: 实时响应滑块操作

## 🔍 故障排除

### 常见问题
1. **音频被截断**: 增加等待时间到2000ms以上
2. **等待过长**: 减少等待时间到1200ms以下
3. **设置不生效**: 重启应用后设置会自动应用

### 调试建议
- 观察日志中的"使用自定义等待时间: XXXms"信息
- 根据实际音频播放时长调整设置
- 考虑设备性能和网络环境因素

---

**注意**: 等待时间设置会影响整体交互体验，建议根据实际使用情况进行微调，找到最适合的时间设置。
