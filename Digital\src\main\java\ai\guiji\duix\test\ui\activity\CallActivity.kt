package ai.guiji.duix.test.ui.activity

import ai.guiji.duix.sdk.client.Constant
import ai.guiji.duix.sdk.client.DUIX
import ai.guiji.duix.sdk.client.render.DUIXRenderer
import ai.guiji.duix.test.R
import ai.guiji.duix.test.config.ApiConfig
import ai.guiji.duix.test.databinding.ActivityCallBinding
import ai.guiji.duix.test.model.SseMessage
import ai.guiji.duix.test.model.MediaDisplayItem
import ai.guiji.duix.test.model.CachedQuestionsResponse
import ai.guiji.duix.test.ui.adapter.MediaAdapter
import ai.guiji.duix.test.util.LogUtils
import ai.guiji.duix.test.utils.DeviceIdUtil
import ai.guiji.duix.test.utils.LayoutManager
import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.opengl.GLSurfaceView
import android.os.Bundle
import android.os.Handler
import android.animation.ValueAnimator
import android.util.Log
import android.widget.ArrayAdapter
import android.widget.Button
import android.widget.LinearLayout

import android.widget.Toast
import android.view.View
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.fasterxml.jackson.databind.ObjectMapper

import io.noties.markwon.Markwon
import io.noties.markwon.ext.strikethrough.StrikethroughPlugin


import okhttp3.Call
import okhttp3.Callback
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.Response
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.OutputStream
import java.util.concurrent.Executors


class CallActivity : BaseActivity() {

    companion object {
        const val GL_CONTEXT_VERSION = 2
        private const val DEFAULT_TTS_BASE_URL = ""
        private const val PERMISSION_REQUEST_CODE = 1001
        private const val TAG_NET = "DUIX_NET"
    }

    private var baseDir = ""
    private var modelDir = ""
    private var ttsBaseUrl = DEFAULT_TTS_BASE_URL  // 基础服务器地址
    private var apiKey = ""  // 新增 apiKey 变量
    private var voiceType = "male"  // 新增 voiceType 变量，默认女声

    // 本地ASR相关变量
    private var localAsrEnabled: Boolean = false
    private var keywordSpotter: com.k2fsa.sherpa.onnx.KeywordSpotter? = null
    private var kwsStream: com.k2fsa.sherpa.onnx.OnlineStream? = null
    private var kwsAudioRecord: android.media.AudioRecord? = null
    private var kwsThread: Thread? = null
    @Volatile
    private var isKwsRecording: Boolean = false

    // 真实ASR相关变量
    private var asrOfflineRecognizer: com.k2fsa.sherpa.onnx.OfflineRecognizer? = null
    private var asrVad: com.k2fsa.sherpa.onnx.Vad? = null
    private var asrAudioRecord: android.media.AudioRecord? = null
    private var asrThread: Thread? = null
    @Volatile
    private var isAsrRecording: Boolean = false
    @Volatile
    private var isWaitingForSpeech: Boolean = false

    private lateinit var binding: ActivityCallBinding
    private var duix: DUIX? = null
    private var mDUIXRender: DUIXRenderer? = null
    private val mFilePath by lazy { getExternalFilesDir("vad")?.absolutePath ?: "" }
    private var isProcessingRequest = false
    private val objectMapper = ObjectMapper()
    private var isPlaying = false
    private val sharedPrefs by lazy { getSharedPreferences(SettingsActivity.PREF_NAME, MODE_PRIVATE) }
    
    // Markdown渲染器
    private lateinit var markwon: Markwon
    
    // LLM回答累积变量
    private var currentLlmAnswer = StringBuilder()

    // 完整LLM回答内容（用于一次性显示）
    private var completeLlmAnswer = StringBuilder()
    
    // 音频播放队列管理
    private val audioPlayQueue = mutableListOf<String>()
    private var isPlayingLlmAudio = false
    private var currentPlayingAudioUrl: String? = null



    // 智能滚动恢复系统
    private var userLastScrollTime = 0L
    private var scrollStartTime = 0L
    private var scrollDistance = 0
    private var lastScrollY = 0
    private val USER_SCROLL_TIMEOUT = 3000L // 3秒超时
    private val handler = android.os.Handler(android.os.Looper.getMainLooper())

    // 滚动行为类型枚举
    enum class ScrollBehaviorType {
        ACCIDENTAL,        // 意外触碰：短暂、距离小
        POSITION_ADJUST,   // 位置调整：微调后继续关注当前内容
        QUICK_BROWSE,      // 快速浏览：高速滑动查看整体
        DELIBERATE_READING // 深度阅读：长时间停留在特定位置
    }

    // 恢复优先级枚举
    enum class RecoveryPriority(val level: Int, val description: String) {
        CRITICAL(1, "音频高亮同步"),     // 最高优先级
        IMPORTANT(2, "新内容提示"),     // 中等优先级
        NORMAL(3, "自动滚动到底部")     // 最低优先级
    }
    
    // 音频重试机制
    private val audioRetryMap = mutableMapOf<String, Int>()
    private val maxAudioRetryCount = 2
    
    // 防止重复请求
    private var isProcessingLlmRequest = false

    // 🔧 增强请求追踪：记录当前正在处理的请求文本
    private var currentProcessingText: String? = null
    private var lastRequestTimestamp: Long = 0

    // 🔧 唤醒词监听恢复机制
    private var kwsMonitorHandler: android.os.Handler? = null
    private var kwsMonitorRunnable: Runnable? = null

    // 🔧 SSE流状态跟踪
    private var isLlmStreamActive = false  // 标记LLM流是否还在活跃

    // SSE连接管理
    private var currentSseCall: Call? = null

    // 媒体展示相关
    private lateinit var mediaAdapter: MediaAdapter
    private val mediaItems = mutableListOf<MediaDisplayItem>()

    // 历史问题管理
    private val historyQuestions = LinkedHashSet<String>() // 使用LinkedHashSet保持插入顺序并避免重复
    private val maxHistoryQuestions = 15 // 最多保存15个历史问题
    private var currentSessionQuestion: String? = null // 当前会话的问题，避免重复添加

    // 布局管理器
    private lateinit var layoutManager: LayoutManager

    // 滚动动画相关
    private val scrollAnimators = mutableListOf<ValueAnimator>()
    private val scrollSpeeds = arrayOf(1f, -1f, 1f) // 第1行左滚动，第2行右滚动，第3行左滚动
    private val scrollDurations = arrayOf(20000L, 25000L, 30000L) // 更快的滚动速度：25s->12s, 30s->15s, 35s->18s

    // 控制是否启用标签复制（用于解决重复显示问题）
    private val enableTagDuplication = false // 设置为false可以完全禁用标签复制

    // 生命周期状态管理 - 用于处理应用切换时的音频和ASR状态
    private var wasPlayingBeforePause = false
    private var wasAsrActiveBeforePause = false
    private var wasKwsActiveBeforePause = false

    // 打断提示条状态管理 - 防止闪烁和重复操作
    private var isInterruptHintVisible = false

    // 音频波浪可视化组件
    private lateinit var audioWaveView: ai.guiji.duix.test.ui.view.AudioWaveView

    // 语言选择相关 - 现在从设置中读取
    private fun getCurrentLanguage(): String {
        return sharedPrefs.getString(SettingsActivity.KEY_LANGUAGE, SettingsActivity.DEFAULT_LANGUAGE) ?: SettingsActivity.DEFAULT_LANGUAGE
    }

    // 唤醒词相关 - 从设置中读取
    private fun getCurrentWakeWord(): String {
        return sharedPrefs.getString(SettingsActivity.KEY_WAKE_WORD, SettingsActivity.DEFAULT_WAKE_WORD) ?: SettingsActivity.DEFAULT_WAKE_WORD
    }

    var reference_id: String? = null

    // 对话状态跟踪：用于控制greeting音频播放
    private var isFirstInteraction = true

    // 当前请求类型：用于控制greeting音频播放
    private var currentRequestType = "voice_input"  // voice_input, suggestion_click, history_click

    // dp转px的扩展函数
    private fun Int.dpToPx(): Int {
        return (this * resources.displayMetrics.density).toInt()
    }

    // 返回开场白定时器
    private var returnToGreetingHandler: android.os.Handler? = null
    private var returnToGreetingRunnable: Runnable? = null

    // 音频打断状态跟踪
    private var audioInterruptedByMedia = false
    private var interruptedAudioQueue = mutableListOf<String>()
    private var wasPlayingWhenInterrupted = false

    // 滚动交互检测
    private var scrollInteractionHandler: android.os.Handler? = null
    private var scrollInteractionRunnable: Runnable? = null
    private var isUserScrolling = false


    /**
     * 更新reference_id，确保与当前选择的模型类别一致
     */
    private fun updateReferenceId() {
        val modelCategory = sharedPrefs.getString(SettingsActivity.KEY_SELECTED_MODEL_CATEGORY, "male") ?: "male"

        Log.i(TAG_NET, "========== updateReferenceId 调试信息 ==========")
        Log.i(TAG_NET, "从SharedPreferences读取的模型类别: '$modelCategory'")
        Log.i(TAG_NET, "SettingsActivity.woman_key = '${SettingsActivity.woman_key}'")
        Log.i(TAG_NET, "SettingsActivity.man_key = '${SettingsActivity.man_key}'")

        val newReferenceId = when (modelCategory) {
            "female" -> {
                Log.i(TAG_NET, "匹配到female，设置为woman_key")
                SettingsActivity.woman_key  // "woman"
            }
            "male" -> {
                Log.i(TAG_NET, "匹配到male，设置为man_key")
                SettingsActivity.man_key      // "man"
            }
            else -> {
                Log.i(TAG_NET, "未匹配到已知类别，使用默认man_key")
                SettingsActivity.man_key        // 默认为男声
            }
        }

        Log.i(TAG_NET, "计算出的newReferenceId: '$newReferenceId'")
        Log.i(TAG_NET, "当前reference_id: '$reference_id'")

        if (reference_id != newReferenceId) {
            Log.i(TAG_NET, "✓ 更新reference_id: '$reference_id' → '$newReferenceId' (模型类别: '$modelCategory')")
            reference_id = newReferenceId
        } else {
            Log.i(TAG_NET, "✓ reference_id无需更新: '$reference_id' (模型类别: '$modelCategory')")
        }
        Log.i(TAG_NET, "========== updateReferenceId 结束 ==========")
    }

    // 创建一个配置了超时时间的 OkHttpClient
    private val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
        .writeTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
        .readTimeout(60, java.util.concurrent.TimeUnit.SECONDS).build()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.i(TAG_NET, "========== CallActivity onCreate 开始 ==========")

        keepScreenOn()
        binding = ActivityCallBinding.inflate(layoutInflater)
        setContentView(binding.root)

        Log.i(TAG_NET, "✓ 布局绑定完成")

        // 设置打断提示条的响应式内边距（基于15dp的百分比）
        setupResponsivePadding()

        // 初始化Markdown渲染器
        markwon = Markwon.builder(this)
            .usePlugin(StrikethroughPlugin.create())
            .usePlugin(io.noties.markwon.html.HtmlPlugin.create())
            .build()
        Log.i(TAG_NET, "✓ Markwon初始化完成")

        baseDir = intent.getStringExtra("baseDir") ?: ""
        modelDir = intent.getStringExtra("modelDir") ?: ""
        ttsBaseUrl = intent.getStringExtra("ttsUrl") ?: DEFAULT_TTS_BASE_URL  // 获取基础服务器地址
        apiKey = intent.getStringExtra("apiKey") ?: ""  // 获取 apiKey
        voiceType = intent.getStringExtra("voiceType") ?: "male"  // 获取 voiceType

        // 如果Intent中没有传递ttsUrl，从SharedPreferences中获取
        if (ttsBaseUrl.isEmpty()) {
            ttsBaseUrl = sharedPrefs.getString(SettingsActivity.KEY_TTS_URL, "") ?: ""
            Log.i(TAG_NET, "Intent中ttsUrl为空，从SharedPreferences获取: $ttsBaseUrl")
        }

        // 添加调试日志
        Log.i(TAG_NET, "Intent传递的参数:")
        Log.i(TAG_NET, "  baseDir: $baseDir")
        Log.i(TAG_NET, "  modelDir: $modelDir")
        Log.i(TAG_NET, "  ttsUrl from Intent: ${intent.getStringExtra("ttsUrl")}")
        Log.i(TAG_NET, "  ttsBaseUrl (最终值): $ttsBaseUrl")
        Log.i(TAG_NET, "  apiKey: $apiKey")
        Log.i(TAG_NET, "  voiceType: $voiceType")

        // 获取传递的模型信息，如果有的话，保存到SharedPreferences
        val selectedModelUrl = intent.getStringExtra("selectedModelUrl")
        val selectedModelCategory = intent.getStringExtra("selectedModelCategory")

        if (!selectedModelUrl.isNullOrEmpty() && !selectedModelCategory.isNullOrEmpty()) {
            Log.i(TAG_NET, "接收到模型信息 - URL: $selectedModelUrl, Category: $selectedModelCategory")
            sharedPrefs.edit()
                .putString(SettingsActivity.KEY_SELECTED_MODEL_URL, selectedModelUrl)
                .putString(SettingsActivity.KEY_SELECTED_MODEL_CATEGORY, selectedModelCategory)
                .apply()
        }

        // 读取是否启用本地ASR
        localAsrEnabled = sharedPrefs.getBoolean(SettingsActivity.KEY_ENABLE_LOCAL_ASR, false)

        // 如果启用本地ASR，先获取唤醒词再初始化ASR组件
        if (localAsrEnabled) {
            // Toast.makeText(this, "正在初始化语音识别...", Toast.LENGTH_SHORT).show()
            // 先获取唤醒词，然后再初始化ASR
            fetchWakeWordsAndInitAsr()
        }

        // 添加详细日志
        Log.i(TAG_NET, "CallActivity 初始化参数:")
        Log.i(TAG_NET, "baseDir: $baseDir")
        Log.i(TAG_NET, "modelDir: $modelDir")
        Log.i(TAG_NET, "ttsBaseUrl: $ttsBaseUrl")
        Log.i(TAG_NET, "Intent传递的apiKey: $apiKey")
        Log.i(TAG_NET, "voiceType: $voiceType")

        // 检查SharedPreferences中的API Key
        val savedApiKey = sharedPrefs.getString(SettingsActivity.KEY_APIKEY, "") ?: ""
        Log.i(TAG_NET, "SharedPreferences中的apiKey: $savedApiKey")

        Log.e("CallActivity", "baseDir: $baseDir")
        Log.e("CallActivity", "modelDir: $modelDir")

        // 检查目录和文件状态
        checkDirectoriesAndFiles()

        // 显示Toast来确认我们能看到输出
        // Toast.makeText(this, "CallActivity启动，baseDir: $baseDir", Toast.LENGTH_LONG).show()

        // 设置音色选择下拉框
        val voiceTypes = arrayOf("女声", "男声")
        val adapter = ArrayAdapter(this, com.google.android.material.R.layout.support_simple_spinner_dropdown_item, voiceTypes)
        binding.actVoiceType.setAdapter(adapter)

        // 设置初始值
        binding.actVoiceType.setText(if (voiceType == "female") "女声" else "男声", false)

        // 添加音色选择监听
        binding.actVoiceType.setOnItemClickListener { _, _, position, _ ->
            voiceType = if (position == 0) "female" else "male" // 可以在这里添加其他需要的处理
        }

        binding.btnPlay.setOnClickListener {
            // 测试按钮颜色变化
            Log.i(TAG_NET, "测试按钮颜色变化 - 设置红色状态")
            updateRecordButton("播放中...")

            // 10秒后恢复正常状态，给用户足够时间观察
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                Log.i(TAG_NET, "测试完成 - 恢复等待唤醒状态")
                updateRecordButton("等待唤醒")
            }, 10000)

            // 显示提示
            // Toast.makeText(this, "测试红色按钮状态，10秒后恢复", Toast.LENGTH_LONG).show()

            // 同时播放音频
            playWav()
        }

        // 录音按钮点击事件 - 用于手动打断音频播报
        binding.btnRecord.setOnClickListener {
            Log.i(TAG_NET, "========== 录音按钮被点击 ==========")
            Log.i(TAG_NET, "当前状态 - isPlayingLlmAudio: $isPlayingLlmAudio, isProcessingRequest: $isProcessingRequest")
            Log.i(TAG_NET, "当前播放URL: $currentPlayingAudioUrl")
            Log.i(TAG_NET, "音频队列大小: ${audioPlayQueue.size}")

            // 检查数字人是否正在播报（包括LLM音频和TTS音频）
            if (isPlayingLlmAudio || isProcessingRequest) {
                Log.i(TAG_NET, "检测到音频播放中，执行手动打断操作")

                // 🔧 标记为手动打断
                isManualInterrupt = true

                // 统一使用打断逻辑，无论是LLM还是TTS音频
                interruptAudioPlayback()

                // 显示提示信息
                runOnUiThread {
                    Toast.makeText(this, "已打断音频播报", Toast.LENGTH_SHORT).show()
                }
            } else {
                Log.i(TAG_NET, "当前没有音频播放，但仍尝试停止")

                // 即使状态显示没有播放，也尝试停止，防止状态不同步
                try {
                    val stopResult = duix?.stopAudio()
                    Log.i(TAG_NET, "强制调用duix.stopAudio()结果: $stopResult")
                } catch (e: Exception) {
                    Log.e(TAG_NET, "强制停止音频播放失败: ${e.message}", e)
                }

                // 重置所有状态
                isPlayingLlmAudio = false
                currentPlayingAudioUrl = null
                isProcessingRequest = false
                clearAudioQueue()

                runOnUiThread {
                    updateRecordButton("等待唤醒")
                    Toast.makeText(this, "已重置音频状态", Toast.LENGTH_SHORT).show()
                }
            }
            Log.i(TAG_NET, "========== 录音按钮点击处理完成 ==========")
        }

        // 录音按钮长按事件 - 布局调整诊断
        binding.btnRecord.setOnLongClickListener {
            Log.i(TAG_NET, "录音按钮长按 - 启动布局调整诊断")

            runOnUiThread {
                // 创建诊断对话框
                val dialog = android.app.AlertDialog.Builder(this)
                    .setTitle("布局调整诊断")
                    .setMessage("选择诊断功能：")
                    .setPositiveButton("测试开场白问题位置") { _, _ ->
                        testGreetingQuestionsPosition()
                    }
                    .setNegativeButton("ASR系统诊断") { _, _ ->
                        Thread {
                            val diagnosis = diagnoseAsrSystem()
                            runOnUiThread {
                                val asrDialog = android.app.AlertDialog.Builder(this)
                                    .setTitle("ASR系统诊断")
                                    .setMessage(diagnosis)
                                    .setPositiveButton("重新初始化ASR") { _, _ ->
                                        Thread {
                                            val success = reinitializeAsrSystem()
                                            runOnUiThread {
                                                Toast.makeText(
                                                    this,
                                                    if (success) "ASR重新初始化成功" else "ASR重新初始化失败",
                                                    Toast.LENGTH_SHORT
                                                ).show()
                                            }
                                        }.start()
                                    }
                                    .setNeutralButton("关闭", null)
                                    .create()
                                asrDialog.show()
                            }
                        }.start()
                    }
                    .setNeutralButton("关闭", null)
                    .create()

                dialog.show()
            }

            true // 消费长按事件
        }

        // 设置图标点击事件
        binding.btnSettings.setOnClickListener {
            Log.i(TAG_NET, "设置图标被点击")
            try {
                val intent = Intent(this, SettingsActivity::class.java)
                Log.i(TAG_NET, "准备启动SettingsActivity")
                startActivity(intent)
                Log.i(TAG_NET, "SettingsActivity启动成功")
            } catch (e: Exception) {
                Log.e(TAG_NET, "启动SettingsActivity失败: ${e.message}", e)
            }
        }

        // 模型选择图标点击事件
        binding.btnModel.setOnClickListener {
            Log.i(TAG_NET, "模型选择图标被点击")
            try {
                val intent = Intent(this, ModelSelectionActivity::class.java)
                Log.i(TAG_NET, "准备启动ModelSelectionActivity")
                startActivity(intent)
                Log.i(TAG_NET, "ModelSelectionActivity启动成功")
            } catch (e: Exception) {
                Log.e(TAG_NET, "启动ModelSelectionActivity失败: ${e.message}", e)
            }
        }

        // 背景选择图标点击事件
        binding.btnBackground.setOnClickListener {
            Log.i(TAG_NET, "背景选择图标被点击")
            try {
                val intent = Intent(this, BackgroundSelectionActivity::class.java)
                Log.i(TAG_NET, "准备启动BackgroundSelectionActivity")
                startActivity(intent)
                Log.i(TAG_NET, "BackgroundSelectionActivity启动成功")
            } catch (e: Exception) {
                Log.e(TAG_NET, "启动BackgroundSelectionActivity失败: ${e.message}", e)
            }
        }

        // 重置按钮点击事件
        binding.btnReset.setOnClickListener {
            Log.i(TAG_NET, "重置按钮被点击")
            resetToWelcomeState()
        }

        // 布局调整按钮点击事件
        binding.btnLayoutAdjustment.setOnClickListener {
            Log.i(TAG_NET, "布局调整按钮被点击")
            try {
                LayoutAdjustmentActivity.start(this)
                Log.i(TAG_NET, "LayoutAdjustmentActivity启动成功")
            } catch (e: Exception) {
                Log.e(TAG_NET, "启动LayoutAdjustmentActivity失败: ${e.message}", e)
            }
        }

        // Logo点击事件 - 控制菜单展开/收起
        binding.ivLogo.setOnClickListener {
            Log.i(TAG_NET, "Logo被点击，当前菜单状态: $isMenuExpanded")
            toggleMenu()
        }

        // 加载背景图片（从SharedPreferences获取用户选择的背景）
        loadSelectedBackground()

        // 加载动态Logo（从SharedPreferences获取用户选择的Logo）
        loadSelectedLogo()

        // 初始化媒体适配器
        initMediaAdapter()

        // 更新唤醒词提示
        updateWakeWordHint()

        binding.glTextureView.setEGLContextClientVersion(GL_CONTEXT_VERSION)
        binding.glTextureView.setEGLConfigChooser(8, 8, 8, 8, 16, 0) // 透明
        //        binding.glTextureView.preserveEGLContextOnPause = true      // 后台运行不要释放上下文
        binding.glTextureView.isOpaque = false           // 透明

        mDUIXRender = DUIXRenderer(mContext, binding.glTextureView)
        binding.glTextureView.setRenderer(mDUIXRender)
        binding.glTextureView.renderMode = GLSurfaceView.RENDERMODE_WHEN_DIRTY      // 一定要在设置完Render之后再调用

        // 检查目录是否存在
        Log.i(TAG_NET, "=== DUIX初始化诊断开始 ===")
        Log.i(TAG_NET, "baseDir路径: $baseDir")
        Log.i(TAG_NET, "modelDir路径: $modelDir")
        Log.i(TAG_NET, "baseDir存在: ${File(baseDir).exists()}")
        Log.i(TAG_NET, "modelDir存在: ${File(modelDir).exists()}")

        // 检查baseDir
        val baseDirFile = File(baseDir)
        if (baseDirFile.exists()) {
            Log.i(TAG_NET, "baseDir存在，文件数量: ${baseDirFile.listFiles()?.size ?: 0}")
            // 注释掉详细文件列表以减少日志输出
            /*
            baseDirFile.listFiles()?.forEach { file ->
                Log.i(TAG_NET, "  ${file.name} (${if (file.isDirectory) "目录" else "文件"}, ${file.length()} bytes)")
            }
            */

            // 检查baseDir中的关键文件（DUIX SDK需要的基础文件）
            Log.i(TAG_NET, "检查baseDir中的关键基础文件:")
            val baseRequiredFiles = listOf("wo", "wb", "cp", "ab", "ap")
            var missingBaseFiles = 0
            baseRequiredFiles.forEach { fileName ->
                val file = File(baseDir, fileName)
                val exists = file.exists()
                Log.i(TAG_NET, "  基础文件 $fileName: ${if (exists) "存在 (${file.length()} bytes)" else "缺失"}")
                if (!exists) missingBaseFiles++
            }

            // 检查原始基础文件（需要转换的文件）
            Log.i(TAG_NET, "检查baseDir中的原始基础文件:")
            val baseOriginalFiles = mapOf(
                "alpha_model.b" to "ab",
                "alpha_model.p" to "ap",
                "cacert.p" to "cp",
                "weight_168u.b" to "wb",
                "wenet.o" to "wo"
            )
            baseOriginalFiles.forEach { (originalName, convertedName) ->
                val originalFile = File(baseDir, originalName)
                val convertedFile = File(baseDir, convertedName)
                Log.i(TAG_NET, "  原始文件 $originalName: ${if (originalFile.exists()) "存在 (${originalFile.length()} bytes)" else "缺失"}")
                Log.i(TAG_NET, "  转换文件 $convertedName: ${if (convertedFile.exists()) "存在 (${convertedFile.length()} bytes)" else "缺失"}")
            }

            if (missingBaseFiles > 0) {
                Log.e(TAG_NET, "baseDir缺少 $missingBaseFiles 个关键基础文件，DUIX初始化可能失败")
                Log.e(TAG_NET, "请检查基础配置文件是否已正确下载和解压")
            }
        }

        // 检查modelDir
        val modelDirFile = File(modelDir)
        if (modelDirFile.exists()) {
            val files = modelDirFile.listFiles()
            Log.i(TAG_NET, "modelDir存在，文件数量: ${files?.size ?: 0}")
            // 注释掉详细文件列表以减少日志输出
            /*
            files?.forEach { file ->
                Log.i(TAG_NET, "  ${file.name} (${if (file.isDirectory) "目录" else "文件"}, 大小: ${file.length()} bytes)")
            }
            */

            // 检查uuid文件
            val uuidFile = File(modelDirFile, "uuid")
            if (uuidFile.exists()) {
                try {
                    val uuidContent = uuidFile.readText().trim()
                    Log.i(TAG_NET, "uuid文件内容: '$uuidContent'")
                } catch (e: Exception) {
                    Log.e(TAG_NET, "读取uuid文件失败: ${e.message}")
                }
            } else {
                Log.w(TAG_NET, "uuid文件不存在")
            }

            // 检查必要的模型文件
            checkModelFiles(modelDirFile)
        } else {
            Log.e(TAG_NET, "modelDir不存在: $modelDir")
        }

        Log.i(TAG_NET, "=== 开始创建DUIX对象 ===")

        // 最后检查：确保模型目录完整性
        if (!validateModelDirectory(modelDirFile)) {
            Log.e(TAG_NET, "模型目录验证失败，无法初始化DUIX")
            runOnUiThread {
                Toast.makeText(this@CallActivity, "模型文件不完整，请重新下载模型", Toast.LENGTH_LONG).show()
                // 返回到模型选择界面重新下载
                finish()
            }
            return
        }

        duix = DUIX(mContext, baseDir, modelDir, mDUIXRender) { event, msg, info ->
            when (event) {
                Constant.CALLBACK_EVENT_INIT_READY -> {
                    Log.i(TAG_NET, "DUIX初始化成功")
                    initOk()
                }

                Constant.CALLBACK_EVENT_INIT_ERROR -> {
                    Log.e(TAG_NET, "DUIX初始化失败: $msg")
                    Log.e(TAG_NET, "初始化失败详细信息: event=$event, msg=$msg, info=$info")

                    // 再次检查文件状态
                    Log.e(TAG_NET, "初始化失败时的文件状态:")
                    Log.e(TAG_NET, "baseDir: $baseDir, 存在: ${File(baseDir).exists()}")
                    Log.e(TAG_NET, "modelDir: $modelDir, 存在: ${File(modelDir).exists()}")

                    runOnUiThread {
                        Toast.makeText(mContext, "初始化异常: $msg", Toast.LENGTH_LONG).show()
                    }
                }

                Constant.CALLBACK_EVENT_AUDIO_PLAY_START -> {
                    Log.i(TAG_NET, "数字人开始播放")

                    runOnUiThread {
                        updateRecordButton("播放中...")

                        // 只在LLM音频播放时显示打断提示条，唤醒回复音频不显示
                        if (isPlayingLlmAudio) {
                            Log.i(TAG_NET, "检测到LLM音频播放，显示打断提示条")
                            showInterruptHint()

                            // 设置音频波浪为播放状态
                            setAudioWavePlaying(true)

                            // 🔧 增强唤醒词监听检查：确保在音频播放期间真正有效
                            Log.i(TAG_NET, "========== 音频播放期间唤醒词监听状态检查 ==========")
                            Log.i(TAG_NET, "isKwsRecording: $isKwsRecording")
                            Log.i(TAG_NET, "kwsAudioRecord: ${kwsAudioRecord != null}")
                            Log.i(TAG_NET, "kwsAudioRecord状态: ${kwsAudioRecord?.recordingState}")
                            Log.i(TAG_NET, "kwsThread: ${kwsThread != null}")
                            Log.i(TAG_NET, "kwsThread活跃: ${kwsThread?.isAlive}")

                            val isKwsActuallyWorking = isKwsRecording &&
                                kwsAudioRecord != null &&
                                kwsAudioRecord?.recordingState == android.media.AudioRecord.RECORDSTATE_RECORDING &&
                                kwsThread?.isAlive == true

                            if (!isKwsActuallyWorking) {
                                Log.w(TAG_NET, "⚠ 唤醒词监听状态异常，强制重新启动")
                                Log.w(TAG_NET, "状态详情 - isKwsRecording: $isKwsRecording")
                                Log.w(TAG_NET, "状态详情 - recordingState: ${kwsAudioRecord?.recordingState}")
                                Log.w(TAG_NET, "状态详情 - threadAlive: ${kwsThread?.isAlive}")

                                // 强制重新初始化唤醒词监听
                                isKwsRecording = false
                                try {
                                    kwsAudioRecord?.stop()
                                    kwsAudioRecord?.release()
                                    kwsAudioRecord = null
                                    kwsThread?.interrupt()
                                    kwsThread = null
                                } catch (e: Exception) {
                                    Log.e(TAG_NET, "清理异常唤醒词状态失败: ${e.message}")
                                }

                                // 延迟重启，避免与音频播放冲突
                                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                                    Log.i(TAG_NET, "音频播放期间重新启动唤醒词监听")
                                    startLocalKws()
                                }, 200)
                            } else {
                                Log.i(TAG_NET, "✓ 唤醒词监听真正活跃，支持语音打断")
                            }

                            // 🔧 启动定期唤醒词监听检查机制
                            startKwsMonitoring()

                            Log.i(TAG_NET, "========== 唤醒词监听状态检查完成 ==========")
                        } else {
                            Log.i(TAG_NET, "非LLM音频播放（如唤醒回复），不显示打断提示条")
                            // 非LLM音频也设置播放状态，但幅度较小
                            setAudioWavePlaying(true)
                        }
                    }
                }

                Constant.CALLBACK_EVENT_AUDIO_PLAY_END -> {
                    Log.i(TAG_NET, "数字人播放结束")

                    // 关闭音频波浪播放状态
                    runOnUiThread {
                        setAudioWavePlaying(false)
                    }

                    // 判断是否是LLM音频播放完成
                    if (isPlayingLlmAudio && currentPlayingAudioUrl != null) {
                        Log.i(TAG_NET, "LLM音频播放完成")

                        // 🔧 停止唤醒词监听监控
                        stopKwsMonitoring()

                        onLlmAudioPlayCompleted()
                    } else {
                        // 原有的TTS音频播放完成逻辑
                        Log.i(TAG_NET, "TTS音频播放完成")
                        isProcessingRequest = false  // 播放结束，重置状态
                        runOnUiThread {
                            binding.btnRecord.isEnabled = true
                            updateRecordButton("等待唤醒")
                            hideInterruptHint()
                        }

                        // 🔧 关键修复：TTS音频播放完成后重新启动唤醒词监听（使用强制重启机制）
                        Log.i(TAG_NET, "TTS音频播放完成，重新启动唤醒词监听")
                        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                            if (localAsrEnabled) {
                                // 🔧 使用与红色按钮相同的强制重启机制，解决状态不同步问题
                                if (!isKwsRecording) {
                                    Log.i(TAG_NET, "重新启动唤醒词监听以支持下次唤醒")
                                    startLocalKws()
                                } else {
                                    Log.w(TAG_NET, "⚠ 检测到状态不同步，强制重新初始化唤醒词监听")
                                    Log.w(TAG_NET, "当前状态 - isKwsRecording: $isKwsRecording, recordingState: ${kwsAudioRecord?.recordingState}")
                                    isKwsRecording = false
                                    startLocalKws()
                                }
                            } else {
                                Log.i(TAG_NET, "本地ASR未启用，跳过唤醒词监听重启")
                            }
                        }, 500) // 延迟500ms确保音频完全停止
                    }
                }

                Constant.CALLBACK_EVENT_AUDIO_PLAY_ERROR -> {
                    Log.e(TAG_NET, "数字人播放错误: $msg")

                    // 关闭音频波浪播放状态
                    runOnUiThread {
                        setAudioWavePlaying(false)
                    }

                    // 判断是否是LLM音频播放错误
                    if (isPlayingLlmAudio && currentPlayingAudioUrl != null) {
                        Log.e(TAG_NET, "LLM音频播放错误")
                        onLlmAudioPlayError(msg ?: "未知错误")
                    } else {
                        // 原有的TTS音频播放错误逻辑
                        Log.e(TAG_NET, "TTS音频播放错误")
                        isProcessingRequest = false  // 播放错误，重置状态
                        runOnUiThread {
                            Toast.makeText(mContext, "播放失败: $msg", Toast.LENGTH_SHORT).show()
                            binding.btnRecord.isEnabled = true
                            updateRecordButton("等待唤醒")
                        }
                    }
                }

                Constant.CALLBACK_EVENT_AUDIO_PLAY_PROGRESS -> { //                    Log.e(TAG, "audio play progress: $info")

                }
            }
        } // 异步回调结果

        Log.i(TAG_NET, "开始调用DUIX.init()")
        try {
            duix?.init()
            Log.i(TAG_NET, "DUIX.init()调用成功")
        } catch (e: Exception) {
            Log.e(TAG_NET, "DUIX.init()调用失败: ${e.message}", e)
        }

        // 设置按钮初始状态为等待唤醒（蓝色）
        updateRecordButton("等待唤醒")

        if (checkRecordPermission()) {
            initMinerva()
        } else {
            requestRecordPermission()
        }
        // 根据模型category设置reference_id
        val modelCategory = sharedPrefs.getString(SettingsActivity.KEY_SELECTED_MODEL_CATEGORY, "male") ?: "male"
        Log.i(TAG_NET, "从SharedPreferences读取的模型类别: $modelCategory")
        Log.i(TAG_NET, "SettingsActivity.woman_key = ${SettingsActivity.woman_key}")
        Log.i(TAG_NET, "SettingsActivity.man_key = ${SettingsActivity.man_key}")

        reference_id = when (modelCategory) {
            "female" -> SettingsActivity.woman_key  // "woman"
            "male" -> SettingsActivity.man_key      // "man"
            else -> SettingsActivity.man_key        // 默认为男声
        }

        Log.i(TAG_NET, "模型类别: $modelCategory, 设置reference_id: $reference_id")

        // 初始化请求状态
        currentRequestType = "voice_input"
        Log.i(TAG_NET, "✓ 初始化请求状态")

        // 初始化布局管理器
        layoutManager = LayoutManager.getInstance(this)
        Log.i(TAG_NET, "✓ LayoutManager初始化完成")

        // 注册布局变化监听器
        layoutManager.registerLayoutChangeListener(object : LayoutManager.OnLayoutChangeListener {
            override fun onLayoutChanged(layoutType: LayoutManager.LayoutType, newPercentage: Float) {
                Log.i(TAG_NET, "布局变化监听: $layoutType -> ${(newPercentage * 100).toInt()}%")
                runOnUiThread {
                    when (layoutType) {
                        LayoutManager.LayoutType.OPENING_STATEMENT ->
                            updateGuidelinePosition("guideline_opening_statement", newPercentage)
                        LayoutManager.LayoutType.GREETING_QUESTIONS ->
                            updateGuidelinePosition("guideline_greeting_questions", newPercentage)
                        LayoutManager.LayoutType.GREETING_QUESTIONS_WIDTH -> {
                            // 更新推荐问题区域的宽度
                            updateSuggestedQuestionsWidth(newPercentage)
                        }
                        LayoutManager.LayoutType.USER_QUESTION,
                        LayoutManager.LayoutType.AI_ANSWER,
                        LayoutManager.LayoutType.CONVERSATION_DIALOG -> {
                            updateGuidelinePosition("guideline_user_question", newPercentage)
                            // 获取当前对话框高度
                            val conversationHeight = layoutManager.getLayoutPosition(LayoutManager.LayoutType.CONVERSATION_HEIGHT)
                            updateGuidelinePosition("guideline_ai_answer_end", newPercentage + conversationHeight)
                        }
                        LayoutManager.LayoutType.CONVERSATION_HEIGHT -> {
                            // 对话框高度变化时，更新结束位置
                            val conversationPosition = layoutManager.getLayoutPosition(LayoutManager.LayoutType.CONVERSATION_DIALOG)
                            updateGuidelinePosition("guideline_ai_answer_end", conversationPosition + newPercentage)
                        }
                        LayoutManager.LayoutType.BOTTOM_SUGGESTIONS ->
                            updateGuidelinePosition("guideline_bottom_suggestions", newPercentage)
                        LayoutManager.LayoutType.RECORD_BUTTON ->
                            updateGuidelinePosition("guideline_record_button", newPercentage)
                        LayoutManager.LayoutType.INTERRUPT_HINT ->
                            updateGuidelinePosition("guideline_interrupt_hint", newPercentage)
                    }
                }
            }
        })

        // 应用动态布局（延迟执行，确保视图已经测量完成）
        binding.root.post {
            applyDynamicLayouts()
        }

        // ASR组件现在在后台线程初始化，完成后会自动启动唤醒词监听

        // 初始化音频波浪可视化组件
        initAudioWaveView()

        // 设置智能滚动监听器
        setupIntelligentScrollListener()
    }

    private fun initOk() {
        Log.i(TAG_NET, "========== initOk 开始 ==========")
        Log.i(TAG_NET, "数字人初始化完成，开始获取参数")

        try {
            // 数字人初始化完成后，调用parameters接口获取开场白和推荐问题
            fetchParameters()

            // 获取缓存的历史问题
            getCachedQuestions()
        } catch (e: Exception) {
            Log.e(TAG_NET, "获取参数失败: ${e.message}", e)
            runOnUiThread {
                Toast.makeText(this@CallActivity, "获取开场白失败，使用默认界面", Toast.LENGTH_SHORT).show()
                showDefaultInterface()
            }
        }

        Log.i(TAG_NET, "================================")
    }

    /**
     * 获取参数（开场白和推荐问题）
     */
    private fun fetchParameters() {
        Log.i(TAG_NET, "========== 开始获取参数 ==========")

        // 检查服务器地址是否配置
        if (ttsBaseUrl.isEmpty()) {
            Log.e(TAG_NET, "⚠️ 服务器地址为空！")
            runOnUiThread {
                Toast.makeText(this@CallActivity, "服务器地址未配置，请返回设置界面配置", Toast.LENGTH_LONG).show()
            }
            return
        }

        // 构建parameters接口URL
        val parametersUrl = "${ttsBaseUrl}/v2/parameters"
        Log.i(TAG_NET, "Parameters URL: $parametersUrl")

        // 获取API Key - 优先从SharedPreferences获取，如果为空则使用Intent传递的值
        val currentApiKey = sharedPrefs.getString(SettingsActivity.KEY_APIKEY, "") ?: ""
        if (currentApiKey.isEmpty()) {
            // 如果SharedPreferences中没有，尝试使用Intent传递的值
            val intentApiKey = apiKey
            Log.w(TAG_NET, "SharedPreferences中API Key为空，使用Intent传递的值: $intentApiKey")
            if (intentApiKey.isNotEmpty()) {
                // 将Intent传递的API Key保存到SharedPreferences
                sharedPrefs.edit().putString(SettingsActivity.KEY_APIKEY, intentApiKey).apply()
            }
        }

        val finalApiKey = if (currentApiKey.isNotEmpty()) currentApiKey else apiKey
        Log.i(TAG_NET, "最终使用的API Key: $finalApiKey")
        Log.i(TAG_NET, "API Key长度: ${finalApiKey.length}")

        if (finalApiKey.isEmpty()) {
            Log.e(TAG_NET, "⚠️ API Key为空！请检查设置界面是否正确选择了应用")
            runOnUiThread {
                Toast.makeText(this@CallActivity, "API Key为空，请返回设置界面选择应用", Toast.LENGTH_LONG).show()
            }
            return
        }

        // 构建请求
        val request = Request.Builder()
            .url(parametersUrl)
            .get()
            .addHeader("dify_api_key", finalApiKey)
            .addHeader("User-Agent", "Android App")
            .addHeader("Accept", "*/*")
            .addHeader("Connection", "keep-alive")
            .build()

        // 打印请求详情
        Log.i(TAG_NET, "========== Parameters请求详情 ==========")
        Log.i(TAG_NET, "请求方法: ${request.method}")
        Log.i(TAG_NET, "请求URL: ${request.url}")
        request.headers.forEach { (name, value) ->
            Log.i(TAG_NET, "请求头: $name = $value")
        }
        Log.i(TAG_NET, "=======================================")

        // 发送请求
        try {
            okHttpClient.newCall(request).enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    Log.e(TAG_NET, "Parameters请求失败: ${e.message}", e)
                    runOnUiThread {
                        Toast.makeText(this@CallActivity, "获取开场白失败，请检查服务器地址配置: ${e.message}", Toast.LENGTH_SHORT).show()
                        // 请求失败时，显示默认的界面，不要闪退
                        showDefaultInterface()
                    }
                }

                override fun onResponse(call: Call, response: Response) {
                    try {
                        Log.i(TAG_NET, "========== Parameters响应详情 ==========")
                        Log.i(TAG_NET, "响应状态码: ${response.code}")
                        Log.i(TAG_NET, "响应消息: ${response.message}")
                        response.headers.forEach { (name, value) ->
                            Log.i(TAG_NET, "响应头: $name = $value")
                        }

                        if (!response.isSuccessful) {
                            Log.e(TAG_NET, "Parameters请求失败，状态码: ${response.code}")
                            runOnUiThread {
                                Toast.makeText(this@CallActivity, "获取开场白失败，服务器返回错误: ${response.code}", Toast.LENGTH_SHORT).show()
                                showDefaultInterface()
                            }
                            return
                        }

                        val responseStr = response.body?.string()
                        Log.i(TAG_NET, "响应数据: $responseStr")
                        Log.i(TAG_NET, "=======================================")

                        if (responseStr != null && responseStr.isNotEmpty()) {
                            // 处理SSE格式的响应
                            processSseResponse(responseStr)
                        } else {
                            Log.e(TAG_NET, "Parameters响应体为空")
                            runOnUiThread {
                                Toast.makeText(this@CallActivity, "服务器返回空数据", Toast.LENGTH_SHORT).show()
                                showDefaultInterface()
                            }
                        }

                    } catch (e: Exception) {
                        Log.e(TAG_NET, "处理Parameters响应失败: ${e.message}", e)
                        runOnUiThread {
                            Toast.makeText(this@CallActivity, "处理响应失败: ${e.message}", Toast.LENGTH_SHORT).show()
                            showDefaultInterface()
                        }
                    }
                }
            })
        } catch (e: Exception) {
            Log.e(TAG_NET, "发送Parameters请求失败: ${e.message}", e)
            runOnUiThread {
                Toast.makeText(this@CallActivity, "发送请求失败: ${e.message}", Toast.LENGTH_SHORT).show()
                showDefaultInterface()
            }
        }
    }

    /**
     * 显示默认界面（当获取开场白失败时）
     */
    private fun showDefaultInterface() {
        Log.i(TAG_NET, "显示默认界面")
        runOnUiThread {
            try {
                // 显示默认提示，使用Toast而不是UI元素，避免UI元素不存在的问题
                Toast.makeText(this@CallActivity, "数字人已准备就绪，您可以开始对话", Toast.LENGTH_LONG).show()

                Log.i(TAG_NET, "默认界面显示完成")
            } catch (e: Exception) {
                Log.e(TAG_NET, "显示默认界面失败: ${e.message}", e)
            }
        }
    }

    /**
     * 重置到欢迎状态（重新请求开场白并隐藏AI回复界面）
     */
    private fun resetToWelcomeState() {
        Log.i(TAG_NET, "========== 重置到欢迎状态 ==========")

        runOnUiThread {
            try {
                // 1. 停止当前播放的音频
                currentSseCall?.cancel()
                currentSseCall = null

                // 2. 清空音频队列
                audioPlayQueue.clear()

                // 3. 重置播放状态
                isPlayingLlmAudio = false
                isProcessingLlmRequest = false
                currentPlayingAudioUrl = null

                // 4. 隐藏用户问题和AI回复界面
                binding.cvConversation.visibility = android.view.View.GONE

                // 5. 隐藏历史问题容器并清空历史问题数据
                binding.cvHistoryQuestions.visibility = android.view.View.GONE
                // 清空历史问题数据，确保重新获取时不会显示旧数据
                historyQuestions.clear()
                // 清空历史问题标签视图
                binding.llHistoryRow1.removeAllViews()
                binding.llHistoryRow2.removeAllViews()
                binding.llHistoryRow3.removeAllViews()
                Log.i(TAG_NET, "✓ 已清空历史问题数据和视图")

                // 6. 隐藏data事件问题
                binding.llLlmSuggestionsContainer.visibility = android.view.View.GONE

                // 6. 清空媒体项目
                clearMediaItems()

                // 8. 重置请求状态
                currentRequestType = "voice_input"
                Log.i(TAG_NET, "✓ 重置请求状态")

                // 9. 重置录音按钮状态和隐藏打断提示条
                updateRecordButton("等待唤醒")
                hideInterruptHint()  // 隐藏打断提示条

                // 重置音频波浪状态为静音
                setAudioWavePlaying(false)
                setAudioWaveRecording(false)
                setAudioWaveActive(false)

                // 10. 显示提示信息
                // Toast.makeText(this@CallActivity, "正在重新获取开场白...", Toast.LENGTH_SHORT).show()

                Log.i(TAG_NET, "界面重置完成，开始重新请求开场白")

                // 10. 确保背景图片正确显示
                try {
                    binding.ivBg.invalidate()
                    Log.i(TAG_NET, "✓ 重置时背景图片已刷新")
                } catch (e: Exception) {
                    Log.e(TAG_NET, "重置时背景图片刷新失败: ${e.message}")
                }

                // 11. 重新请求开场白
                fetchParameters()

                // 12. 获取缓存的历史问题
                getCachedQuestions()

                // 🔧 关键修复：重置后重新启动唤醒词监听（使用强制重启机制）
                Log.i(TAG_NET, "重置完成，重新启动唤醒词监听")
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    if (localAsrEnabled) {
                        // 🔧 使用与红色按钮相同的强制重启机制，解决状态不同步问题
                        if (!isKwsRecording) {
                            Log.i(TAG_NET, "重新启动唤醒词监听以支持重置后的唤醒")
                            startLocalKws()
                        } else {
                            Log.w(TAG_NET, "⚠ 检测到状态不同步，强制重新初始化唤醒词监听")
                            Log.w(TAG_NET, "当前状态 - isKwsRecording: $isKwsRecording, recordingState: ${kwsAudioRecord?.recordingState}")
                            isKwsRecording = false
                            startLocalKws()
                        }
                    } else {
                        Log.i(TAG_NET, "本地ASR未启用，跳过唤醒词监听重启")
                    }
                }, 1000) // 延迟1秒确保界面重置完成

            } catch (e: Exception) {
                Log.e(TAG_NET, "重置界面失败: ${e.message}", e)
                Toast.makeText(this@CallActivity, "重置失败: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }

        Log.i(TAG_NET, "========== 重置操作完成 ==========")
    }

    override fun onResume() {
        super.onResume()
        Log.i(TAG_NET, "========== CallActivity onResume ==========")
        Log.i(TAG_NET, "当前模型目录: $modelDir")
        Log.i(TAG_NET, "当前基础目录: $baseDir")

        // 更新reference_id，确保与当前选择的模型类别一致
        updateReferenceId()

        // 检查是否有新的模型参数传入（模型切换）
        val newModelDir = intent.getStringExtra("modelDir")
        val newBaseDir = intent.getStringExtra("baseDir")
        val forceReload = intent.getBooleanExtra("forceReload", false)
        Log.i(TAG_NET, "Intent中的新模型目录: $newModelDir")
        Log.i(TAG_NET, "Intent中的新基础目录: $newBaseDir")
        Log.i(TAG_NET, "强制重新加载: $forceReload")

        if ((newModelDir != null && newModelDir != modelDir) || forceReload) {
            Log.i(TAG_NET, "✓ 检测到模型切换需求 - 使用进程重启方案")
            Log.i(TAG_NET, "旧模型: $modelDir")
            Log.i(TAG_NET, "新模型: $newModelDir")
            Log.i(TAG_NET, "强制重新加载: $forceReload")

            // 使用进程重启方案彻底清理重影
            Log.i(TAG_NET, "启动进程重启方案以彻底清理重影...")
            restartProcessWithNewModel(newModelDir ?: modelDir, newBaseDir)
        } else {
            Log.i(TAG_NET, "无模型切换需求，正常恢复渲染")

            // 刷新背景图片（用户可能在背景选择界面更换了背景）
            // 只在非模型切换时才刷新背景，避免不必要的重复加载
            loadSelectedBackground()

            // 刷新Logo（用户可能在设置界面更换了Logo）
            loadSelectedLogo()

            // 重新应用动态布局设置（用户可能在布局调整界面更改了设置）
            binding.root.post {
                applyDynamicLayouts()
            }

            // 正常恢复OpenGL渲染
            try {
                binding.glTextureView.onResume()
                Log.i(TAG_NET, "✓ GLTextureView正常恢复成功")
            } catch (e: Exception) {
                Log.e(TAG_NET, "✗ GLTextureView正常恢复失败: ${e.message}", e)
            }

            // 恢复ASR监听（如果之前是活跃状态）
            restoreAsrStateAfterResume()

            // 恢复音频波浪动画
            resumeAudioWave()
        }
        Log.i(TAG_NET, "========== CallActivity onResume 完成 ==========")
    }

    /**
     * 通过重启进程来彻底清理重影问题
     * 这是解决全局静态变量g_digit累积状态的最彻底方案
     */
    private fun restartProcessWithNewModel(newModelDir: String, newBaseDir: String?) {
        Log.i(TAG_NET, "========== 开始进程重启方案 ==========")

        // 更新模型路径
        modelDir = newModelDir
        if (newBaseDir != null) {
            baseDir = newBaseDir
        }

        // 重启整个应用进程
        val restartIntent = Intent(this, CallActivity::class.java).apply {
            putExtra("modelDir", newModelDir)
            if (newBaseDir != null) {
                putExtra("baseDir", newBaseDir)
            }
            flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK
        }

        Log.i(TAG_NET, "重启应用进程以彻底清理重影")
        startActivity(restartIntent)

        // 杀死当前进程，强制重新加载所有native库
        android.os.Process.killProcess(android.os.Process.myPid())
    }

    override fun onPause() {
        super.onPause()
        Log.i(TAG_NET, "========== CallActivity onPause 开始 ==========")

        // 保存当前状态
        wasPlayingBeforePause = isPlayingLlmAudio || isProcessingRequest
        wasAsrActiveBeforePause = isAsrRecording
        wasKwsActiveBeforePause = isKwsRecording

        Log.i(TAG_NET, "保存状态 - 播放中: $wasPlayingBeforePause, ASR活跃: $wasAsrActiveBeforePause, KWS活跃: $wasKwsActiveBeforePause")

        // 1. 停止音频播放
        if (wasPlayingBeforePause) {
            Log.i(TAG_NET, "检测到音频播放中，执行停止操作")
            try {
                // 取消当前SSE连接
                currentSseCall?.cancel()
                currentSseCall = null

                // 停止数字人音频播放
                duix?.stopAudio()

                // 清空音频队列
                clearAudioQueue()

                // 重置播放状态
                isPlayingLlmAudio = false
                isProcessingRequest = false
                currentPlayingAudioUrl = null

                Log.i(TAG_NET, "✓ 音频播放已停止")
            } catch (e: Exception) {
                Log.e(TAG_NET, "停止音频播放失败: ${e.message}", e)
            }
        }

        // 2. 暂停ASR录音
        if (wasAsrActiveBeforePause) {
            Log.i(TAG_NET, "检测到ASR录音中，执行暂停操作")
            try {
                pauseAsrRecording()
                Log.i(TAG_NET, "✓ ASR录音已暂停")
            } catch (e: Exception) {
                Log.e(TAG_NET, "暂停ASR录音失败: ${e.message}", e)
            }
        }

        // 3. 暂停唤醒词监听
        if (wasKwsActiveBeforePause) {
            Log.i(TAG_NET, "检测到唤醒词监听中，执行暂停操作")
            try {
                isKwsRecording = false
                kwsAudioRecord?.stop()
                Log.i(TAG_NET, "✓ 唤醒词监听已暂停")
            } catch (e: Exception) {
                Log.e(TAG_NET, "暂停唤醒词监听失败: ${e.message}", e)
            }
        }

        // 4. 更新UI状态
        runOnUiThread {
            updateRecordButton("等待唤醒")
            hideInterruptHint()  // 隐藏打断提示条

            // 重置音频波浪状态并暂停动画
            setAudioWavePlaying(false)
            setAudioWaveRecording(false)
            setAudioWaveActive(false)
            pauseAudioWave()

            Log.i(TAG_NET, "✓ UI状态已重置")
        }

        // 5. 暂停OpenGL渲染，避免重影问题
        binding.glTextureView.onPause()

        Log.i(TAG_NET, "========== CallActivity onPause 完成 ==========")
    }

    override fun onStop() {
        super.onStop()
        Log.i(TAG_NET, "========== CallActivity onStop ==========")

        // 在Activity完全不可见时，确保所有音频和ASR活动都已停止
        // 这是一个额外的安全措施，防止任何遗漏的播放或监听

        try {
            // 强制停止音频播放
            duix?.stopAudio()

            // 取消SSE连接
            currentSseCall?.cancel()
            currentSseCall = null

            // 停止所有ASR活动
            isKwsRecording = false
            isAsrRecording = false

            kwsAudioRecord?.stop()
            asrAudioRecord?.stop()

            // 清空所有队列
            clearAudioQueue()

            Log.i(TAG_NET, "✓ 所有音频和ASR活动已在onStop中停止")
        } catch (e: Exception) {
            Log.e(TAG_NET, "onStop中停止活动失败: ${e.message}", e)
        }

        Log.i(TAG_NET, "========== CallActivity onStop 完成 ==========")
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        Log.i(TAG_NET, "========== onActivityResult ==========")
        Log.i(TAG_NET, "请求码: $requestCode, 结果码: $resultCode")

        when (requestCode) {
            MediaAdapter.REQUEST_CODE_IMAGE_VIEWER -> {
                Log.i(TAG_NET, "图片查看器返回")
                checkAndRestartReturnTimer()
            }
            MediaAdapter.REQUEST_CODE_VIDEO_PLAYER -> {
                Log.i(TAG_NET, "视频播放器返回")
                checkAndRestartReturnTimer()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        // 停止滚动动画
        stopScrollAnimations()

        // 取消返回开场白定时器
        cancelReturnToGreetingTimer()

        // 取消滚动交互检测
        cancelScrollInteractionDetection()

        duix?.release()
        mDUIXRender?.release()

        // 🔧 停止唤醒词监听监控
        stopKwsMonitoring()

        // 释放本地ASR资源
        isKwsRecording = false
        kwsAudioRecord?.stop()
        kwsAudioRecord?.release()
        kwsAudioRecord = null
        keywordSpotter?.release()
        kwsStream?.release()

        // 释放ASR识别资源
        cleanupAsrRecording()
        asrOfflineRecognizer?.release()
        asrVad?.release()
    }

    /**
     * 播放16k采样率单通道16位深的wav本地文件
     */
    private fun playWav() {
        val wavName = "voice4.wav"
        val wavDir = File(mContext.getExternalFilesDir("duix"), "wav")
        if (!wavDir.exists()) {
            wavDir.mkdirs()
        }
        val wavFile = File(wavDir, wavName)
        if (!wavFile.exists()) { // 拷贝到sdcard
            val executor = Executors.newSingleThreadExecutor()
            executor.execute {
                val input = mContext.assets.open("wav/${wavName}")
                val out: OutputStream = FileOutputStream("${wavFile.absolutePath}.tmp")
                val buffer = ByteArray(1024)
                var read: Int
                while (input.read(buffer).also { read = it } != -1) {
                    out.write(buffer, 0, read)
                }
                input.close()
                out.close()
                File("${wavFile.absolutePath}.tmp").renameTo(wavFile)
                duix?.playAudio(wavFile.absolutePath)
            }
        } else {
            duix?.playAudio(wavFile.absolutePath)
        }
    }

    private fun playWav(wavName: String) {
        val wavDir = File(mContext.getExternalFilesDir("duix"), "wav")
        if (!wavDir.exists()) {
            wavDir.mkdirs()
        }
        val wavFile = File(wavDir, wavName)

        Log.i(TAG_NET, "========== playWav 调试信息 ==========")
        Log.i(TAG_NET, "请求播放文件: $wavName")
        Log.i(TAG_NET, "外部存储路径: ${wavFile.absolutePath}")
        Log.i(TAG_NET, "外部存储文件存在: ${wavFile.exists()}")

        if (wavFile.exists()) {
            val fileSize = wavFile.length()
            val lastModified = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
                .format(java.util.Date(wavFile.lastModified()))
            Log.i(TAG_NET, "外部存储文件大小: $fileSize 字节")
            Log.i(TAG_NET, "外部存储文件修改时间: $lastModified")
            Log.i(TAG_NET, "⚠ 使用外部存储中的缓存文件（可能是旧版本）")
        }

        // 检查assets中的文件信息并处理缓存问题
        var needsUpdate = false
        try {
            val inputStream = assets.open("wav/$wavName")
            val assetsFileSize = inputStream.available()
            inputStream.close()
            Log.i(TAG_NET, "Assets文件大小: $assetsFileSize 字节")

            if (wavFile.exists() && wavFile.length() != assetsFileSize.toLong()) {
                Log.w(TAG_NET, "⚠ 警告：外部存储文件大小与assets不匹配！")
                Log.w(TAG_NET, "  外部存储: ${wavFile.length()} 字节")
                Log.w(TAG_NET, "  Assets: $assetsFileSize 字节")
                Log.w(TAG_NET, "  检测到缓存问题，将强制更新文件！")
                needsUpdate = true
            }
        } catch (e: Exception) {
            Log.e(TAG_NET, "无法读取assets文件信息: $e")
        }

        if (!wavFile.exists() || needsUpdate) { // 拷贝到sdcard或强制更新
            if (!wavFile.exists()) {
                Log.i(TAG_NET, "文件不存在，从assets复制...")
            } else {
                Log.i(TAG_NET, "检测到文件版本不匹配，强制从assets重新复制...")
                // 删除旧文件
                if (wavFile.delete()) {
                    Log.i(TAG_NET, "✓ 已删除旧版本文件")
                } else {
                    Log.w(TAG_NET, "⚠ 删除旧版本文件失败")
                }
            }

            val executor = Executors.newSingleThreadExecutor()
            executor.execute {
                try {
                    val input = mContext.assets.open("wav/${wavName}")
                    val out: OutputStream = FileOutputStream("${wavFile.absolutePath}.tmp")
                    val buffer = ByteArray(1024)
                    var read: Int
                    while (input.read(buffer).also { read = it } != -1) {
                        out.write(buffer, 0, read)
                    }
                    input.close()
                    out.close()

                    val tmpFile = File("${wavFile.absolutePath}.tmp")
                    if (tmpFile.renameTo(wavFile)) {
                        Log.i(TAG_NET, "✓ 文件复制完成，新文件大小: ${wavFile.length()} 字节")
                        Log.i(TAG_NET, "✓ 开始播放更新后的文件")
                        duix?.playAudio(wavFile.absolutePath)
                    } else {
                        Log.e(TAG_NET, "❌ 文件重命名失败")
                    }
                } catch (e: Exception) {
                    Log.e(TAG_NET, "❌ 文件复制失败: ${e.message}", e)
                }
            }
        } else {
            Log.i(TAG_NET, "直接播放外部存储中的文件（文件大小匹配）")
            duix?.playAudio(wavFile.absolutePath)
        }
        Log.i(TAG_NET, "========== playWav 结束 ==========")
    }

    private fun initMinerva() {
        if (!checkRecordPermission()) {
            Toast.makeText(this, "缺少录音权限", Toast.LENGTH_SHORT).show()
            return
        }
    }

    /**
     * 初始化媒体适配器
     */
    private fun initMediaAdapter() {
        mediaAdapter = MediaAdapter(this, mediaItems)

        // 设置媒体点击监听器
        mediaAdapter.setOnMediaClickListener(object : MediaAdapter.OnMediaClickListener {
            override fun onMediaClicked(mediaItem: MediaDisplayItem) {
                onMediaItemClicked(mediaItem)
            }

            override fun onMediaViewerStarted(requestCode: Int) {
                // 媒体查看器启动时的额外处理（如果需要）
                Log.i(TAG_NET, "媒体查看器已启动，请求码: $requestCode")
            }
        })

        binding.rvMediaItems.apply {
            layoutManager = LinearLayoutManager(this@CallActivity, LinearLayoutManager.HORIZONTAL, false)
            adapter = mediaAdapter
        }
        Log.i(TAG_NET, "✓ 媒体适配器初始化完成")
    }





    private fun updateRecordButton(text: String) {
        Log.i(TAG_NET, "updateRecordButton 被调用，状态: $text")
        runOnUiThread {
            // 隐藏状态指示器，不显示"正在录音"等文字
            binding.tvStatusIndicator.visibility = android.view.View.GONE

            // 根据状态更新按钮图标和动画，但不显示文字状态
            when (text) {
                "等待唤醒" -> {
                    binding.btnRecord.text = "🎤"
                    binding.btnRecord.isEnabled = true
                    binding.btnRecord.clearAnimation()
                    // 使用蓝色背景（默认状态）
                    binding.btnRecord.setBackgroundResource(R.drawable.bg_digital_record_button)
                    // 恢复正常文本颜色
                    binding.btnRecord.setTextColor(ContextCompat.getColor(this, R.color.digital_text_primary))
                    Log.i(TAG_NET, "✓ 按钮设置为等待唤醒状态（蓝色）")
                }
                "正在录音" -> {
                    binding.btnRecord.text = "🎤"
                    binding.btnRecord.isEnabled = true
                    binding.btnRecord.clearAnimation()
                    // 使用绿色背景（录音状态）
                    binding.btnRecord.setBackgroundResource(R.drawable.bg_digital_record_button_green)
                    // 设置白色文本颜色
                    binding.btnRecord.setTextColor(android.graphics.Color.WHITE)
                    // 添加录音波形动画
                    val pulseAnimation = android.view.animation.AnimationUtils.loadAnimation(this, R.anim.pulse_animation)
                    binding.btnRecord.startAnimation(pulseAnimation)
                    Log.i(TAG_NET, "✓ 按钮设置为正在录音状态（绿色）")
                }
                "处理中..." -> {
                    binding.btnRecord.text = "⏳"
                    binding.btnRecord.isEnabled = false
                    binding.btnRecord.clearAnimation()
                    // 恢复正常背景
                    binding.btnRecord.setBackgroundResource(R.drawable.bg_digital_record_button)
                    // 恢复正常文本颜色
                    binding.btnRecord.setTextColor(ContextCompat.getColor(this, R.color.digital_text_primary))
                }
                "播放中..." -> {
                    Log.i(TAG_NET, "设置按钮为播放中状态 - 红色麦克风图标")
                    binding.btnRecord.text = "🎤"  // 红色麦克风图标，用于打断播报
                    binding.btnRecord.isEnabled = true  // 可点击用于打断
                    binding.btnRecord.clearAnimation()

                    // 直接使用红色drawable资源
                    binding.btnRecord.setBackgroundResource(R.drawable.bg_digital_record_button_red)
                    Log.i(TAG_NET, "✓ 使用红色drawable资源设置背景")

                    // 设置文本颜色为白色，确保可见性
                    binding.btnRecord.setTextColor(android.graphics.Color.WHITE)
                    Log.i(TAG_NET, "✓ 设置文本颜色为白色")

                    // 强制刷新按钮状态
                    binding.btnRecord.invalidate()
                    binding.btnRecord.requestLayout()
                    Log.i(TAG_NET, "✓ 按钮状态已更新为红色背景")
                }
                "点击开始录音" -> {
                    binding.btnRecord.text = "🎤"
                    binding.btnRecord.isEnabled = true
                    binding.btnRecord.clearAnimation()
                    // 使用蓝色背景，提示用户可以点击录音
                    binding.btnRecord.setBackgroundResource(R.drawable.bg_digital_record_button)
                    // 设置正常文本颜色
                    binding.btnRecord.setTextColor(ContextCompat.getColor(this, R.color.digital_text_primary))
                    Log.i(TAG_NET, "✓ 按钮设置为点击录音状态（蓝色）")
                }
                else -> {
                    binding.btnRecord.text = "🎤"
                    binding.btnRecord.isEnabled = true
                    binding.btnRecord.clearAnimation()
                    // 恢复正常背景
                    binding.btnRecord.setBackgroundResource(R.drawable.bg_digital_record_button)
                    // 恢复正常文本颜色
                    binding.btnRecord.setTextColor(ContextCompat.getColor(this, R.color.digital_text_primary))
                }
            }
        }
    }

    private fun playWaitVoice() {
        if (voiceType == SettingsActivity.woman_key) {
            playWav("wait_woman.mp3")
        } else {
            playWav("wait_man.mp3")
        }
    }

    /**
     * 播放唤醒词回复音频
     */
    private fun playWakeupReplyAudio() {
        // 检查是否启用了唤醒词回复
        val enableWakeupReply = sharedPrefs.getBoolean(SettingsActivity.KEY_WAKEUP_REPLY, true)
        if (!enableWakeupReply) {
            Log.i(TAG_NET, "唤醒词回复已禁用，跳过播放")
            return
        }

        // 确保reference_id是最新的
        updateReferenceId()

        // 使用更新后的reference_id
        val currentReferenceId = reference_id ?: "man"

        // 根据当前reference_id选择对应的音频文件
        Log.i(TAG_NET, "========== 音频文件选择调试 ==========")
        Log.i(TAG_NET, "当前reference_id值: '$currentReferenceId'")
        Log.i(TAG_NET, "开始匹配音频文件...")

        val audioFileName = when (currentReferenceId) {
            "woman" -> {
                Log.i(TAG_NET, "✓ 匹配到 'woman'，选择女声文件")
                "我在.wav"      // 女声版本
            }
            "man" -> {
                Log.i(TAG_NET, "✓ 匹配到 'man'，选择男声文件")
                "我在_男.wav"     // 男声版本
            }
            else -> {
                Log.i(TAG_NET, "⚠ 未匹配到已知值，使用默认女声文件")
                "我在.wav"         // 默认女声版本
            }
        }

        Log.i(TAG_NET, "最终选择的音频文件: '$audioFileName'")
        Log.i(TAG_NET, "音频格式: 16kHz单声道")

        // 验证文件是否存在
        try {
            val inputStream = assets.open("wav/$audioFileName")
            val fileSize = inputStream.available()
            inputStream.close()
            Log.i(TAG_NET, "✓ 音频文件存在，大小: $fileSize 字节")
        } catch (e: Exception) {
            Log.e(TAG_NET, "❌ 音频文件不存在或无法读取: $audioFileName", e)
        }

        Log.i(TAG_NET, "========== 开始播放音频 ==========")

        try {
            // 先暂停ASR录音，避免录入自己的声音
            pauseAsrRecording()

            playWav(audioFileName)

            // 从设置中获取等待时间
            val wakeupDelay = sharedPrefs.getInt(SettingsActivity.KEY_WAKEUP_DELAY, 1500).toLong()
            Log.i(TAG_NET, "使用自定义等待时间: ${wakeupDelay}ms")

            // 延迟恢复ASR录音，等音频播放完成
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                resumeAsrRecording()
            }, wakeupDelay)

        } catch (e: Exception) {
            Log.e(TAG_NET, "播放唤醒词回复音频失败: ${e.message}", e)
            // 如果播放失败，也要恢复ASR录音
            resumeAsrRecording()
        }
    }

    /**
     * 暂停ASR录音（避免录入数字人自己的声音）
     */
    private fun pauseAsrRecording() {
        try {
            if (isAsrRecording) {
                Log.i(TAG_NET, "暂停ASR录音，避免录入数字人声音")
                isAsrRecording = false
                asrAudioRecord?.stop()
            }
        } catch (e: Exception) {
            Log.e(TAG_NET, "暂停ASR录音失败: ${e.message}", e)
        }
    }

    /**
     * 恢复ASR录音
     */
    private fun resumeAsrRecording() {
        try {
            if (!isAsrRecording && asrAudioRecord != null) {
                Log.i(TAG_NET, "恢复ASR录音")
                asrAudioRecord?.startRecording()
                isAsrRecording = true
            }
        } catch (e: Exception) {
            Log.e(TAG_NET, "恢复ASR录音失败: ${e.message}", e)
        }
    }

    private fun sendAudioToServer(audioFile: File) {
        Log.i(TAG_NET, "========== sendAudioToServer 开始 ==========")
        Log.i(TAG_NET, "音频文件路径: ${audioFile.absolutePath}")
        Log.i(TAG_NET, "音频文件存在: ${audioFile.exists()}")
        Log.i(TAG_NET, "音频文件大小: ${audioFile.length()} bytes")

        isProcessingRequest = true  // 开始处理请求
        Log.i(TAG_NET, "✓ 设置isProcessingRequest=true")

        runOnUiThread {
            binding.btnRecord.isEnabled = false
            binding.btnRecord.text = "处理中..."
            Log.i(TAG_NET, "✓ UI更新：按钮禁用，文本改为'处理中...'")
        }

        // 构建完整的TTS URL
        val fullTtsUrl = ApiConfig.buildUrl(ttsBaseUrl, ApiConfig.TTS.TTS_ENDPOINT)

        // 打印请求信息
        Log.i(TAG_NET, "开始发送请求")
        LogUtils.getInstance(this).log("基础URL: $ttsBaseUrl")
        LogUtils.getInstance(this).log("完整请求URL: $fullTtsUrl")
        Log.i(TAG_NET, "基础URL: $ttsBaseUrl")
        Log.i(TAG_NET, "完整请求URL: $fullTtsUrl")
        Log.i(TAG_NET, "音频文件: ${audioFile.absolutePath}")
        Log.i(TAG_NET, "文件大小: ${audioFile.length()} bytes")

        // 获取用户在设置界面输入的用户ID
        val userId = sharedPrefs.getString(SettingsActivity.KEY_USER_ID, SettingsActivity.DEFAULT_USER_ID) ?: SettingsActivity.DEFAULT_USER_ID

        Log.i(TAG_NET, "DIFY_API_KEY: $apiKey ")
        Log.i(TAG_NET, "REFERENCE_ID: ${reference_id ?: ""} ")
        Log.i(TAG_NET, "USER_ID: $userId ")
        val requestBody = MultipartBody.Builder().setType(MultipartBody.FORM)
            .addFormDataPart("audio_file", audioFile.name, audioFile.asRequestBody("audio/wav".toMediaTypeOrNull()))
            .build()

        val requestBuilder = Request.Builder().url(fullTtsUrl).post(requestBody)

        // 添加请求头（使用小写格式，与curl示例保持一致）
        requestBuilder.addHeader("dify_api_key", apiKey)
        requestBuilder.addHeader("reference_id", reference_id ?: "")
        requestBuilder.addHeader("user_id", userId)

        val request = requestBuilder.build()

        // 详细打印请求头信息
        Log.i(TAG_NET, "========== 请求头详情 ==========")
        Log.i(TAG_NET, "请求方法: ${request.method}")
        Log.i(TAG_NET, "请求URL: ${request.url}")
        request.headers.forEach { (name, value) ->
            Log.i(TAG_NET, "请求头: $name = $value")
        }
        Log.i(TAG_NET, "请求体类型: ${request.body?.contentType()}")
        Log.i(TAG_NET, "请求体大小: ${request.body?.contentLength()} bytes")
        Log.i(TAG_NET, "================================")

        okHttpClient.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                LogUtils.getInstance(this@CallActivity).log("请求失败: ${e.message}")
                Log.e(TAG_NET, "请求失败: ${e.message}")
                Log.e(TAG_NET, "失败URL: ${call.request().url}")
                Log.e(TAG_NET, "异常堆栈:", e)
                runOnUiThread {
                    Toast.makeText(this@CallActivity, "网络请求失败: ${e.message}", Toast.LENGTH_SHORT)
                        .show()
                    isProcessingRequest = false  // 请求失败，重置状态
                    binding.btnRecord.isEnabled = true
                    updateRecordButton("等待唤醒")
                }
            }

            override fun onResponse(call: Call, response: Response) {
                try {
                    Log.i(TAG_NET, "========== 响应详情 ==========")
                    Log.i(TAG_NET, "响应状态码: ${response.code}")
                    Log.i(TAG_NET, "响应消息: ${response.message}")
                    Log.i(TAG_NET, "响应头数量: ${response.headers.size}")
                    response.headers.forEach { (name, value) ->
                        Log.i(TAG_NET, "响应头: $name = $value")
                    }
                    Log.i(TAG_NET, "==============================")

                    Log.i(TAG_NET, "========== 开始流式处理SSE响应 ==========")

                    // 使用流式处理SSE响应
                    processStreamingSseResponse(response)

                    Log.i(TAG_NET, "========== 流式SSE处理完成 ==========")
                } catch (e: Exception) {
                    Log.e(TAG_NET, "解析响应数据失败: ${e.message}")
                    Log.e(TAG_NET, "异常堆栈:", e)
                    runOnUiThread {
                        isProcessingRequest = false  // 解析失败，重置状态
                        binding.btnRecord.isEnabled = true
                        updateRecordButton("正在录音")
                    }
                }
            }
        })
    }

    private fun checkRecordPermission(): Boolean {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED
    }

    private fun requestRecordPermission() {
        ActivityCompat.requestPermissions(this, arrayOf(Manifest.permission.RECORD_AUDIO), PERMISSION_REQUEST_CODE)
    }

    override fun onRequestPermissionsResult(requestCode: Int,
                                            permissions: Array<out String>,
                                            grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PERMISSION_REQUEST_CODE) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                initMinerva()
            } else {
                Toast.makeText(this, "需要录音权限才能使用该功能", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * 下载并播放原有TTS音频（用于语音录音后的回复）
     */
    private fun downloadAudioAndPlay(audioUrl: String) {
        Log.i(TAG_NET, "开始下载TTS音频: $audioUrl")
        val request = Request.Builder().url(audioUrl).get().build()

        okHttpClient.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e(TAG_NET, "TTS音频下载失败: ${e.message}")
                Log.e(TAG_NET, "失败URL: ${call.request().url}")
                Log.e(TAG_NET, "异常堆栈:", e)
                runOnUiThread {
                    Toast.makeText(this@CallActivity, "音频下载失败: ${e.message}", Toast.LENGTH_SHORT)
                        .show()
                    isProcessingRequest = false
                    binding.btnRecord.isEnabled = true
                    updateRecordButton("等待唤醒")
                }
            }

            override fun onResponse(call: Call, response: Response) {
                try {
                    response.body?.let { responseBody -> 
                        // 创建保存音频的目录
                        val audioDir = File(mContext.getExternalFilesDir("duix"), "audio")
                        if (!audioDir.exists()) {
                            audioDir.mkdirs()
                        }

                        // 生成本地文件名
                        val fileName = "tts_response_${System.currentTimeMillis()}.wav"
                        val localFile = File(audioDir, fileName)

                        // 保存音频文件
                        localFile.outputStream().use { fileOut ->
                            responseBody.byteStream().use { bodyIn ->
                                bodyIn.copyTo(fileOut)
                            }
                        }

                        Log.i(TAG_NET, "TTS音频下载完成，保存至: ${localFile.absolutePath}")

                        // 播放本地音频文件（通过DUIX数字人播放）
                        runOnUiThread {
                            duix?.playAudio(localFile.absolutePath)
                        }
                    } ?: run {
                        Log.e(TAG_NET, "TTS下载响应体为空")
                        runOnUiThread {
                            Toast.makeText(this@CallActivity, "下载响应体为空", Toast.LENGTH_SHORT)
                                .show()
                            isProcessingRequest = false
                            binding.btnRecord.isEnabled = true
                            updateRecordButton("等待唤醒")
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG_NET, "保存TTS音频文件失败: ${e.message}")
                    Log.e(TAG_NET, "异常堆栈:", e)
                    runOnUiThread {
                        Toast.makeText(this@CallActivity, "保存音频文件失败: ${e.message}", Toast.LENGTH_SHORT)
                            .show()
                        isProcessingRequest = false
                        binding.btnRecord.isEnabled = true
                        updateRecordButton("等待唤醒")
                    }
                }
            }
        })
    }

    /**
     * 下载并播放LLM音频（独立的播放器，不影响数字人状态）
     */
    private fun downloadAndPlayLlmAudio(audioUrl: String) {
        Log.i(TAG_NET, "开始下载LLM音频: $audioUrl")
        val request = Request.Builder().url(audioUrl).get().build()

        okHttpClient.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e(TAG_NET, "LLM音频下载失败: ${e.message}")
                Log.e(TAG_NET, "失败URL: ${call.request().url}")
                Log.e(TAG_NET, "异常堆栈:", e)
                onLlmAudioPlayError("下载失败: ${e.message}")
            }

            override fun onResponse(call: Call, response: Response) {
                try {
                    response.body?.let { responseBody -> 
                        // 创建保存LLM音频的目录
                        val audioDir = File(mContext.getExternalFilesDir("duix"), "llm_audio")
                        if (!audioDir.exists()) {
                            audioDir.mkdirs()
                        }

                        // 生成本地文件名
                        val fileName = "llm_${System.currentTimeMillis()}.wav"
                        val localFile = File(audioDir, fileName)

                        // 保存音频文件
                        localFile.outputStream().use { fileOut ->
                            responseBody.byteStream().use { bodyIn ->
                                bodyIn.copyTo(fileOut)
                            }
                        }

                        Log.i(TAG_NET, "LLM音频下载完成，保存至: ${localFile.absolutePath}")

                        // 播放LLM音频（通过DUIX播放，但标记为LLM音频）
                        runOnUiThread {
                            playLlmAudioFile(localFile.absolutePath)
                        }
                    } ?: run {
                        Log.e(TAG_NET, "LLM音频下载响应体为空")
                        onLlmAudioPlayError("响应体为空")
                    }
                } catch (e: Exception) {
                    Log.e(TAG_NET, "保存LLM音频文件失败: ${e.message}")
                    Log.e(TAG_NET, "异常堆栈:", e)
                    onLlmAudioPlayError("保存失败: ${e.message}")
                }
            }
        })
    }



    /**
     * 播放LLM音频文件
     */
    private fun playLlmAudioFile(audioFilePath: String) {
        Log.i(TAG_NET, "播放LLM音频文件: $audioFilePath")
        try {
            // 使用DUIX播放音频，但需要监听播放完成事件
            duix?.playAudio(audioFilePath)

            // 注意：这里需要监听DUIX的播放完成回调
            // 由于我们已经在onCreate中设置了duix的回调监听器
            // 我们需要在那里区分是TTS音频还是LLM音频的播放完成

        } catch (e: Exception) {
            Log.e(TAG_NET, "播放LLM音频失败: ${e.message}", e)
            onLlmAudioPlayError("播放失败: ${e.message}")
        }
    }

    private fun checkDirectoriesAndFiles() {
        Log.d("CallActivity", "=== 开始检查目录和文件状态 ===")

        // 检查baseDir
        val baseDirFile = File(baseDir)
        Log.d("CallActivity", "baseDir存在: ${baseDirFile.exists()}")
        if (baseDirFile.exists()) {
            Log.d("CallActivity", "baseDir文件数量: ${baseDirFile.listFiles()?.size ?: 0}")
            // 注释掉详细内容以减少日志
            // Log.d("CallActivity", "baseDir内容: ${baseDirFile.listFiles()?.map { it.name }}")
        }

        // 检查modelDir
        val modelDirFile = File(modelDir)
        Log.d("CallActivity", "modelDir存在: ${modelDirFile.exists()}")
        if (modelDirFile.exists()) {
            Log.d("CallActivity", "modelDir文件数量: ${modelDirFile.listFiles()?.size ?: 0}")
            // 注释掉详细内容以减少日志
            // Log.d("CallActivity", "modelDir内容: ${modelDirFile.listFiles()?.map { it.name }}")
        }

        // 检查关键文件
        val keyFiles = listOf("bbox.j", "config.j", "db", "dp", "wm")
        keyFiles.forEach { fileName ->
            val file = File(modelDir, fileName)
            Log.d("CallActivity", "关键文件 $fileName 存在: ${file.exists()}")
        }

        Log.d("CallActivity", "=== 目录和文件状态检查完成 ===")
    }

    /**
     * 检查模型文件结构
     */
    private fun checkModelFiles(modelDir: File) {
        Log.i(TAG_NET, "检查模型文件结构: ${modelDir.absolutePath}")

        // 递归检查所有文件（注释掉详细日志以减少输出）
        fun checkDirectory(dir: File, level: Int = 0) {
            val indent = "  ".repeat(level)
            dir.listFiles()?.forEach { file ->
                if (file.isDirectory) {
                    // Log.i(TAG_NET, "${indent}目录: ${file.name}/")
                    checkDirectory(file, level + 1)
                } else {
                    val extension = file.extension.lowercase()
                    // Log.i(TAG_NET, "${indent}文件: ${file.name} (${file.length()} bytes, 类型: $extension)")

                    // 检查关键文件类型（注释掉详细日志）
                    /*
                    when (extension) {
                        "json" -> Log.i(TAG_NET, "${indent}  -> JSON配置文件")
                        "bin", "dat" -> Log.i(TAG_NET, "${indent}  -> 模型数据文件")
                        "jpg", "jpeg", "png" -> Log.i(TAG_NET, "${indent}  -> 图片文件")
                        "mp3", "wav" -> Log.i(TAG_NET, "${indent}  -> 音频文件")
                        else -> Log.i(TAG_NET, "${indent}  -> 其他文件")
                    }
                    */
                }
            }
        }

        checkDirectory(modelDir)

        // 检查是否有必要的文件
        val hasJsonFiles = modelDir.walkTopDown().any { it.extension.lowercase() == "json" }
        val hasBinFiles = modelDir.walkTopDown().any { it.extension.lowercase() in listOf("bin", "dat") }

        Log.i(TAG_NET, "模型文件检查结果:")
        Log.i(TAG_NET, "  包含JSON文件: $hasJsonFiles")
        Log.i(TAG_NET, "  包含模型数据文件: $hasBinFiles")

        if (!hasJsonFiles) {
            Log.w(TAG_NET, "警告: 未找到JSON配置文件")
        }
        if (!hasBinFiles) {
            Log.w(TAG_NET, "警告: 未找到模型数据文件")
        }
    }

    /**
     * 验证模型目录完整性
     */
    private fun validateModelDirectory(modelDir: File): Boolean {
        if (!modelDir.exists() || !modelDir.isDirectory) {
            Log.e(TAG_NET, "模型目录不存在或不是目录: ${modelDir.absolutePath}")
            return false
        }

        // 检查uuid文件
        val uuidFile = File(modelDir, "uuid")
        if (!uuidFile.exists()) {
            Log.e(TAG_NET, "uuid文件不存在")
            return false
        }

        // 检查关键模型文件（根据DUIX SDK的要求）
        val files = modelDir.listFiles() ?: return false
        val fileNames = files.map { it.name }

        Log.i(TAG_NET, "当前模型文件列表: ${fileNames.joinToString(", ")}")

        // 根据ModelInfo.java检查必需的文件
        val requiredFiles = mapOf(
            "config.j" to "配置文件",
            "bbox.j" to "边界框文件",
            "dh_model.b" to "模型二进制文件",
            "dh_model.p" to "模型参数文件",
            "uuid" to "UUID文件"
        )

        // 检查DUIX SDK需要的转换后文件名（根据MD5映射）
        val expectedConvertedFiles = mapOf(
            "db" to "模型二进制文件(转换后)",
            "dp" to "模型参数文件(转换后)",
            "bj" to "边界框文件(转换后)",
            "cj" to "配置文件(转换后)"
        )

        var allRequiredExists = true

        // 检查原始文件
        requiredFiles.forEach { (fileName, description) ->
            val exists = fileNames.contains(fileName)
            Log.i(TAG_NET, "  $description ($fileName): ${if (exists) "存在" else "缺失"}")
            if (!exists && fileName != "uuid") { // uuid文件已经单独检查过了
                allRequiredExists = false
            }
        }

        // 检查转换后的文件（DUIX SDK可能需要这些）
        expectedConvertedFiles.forEach { (fileName, description) ->
            val exists = fileNames.contains(fileName)
            Log.i(TAG_NET, "  $description ($fileName): ${if (exists) "存在" else "缺失"}")
        }

        if (!allRequiredExists) {
            Log.e(TAG_NET, "模型目录缺少必需的文件")
            return false
        }

        Log.i(TAG_NET, "模型目录验证通过")
        return true
    }

    // 记录当前加载的背景URL，避免重复加载
    private var currentBackgroundUrl: String? = null

    // 标记用户是否手动打断了音频播放
    private var isAudioInterrupted = false

    // 🔧 标记是否为手动打断（区分手动点击和语音唤醒打断）
    private var isManualInterrupt = false
    
    // Logo菜单相关变量
    private var isMenuExpanded = false

    // 🔧 修复说明：统一使用强制重启机制解决AI回复时唤醒词无法打断的问题
    // 问题原因：isKwsRecording标志与实际AudioRecord状态可能不同步，导致音频播放完成后不会重启唤醒词监听
    // 解决方案：在所有音频播放完成后，使用与红色按钮相同的强制重启机制

    /**
     * 加载用户选择的背景图片
     */
    private fun loadSelectedBackground() {
        val backgroundUrl = sharedPrefs.getString(BackgroundSelectionActivity.KEY_SELECTED_BACKGROUND_URL, "")
        Log.i(TAG_NET, "准备加载背景图片: $backgroundUrl")
        Log.i(TAG_NET, "当前背景URL: $currentBackgroundUrl")

        // 检查是否需要重新加载背景
        if (currentBackgroundUrl == backgroundUrl) {
            Log.i(TAG_NET, "背景URL未变化，跳过重复加载")
            return
        }

        currentBackgroundUrl = backgroundUrl
        Log.i(TAG_NET, "开始加载新背景: $backgroundUrl")

        if (!backgroundUrl.isNullOrEmpty()) {
            // 加载用户选择的背景
            Glide.with(mContext)
                .load(backgroundUrl)
                .centerCrop()
                .placeholder(binding.ivBg.drawable) // 使用当前图片作为占位符，避免闪烁
                .into(binding.ivBg)
            Log.i(TAG_NET, "✓ 已加载用户选择的背景: $backgroundUrl")
        } else {
            // 加载默认背景
            val defaultUrl = "file:///android_asset/bg/bg1.png"
            Glide.with(mContext)
                .load(defaultUrl)
                .centerCrop()
                .placeholder(binding.ivBg.drawable) // 使用当前图片作为占位符，避免闪烁
                .into(binding.ivBg)
            Log.i(TAG_NET, "✓ 已加载默认背景: $defaultUrl")
        }
    }

    /**
     * 加载用户选择的Logo图片
     */
    private fun loadSelectedLogo() {
        val logoUrl = sharedPrefs.getString(SettingsActivity.KEY_SELECTED_LOGO_URL, "")
        val logoName = sharedPrefs.getString(SettingsActivity.KEY_SELECTED_LOGO_NAME, "")
        Log.i(TAG_NET, "准备加载Logo图片: $logoUrl (名称: $logoName)")

        if (!logoUrl.isNullOrEmpty()) {
            // 加载用户选择的Logo
            try {
                Glide.with(mContext)
                    .load(logoUrl)
                    .centerInside()
                    .placeholder(binding.ivLogo.drawable) // 使用当前图片作为占位符，避免闪烁
                    .error(R.drawable.logo) // 加载失败时使用默认logo
                    .into(binding.ivLogo)
                Log.i(TAG_NET, "✓ 已加载用户选择的Logo: $logoUrl")
            } catch (e: Exception) {
                Log.e(TAG_NET, "加载用户Logo失败: ${e.message}", e)
                // 加载失败时使用默认logo
                binding.ivLogo.setImageResource(R.drawable.logo)
            }
        } else {
            // 使用默认Logo
            binding.ivLogo.setImageResource(R.drawable.logo)
            Log.i(TAG_NET, "✓ 已加载默认Logo")
        }
    }

    /**
     * 显示开场白
     */
    private fun showOpeningStatement(openingStatement: String) {
        Log.i(TAG_NET, "========== 显示开场白 ==========")
        Log.i(TAG_NET, "开场白内容: $openingStatement")
        Log.i(TAG_NET, "开场白长度: ${openingStatement.length}")

        runOnUiThread {
            try {
                binding.tvOpeningStatement.text = openingStatement
                binding.cvOpeningStatement.visibility = android.view.View.VISIBLE

                // 显示唤醒词提示
                showWakeWordHint()

                // 添加淡入动画效果
                val fadeInAnimation = android.view.animation.AnimationUtils.loadAnimation(this, R.anim.fade_in_animation)
                binding.cvOpeningStatement.startAnimation(fadeInAnimation)

                Log.i(TAG_NET, "✓ 开场白UI更新成功")

                // 显示历史问题标签云
                showHistoryQuestions()

            } catch (e: Exception) {
                Log.e(TAG_NET, "✗ 开场白UI更新失败: ${e.message}", e)
            }
        }
        Log.i(TAG_NET, "==============================")
    }

    /**
     * 显示推荐问题按钮
     */
    private fun showSuggestedQuestions(questions: List<String>) {
        Log.i(TAG_NET, "========== 显示推荐问题 ==========")
        Log.i(TAG_NET, "推荐问题数量: ${questions.size}")
        questions.forEachIndexed { index, question ->
            Log.i(TAG_NET, "问题${index + 1}: $question")
        }

        runOnUiThread {
            try {
                binding.llSuggestedQuestions.removeAllViews()
                Log.i(TAG_NET, "✓ 清空旧的推荐问题按钮")

                questions.forEach { question ->
                    val button = Button(this).apply {
                        text = question
                        textSize = 13f
                        setPadding(24, 8, 24, 8) // 减少高度，增加左右padding

                        // 设置按钮样式 - 使用与开场白相同的背景
                        background = ContextCompat.getDrawable(this@CallActivity, R.drawable.bg_digital_suggested_question)
                        setTextColor(ContextCompat.getColor(this@CallActivity, R.color.digital_text_primary))

                        // 设置字体样式
                        typeface = android.graphics.Typeface.DEFAULT_BOLD
                        letterSpacing = 0.02f

                        // 设置布局参数 - 减少高度，增加宽度
                        val layoutParams = LinearLayout.LayoutParams(
                            LinearLayout.LayoutParams.MATCH_PARENT,
                            (48 * resources.displayMetrics.density).toInt() // 48dp转换为像素
                        ).apply {
                            setMargins(0, 4, 0, 4) // 减少间距
                        }
                        this.layoutParams = layoutParams

                        // 设置阴影效果
                        elevation = 6f

                        // 设置点击事件：调用LLM接口
                        setOnClickListener {
                            Log.i(TAG_NET, "========== 用户点击建议问题 ==========")
                            Log.i(TAG_NET, "点击的建议问题: $question")
                            Log.i(TAG_NET, "点击前历史问题: ${historyQuestions.joinToString(", ")}")

                            // 隐藏欢迎内容，启用对话布局
                            hideWelcomeContent()

                            // 设置当前会话问题，避免SSE返回时重复添加到历史记录
                            currentSessionQuestion = question
                            Log.i(TAG_NET, "✓ 设置当前会话问题: $currentSessionQuestion")

                            // 显示用户问题（建议问题不重复添加到历史记录）
                            displayUserQuestion(question, addToHistory = false)

                            // 检查问题是否在历史记录中，决定请求类型
                            Log.i(TAG_NET, "🔍 检查建议问题是否在历史记录中:")
                            Log.i(TAG_NET, "  当前问题: '$question'")
                            Log.i(TAG_NET, "  历史问题列表: ${historyQuestions.joinToString(", ") { "'$it'" }}")

                            // 清理问题文本进行比较
                            val cleanQuestion = question.trim().replace(Regex("\\s+"), " ")
                            val isInHistory = historyQuestions.any { historyQuestion ->
                                val cleanHistoryQuestion = historyQuestion.trim().replace(Regex("\\s+"), " ")
                                cleanQuestion == cleanHistoryQuestion
                            }

                            val requestType = if (isInHistory) {
                                Log.i(TAG_NET, "✓ 该建议问题在历史记录中，使用history_click类型（跳过greeting）")
                                "history_click"
                            } else {
                                Log.i(TAG_NET, "✓ 该建议问题不在历史记录中，使用suggestion_click类型（播放greeting）")
                                "suggestion_click"
                            }

                            // 调用LLM接口处理问题
                            sendAsrResultToLlm(question, resetSession = false, requestType = requestType)
                        }
                    }

                    binding.llSuggestedQuestions.addView(button)
                    Log.i(TAG_NET, "✓ 添加按钮: $question")
                }

                binding.suggestedQuestionsContainer.visibility = android.view.View.VISIBLE

                // 添加淡入动画效果
                val fadeInAnimation = android.view.animation.AnimationUtils.loadAnimation(this, R.anim.fade_in_animation)
                binding.suggestedQuestionsContainer.startAnimation(fadeInAnimation)

                Log.i(TAG_NET, "✓ 推荐问题容器设为可见")
                Log.i(TAG_NET, "✓ 推荐问题UI更新成功，共${questions.size}个按钮")

            } catch (e: Exception) {
                Log.e(TAG_NET, "✗ 推荐问题UI更新失败: ${e.message}", e)
            }
        }
        Log.i(TAG_NET, "================================")
    }

    /**
     * 隐藏开场白和推荐问题，启用对话布局
     */
    private fun hideWelcomeContent() {
        runOnUiThread {
            Log.i(TAG_NET, "========== 隐藏欢迎内容，启用对话布局 ==========")

            // 取消返回开场白定时器（用户开始新的交互）
            cancelReturnToGreetingTimer()

            // 隐藏开场白、推荐问题、历史问题和唤醒词提示
            binding.cvOpeningStatement.visibility = android.view.View.GONE
            binding.suggestedQuestionsContainer.visibility = android.view.View.GONE
            binding.cvHistoryQuestions.visibility = android.view.View.GONE
            hideWakeWordHint()

            // 确保背景图片正确显示，防止UI状态变化时出现视觉异常
            try {
                binding.ivBg.invalidate()
                Log.i(TAG_NET, "✓ 背景图片已刷新")
            } catch (e: Exception) {
                Log.e(TAG_NET, "背景图片刷新失败: ${e.message}")
            }

            Log.i(TAG_NET, "✓ 已隐藏开场白、推荐问题和历史问题")
            Log.i(TAG_NET, "✓ 屏幕空间现在可用于LLM对话界面")
            Log.i(TAG_NET, "✓ 左下角：LLM建议问题列表（最多3个）")
            Log.i(TAG_NET, "✓ 其余空间：AI回答显示区域")
            Log.i(TAG_NET, "===============================================")
        }
    }

    /**
     * 处理SSE格式的响应数据
     * 解析每一行的data:开头的JSON数据
     */
    private fun processSseResponse(responseStr: String) {
        try {
            Log.i(TAG_NET, "========== 开始处理SSE响应 ==========")
            Log.i(TAG_NET, "原始响应长度: ${responseStr.length}")

            // 按行分割SSE响应
            val lines = responseStr.split("\n")
            Log.i(TAG_NET, "分割后行数: ${lines.size}")

            var hasAudioUrl = false
            var dataLineCount = 0

            for ((index, line) in lines.withIndex()) {
                Log.i(TAG_NET, "处理第${index + 1}行: '$line'")

                if (line.startsWith("data: ")) {
                    dataLineCount++
                    val jsonStr = line.substring(6) // 移除"data: "前缀
                    Log.i(TAG_NET, "第${dataLineCount}个data行，JSON内容: $jsonStr")

                    try {
                        val sseMessage = objectMapper.readValue(jsonStr, SseMessage::class.java)
                        Log.i(TAG_NET, "成功解析SSE消息: event=${sseMessage.event}, url=${sseMessage.url}, data=${sseMessage.data}")
                        handleSseMessage(sseMessage)

                        // 如果有音频URL，标记为有音频
                        if (!sseMessage.url.isNullOrEmpty()) {
                            hasAudioUrl = true
                            Log.i(TAG_NET, "找到音频URL: ${sseMessage.url}")
                        }

                    } catch (e: Exception) {
                        Log.e(TAG_NET, "解析SSE消息失败: $jsonStr")
                        Log.e(TAG_NET, "解析错误详情: ${e.message}", e)
                    }
                } else if (line.isNotBlank()) {
                    Log.i(TAG_NET, "非data行: '$line'")
                }
            }

            Log.i(TAG_NET, "SSE处理完成，共处理${dataLineCount}个data行，hasAudioUrl=$hasAudioUrl")

            // 如果没有找到音频URL，重置状态
            if (!hasAudioUrl) {
                Log.w(TAG_NET, "SSE响应中未找到音频URL")
                runOnUiThread {
                    isProcessingRequest = false
                    binding.btnRecord.isEnabled = true
                    updateRecordButton("等待唤醒")
                }
            }

        } catch (e: Exception) {
            Log.e(TAG_NET, "处理SSE响应失败: ${e.message}", e)
            runOnUiThread {
                Toast.makeText(this@CallActivity, "处理响应失败: ${e.message}", Toast.LENGTH_SHORT).show()
                isProcessingRequest = false
                binding.btnRecord.isEnabled = true
                updateRecordButton("等待唤醒")
            }
        }
    }

    /**
     * 处理单个SSE消息
     */
    private fun handleSseMessage(message: SseMessage) {
        Log.i(TAG_NET, "========== 处理SSE消息 ==========")
        Log.i(TAG_NET, "消息类型: event=${message.event}")
        Log.i(TAG_NET, "消息内容: answer=${message.answer}")
        Log.i(TAG_NET, "音频URL: url=${message.url}")
        Log.i(TAG_NET, "数据字段: data=${message.data}")
        Log.i(TAG_NET, "开场白: openingStatement=${message.openingStatement}")
        Log.i(TAG_NET, "推荐问题: suggestedQuestions=${message.suggestedQuestions}")
        Log.i(TAG_NET, "问候标志: greeting=${message.greeting}")
        Log.i(TAG_NET, "状态: status=${message.status}")
        Log.i(TAG_NET, "================================")

        when {
            // 处理parameters事件 - 包含开场白和推荐问题
            message.event == "parameters" -> {
                Log.i(TAG_NET, "✓ 识别为parameters事件")
                message.openingStatement?.let { openingStatement ->
                    Log.i(TAG_NET, "✓ 显示开场白: $openingStatement")
                    showOpeningStatement(openingStatement)
                }
                message.suggestedQuestions?.let { questions ->
                    Log.i(TAG_NET, "✓ 显示推荐问题: $questions")
                    showSuggestedQuestions(questions)
                }
            }

            // 处理data字段 - 推荐问题数组
            !message.data.isNullOrEmpty() -> {
                Log.i(TAG_NET, "✓ 识别为推荐问题数据: ${message.data}")
                showSuggestedQuestions(message.data)
            }

            // 处理message事件 - 包含音频URL但没有answer的情况（可能是早期消息）
            message.event == "message" && !message.url.isNullOrEmpty() && message.answer.isNullOrEmpty() -> {
                Log.i(TAG_NET, "✓ 识别为纯音频消息（无文本）: ${message.url}")
                Log.w(TAG_NET, "⚠ 该消息没有answer字段，无法建立文本映射，将直接播放音频")
                addToAudioQueue(message.url)

                // 如果是第一次收到音频，隐藏欢迎内容
                if (message.greeting == true) {
                    Log.i(TAG_NET, "✓ 这是问候消息，隐藏欢迎内容")
                    hideWelcomeContent()
                }
            }

            // 处理其他message事件
            message.event == "message" -> {
                Log.i(TAG_NET, "✓ 识别为普通消息事件: answer=${message.answer}, status=${message.status}")
            }

            else -> {
                Log.w(TAG_NET, "⚠ 未识别的消息类型或内容为空")
            }
        }
    }

    /**
     * 启动本地唤醒词监听
     */
    private fun startLocalKws() {
        try {
            // 🔧 增强状态检查：防止重复启动，同时检查实际录制状态
            if (isKwsRecording && kwsAudioRecord?.recordingState == android.media.AudioRecord.RECORDSTATE_RECORDING) {
                Log.w(TAG_NET, "⚠ 唤醒词监听已在运行且录制状态正常，跳过重复启动")
                Log.i(TAG_NET, "当前状态 - isKwsRecording: $isKwsRecording, recordingState: ${kwsAudioRecord?.recordingState}")
                return
            }

            // 如果状态标志为true但实际录制状态异常，强制重新初始化
            if (isKwsRecording && kwsAudioRecord?.recordingState != android.media.AudioRecord.RECORDSTATE_RECORDING) {
                Log.w(TAG_NET, "⚠ 检测到状态不同步，强制重新初始化唤醒词监听")
                Log.w(TAG_NET, "状态标志: $isKwsRecording, 实际录制状态: ${kwsAudioRecord?.recordingState}")

                // 清理异常状态
                isKwsRecording = false
                try {
                    kwsAudioRecord?.stop()
                    kwsAudioRecord?.release()
                    kwsAudioRecord = null
                    kwsThread?.interrupt()
                    kwsThread = null
                } catch (e: Exception) {
                    Log.e(TAG_NET, "清理异常唤醒词状态失败: ${e.message}")
                }
            }

            Log.i(TAG_NET, "========== 启动唤醒词监听 ==========")

            // 获取当前设置的唤醒词
            val currentWakeWord = sharedPrefs.getString(SettingsActivity.KEY_WAKE_WORD, SettingsActivity.DEFAULT_WAKE_WORD) ?: SettingsActivity.DEFAULT_WAKE_WORD
            Log.i(TAG_NET, "当前设置的唤醒词: $currentWakeWord")

            // 使用动态keywords配置

            // 加载JNI库（如果尚未加载）
            try {
                System.loadLibrary("sherpa-onnx-jni")
            } catch (_: Throwable) {
                // 已加载
            }

            val sampleRateInHz = 16000
            val featureDim = 80

            val featConf = com.k2fsa.sherpa.onnx.getFeatureConfig(sampleRateInHz, featureDim)
            val modelConf = com.k2fsa.sherpa.onnx.getKwsModelConfig(0) ?: return

            // 检查是否有更新的唤醒词文件
            val privateModelDir = java.io.File(filesDir, "sherpa-onnx-kws-zipformer-wenetspeech-3.3M-2024-01-01")
            val updatedKeywordsFile = java.io.File(privateModelDir, "keywords.txt")

            // 验证keywords.txt文件内容
            verifyKeywordsFile()

            // 为避免闪退，直接使用默认配置（assets文件已包含"小灵小灵"）
            Log.i(TAG_NET, "使用默认assets配置，已包含'小灵小灵'唤醒词")

            val kwsConfig = com.k2fsa.sherpa.onnx.KeywordSpotterConfig(
                featConfig = featConf,
                modelConfig = modelConf,
                keywordsFile = com.k2fsa.sherpa.onnx.getKeywordsFile(0)  // 使用默认assets文件
            )

            // 根据配置初始化KeywordSpotter
            keywordSpotter = try {
                com.k2fsa.sherpa.onnx.KeywordSpotter(assets, kwsConfig)
            } catch (e: Exception) {
                Log.e(TAG_NET, "KeywordSpotter初始化失败，回退到默认配置: ${e.message}", e)
                // 如果初始化失败，回退到完全默认的配置
                val defaultConfig = com.k2fsa.sherpa.onnx.KeywordSpotterConfig(
                    featConfig = featConf,
                    modelConfig = modelConf,
                    keywordsFile = com.k2fsa.sherpa.onnx.getKeywordsFile(0)
                )
                com.k2fsa.sherpa.onnx.KeywordSpotter(assets, defaultConfig)
            }


            kwsStream = keywordSpotter!!.createStream("")

            // 初始化麦克风
            val bufferSize = android.media.AudioRecord.getMinBufferSize(
                sampleRateInHz,
                android.media.AudioFormat.CHANNEL_IN_MONO,
                android.media.AudioFormat.ENCODING_PCM_16BIT
            ) * 2

            kwsAudioRecord = android.media.AudioRecord(
                android.media.MediaRecorder.AudioSource.MIC,
                sampleRateInHz,
                android.media.AudioFormat.CHANNEL_IN_MONO,
                android.media.AudioFormat.ENCODING_PCM_16BIT,
                bufferSize
            )

            // 🔧 检查AudioRecord初始化状态
            if (kwsAudioRecord?.state != android.media.AudioRecord.STATE_INITIALIZED) {
                Log.e(TAG_NET, "AudioRecord初始化失败，状态: ${kwsAudioRecord?.state}")
                throw Exception("AudioRecord初始化失败")
            }

            kwsAudioRecord?.startRecording()

            // 🔧 验证录制状态
            val recordingState = kwsAudioRecord?.recordingState
            if (recordingState != android.media.AudioRecord.RECORDSTATE_RECORDING) {
                Log.e(TAG_NET, "AudioRecord启动录制失败，状态: $recordingState")
                throw Exception("AudioRecord启动录制失败")
            }

            isKwsRecording = true
            Log.i(TAG_NET, "✓ AudioRecord初始化并启动成功，状态: ${kwsAudioRecord?.state}, 录制状态: $recordingState")

            // 开启线程处理音频
            kwsThread = kotlin.concurrent.thread(start = true) {
                val shortBuffer = ShortArray(bufferSize / 2)
                var consecutiveZeroReads = 0
                var lastVolumeCheck = System.currentTimeMillis()

                while (isKwsRecording) {
                    val read = kwsAudioRecord?.read(shortBuffer, 0, shortBuffer.size) ?: 0

                    // 🔧 增强音频录制监控
                    if (read <= 0) {
                        consecutiveZeroReads++
                        if (consecutiveZeroReads > 10) {
                            Log.w(TAG_NET, "⚠ 唤醒词录制连续失败，可能存在音频设备冲突")
                            Log.w(TAG_NET, "连续零读取次数: $consecutiveZeroReads")
                            Log.w(TAG_NET, "AudioRecord状态: ${kwsAudioRecord?.recordingState}")
                            consecutiveZeroReads = 0  // 重置计数器
                        }
                        Thread.sleep(10)  // 短暂等待
                        continue
                    }

                    consecutiveZeroReads = 0  // 重置计数器

                    // 🔧 增强音频音量检测，提高唤醒词检测敏感度
                    val currentTime = System.currentTimeMillis()
                    if (currentTime - lastVolumeCheck > 2000) {  // 每2秒检查一次，更频繁
                        val volume = shortBuffer.map { kotlin.math.abs(it.toFloat()) }.average()
                        val maxVolume = shortBuffer.map { kotlin.math.abs(it.toFloat()) }.maxOrNull() ?: 0f

                        // 🔧 在音频播放期间使用更详细的监控
                        if (isPlayingLlmAudio) {
                            Log.d(TAG_NET, "🎵 播放期间唤醒词监听检查 - 平均音量: $volume, 最大音量: $maxVolume")

                            // 检测到较强音频信号时特别记录
                            if (volume > 100.0 || maxVolume > 1000.0) {
                                Log.i(TAG_NET, "🎯 播放期间检测到强音频信号！平均: $volume, 最大: $maxVolume")
                            }
                        } else {
                            Log.d(TAG_NET, "唤醒词监听音量检查 - 平均: $volume, 最大: $maxVolume")
                        }

                        // 降低音量检测阈值，提高敏感度
                        if (volume > 0.001 || maxVolume > 0.01) {  // 更敏感的阈值
                            Log.d(TAG_NET, "✓ 检测到有效音频信号，平均音量: $volume, 最大音量: $maxVolume")
                        }
                        lastVolumeCheck = currentTime
                    }

                    if (read > 0) {
                        val floatSamples = FloatArray(read) { shortBuffer[it] / 32768.0f }
                        kwsStream?.acceptWaveform(floatSamples, sampleRate = sampleRateInHz)

                        while (keywordSpotter?.isReady(kwsStream!!) == true) {
                            keywordSpotter?.decode(kwsStream!!)
                            val result = keywordSpotter?.getResult(kwsStream!!)
                            val keyword = result?.keyword ?: ""
                            if (keyword.isNotBlank()) {
                                // 🔧 增强唤醒词检测成功日志
                                Log.i(TAG_NET, "========== 🎯 唤醒词检测成功 🎯 ==========")
                                Log.i(TAG_NET, "检测到唤醒词: $keyword")
                                Log.i(TAG_NET, "当前播放状态 - isPlayingLlmAudio: $isPlayingLlmAudio")
                                Log.i(TAG_NET, "当前处理状态 - isProcessingRequest: $isProcessingRequest")
                                Log.i(TAG_NET, "当前LLM请求状态 - isProcessingLlmRequest: $isProcessingLlmRequest")
                                Log.i(TAG_NET, "当前时间: ${System.currentTimeMillis()}")
                                Log.i(TAG_NET, "音频队列大小: ${audioPlayQueue.size}")
                                Log.i(TAG_NET, "=====================================")

                                // 🔧 立即显示唤醒成功提示并重置计时器
                                runOnUiThread {
                                    android.widget.Toast.makeText(this, "🎯 唤醒成功: $keyword", android.widget.Toast.LENGTH_SHORT).show()

                                    // 🔧 重置返回开场白计时器，给用户充足时间完成交互
                                    Log.i(TAG_NET, "唤醒词检测成功，重置返回开场白计时器")
                                    cancelReturnToGreetingTimer()  // 先取消当前计时器
                                    startReturnToGreetingTimer()   // 重新开始计时
                                }

                                // 重置流准备下一次唤醒
                                keywordSpotter?.reset(kwsStream!!)

                                // 🔧 强制打断所有正在进行的操作
                                if (isPlayingLlmAudio || isProcessingRequest || isProcessingLlmRequest) {
                                    Log.i(TAG_NET, "🎯 唤醒词检测到活动中，强制执行打断操作")

                                    // 立即停止所有音频播放
                                    duix?.stopAudio()

                                    // 取消所有正在进行的请求
                                    currentSseCall?.cancel()

                                    // 执行打断操作
                                    interruptAudioPlayback()
                                    runOnUiThread {
                                        Toast.makeText(this, "🎯 已打断AI音频，正在切换到语音识别...", Toast.LENGTH_SHORT).show()
                                    }

                                    // 🔧 减少打断后的等待时间，提高响应速度
                                    val audioStopDelay = 100L // 减少到100ms等待AI音频停止
                                    android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                                        Log.i(TAG_NET, "AI音频已停止，播放唤醒回复")

                                        // 播放唤醒词回复音频（"我在，请说"）
                                        playWakeupReplyAudio()

                                        // 🔧 优化等待时间，使用更短的延迟提高响应速度
                                        val wakeupDelay = sharedPrefs.getInt(SettingsActivity.KEY_WAKEUP_DELAY, 1000).toLong() // 默认改为1000ms
                                        Log.i(TAG_NET, "使用优化的回复等待时间: ${wakeupDelay}ms")

                                        // 等待唤醒回复播放完成 + 用户设置的等待时间
                                        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                                            Log.i(TAG_NET, "🎯 唤醒回复播放完成且等待时间结束，开始ASR识别")
                                            runOnUiThread {
                                                Toast.makeText(this, "🎯 开始语音识别", Toast.LENGTH_SHORT).show()
                                            }
                                            startLocalAsrRecognition()
                                        }, wakeupDelay)
                                    }, audioStopDelay)
                                } else {
                                    Log.i(TAG_NET, "没有AI音频播放，直接播放唤醒回复")

                                    // 播放唤醒词回复音频
                                    playWakeupReplyAudio()

                                    // 没有AI音频播放，等待唤醒回复播放完成后开始ASR
                                    val wakeupDelay = sharedPrefs.getInt(SettingsActivity.KEY_WAKEUP_DELAY, 1500).toLong()
                                    Log.i(TAG_NET, "正常唤醒，使用设置的回复等待时间: ${wakeupDelay}ms")
                                    android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                                        Log.i(TAG_NET, "唤醒词回复播放完成且等待时间结束，开始ASR识别")
                                        runOnUiThread {
                                            Toast.makeText(this, "开始语音识别", Toast.LENGTH_SHORT).show()
                                        }
                                        startLocalAsrRecognition()
                                    }, wakeupDelay)
                                }
                            }
                        }
                    }
                }
            }

            // 🔧 增强日志：确认唤醒词监听启动成功
            Log.i(TAG_NET, "✓ 唤醒词监听启动成功")
            Log.i(TAG_NET, "✓ isKwsRecording: $isKwsRecording")
            Log.i(TAG_NET, "✓ kwsAudioRecord状态: ${kwsAudioRecord?.recordingState}")
            Log.i(TAG_NET, "========== 唤醒词监听启动完成 ==========")

        } catch (e: Exception) {
            Log.e(TAG_NET, "启动本地KWS失败: ${e.message}", e)
            isKwsRecording = false  // 确保状态正确
            runOnUiThread {
                android.widget.Toast.makeText(this, "唤醒词初始化失败，请使用手动录音", android.widget.Toast.LENGTH_LONG).show()
                updateRecordButton("点击开始录音")
            }
        }
    }

    /**
     * 停止ASR录音
     */
    private fun stopAsrRecording() {
        Log.i(TAG_NET, "========== 停止ASR录音 ==========")

        isAsrRecording = false
        isWaitingForSpeech = false

        try {
            asrAudioRecord?.stop()
            asrAudioRecord?.release()
            asrAudioRecord = null
            Log.i(TAG_NET, "✓ ASR录音已停止")
        } catch (e: Exception) {
            Log.e(TAG_NET, "停止ASR录音失败: ${e.message}", e)
        }

        runOnUiThread {
            updateRecordButton("点击开始录音")
            setAudioWaveRecording(false)
            Toast.makeText(this, "录音已停止", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 启动真实的本地ASR识别
     */
    private fun startLocalAsrRecognition() {
        Log.i(TAG_NET, "========== 启动本地ASR识别 ==========")

        // 首先进行完整的ASR状态检查
        if (!isAsrSystemReady()) {
            Log.w(TAG_NET, "ASR系统未就绪，尝试重新初始化")
            runOnUiThread {
                Toast.makeText(this, "ASR系统初始化中，请稍候...", Toast.LENGTH_SHORT).show()
                updateRecordButton("初始化中...")
            }

            // 异步重新初始化ASR系统
            Thread {
                if (reinitializeAsrSystem()) {
                    // 重新初始化成功，重新尝试启动ASR
                    runOnUiThread {
                        Toast.makeText(this, "ASR系统就绪，开始识别", Toast.LENGTH_SHORT).show()
                    }
                    startLocalAsrRecognition()
                } else {
                    runOnUiThread {
                        Toast.makeText(this, "ASR系统初始化失败，请检查设备权限和网络", Toast.LENGTH_LONG).show()
                        updateRecordButton("ASR不可用")

                        // ASR系统初始化失败后跳转到数字人首页（开场白页面）
                        Log.i(TAG_NET, "ASR系统初始化失败，2秒后跳转到数字人首页")
                        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                            Log.i(TAG_NET, "执行ASR初始化失败后的首页跳转")
                            resetToWelcomeState()
                        }, 2000) // 延迟2秒，让用户看到错误提示
                    }
                }
            }.start()
            return
        }

        runOnUiThread {
            Toast.makeText(this, "开始本地ASR识别...", Toast.LENGTH_SHORT).show()
            // 清空之前的回答显示
            clearAiAnswerDisplay()
            // 更新按钮状态为录音中（绿色）
            updateRecordButton("正在录音")
            // 设置音频波浪为录音状态
            setAudioWaveRecording(true)
        }

        try {
            val sampleRateInHz = 16000
            val bufferSize = android.media.AudioRecord.getMinBufferSize(
                sampleRateInHz,
                android.media.AudioFormat.CHANNEL_IN_MONO,
                android.media.AudioFormat.ENCODING_PCM_16BIT
            ) * 2

            asrAudioRecord = android.media.AudioRecord(
                android.media.MediaRecorder.AudioSource.MIC,
                sampleRateInHz,
                android.media.AudioFormat.CHANNEL_IN_MONO,
                android.media.AudioFormat.ENCODING_PCM_16BIT,
                bufferSize
            )

            asrAudioRecord?.startRecording()
            isAsrRecording = true
            isWaitingForSpeech = true

            // 安全重置VAD
            try {
                asrVad?.reset()
                Log.i(TAG_NET, "✓ VAD重置成功")
            } catch (e: Exception) {
                Log.e(TAG_NET, "VAD重置失败，尝试重新创建: ${e.message}")
                // 重新创建VAD
                val vadConfig = com.k2fsa.sherpa.onnx.getVadModelConfig(0)
                if (vadConfig != null) {
                    asrVad = com.k2fsa.sherpa.onnx.Vad(assets, vadConfig)
                    asrVad?.reset()
                    Log.i(TAG_NET, "✓ VAD重新创建并重置成功")
                } else {
                    throw Exception("VAD配置获取失败")
                }
            }
            
            asrThread = kotlin.concurrent.thread(start = true) {
                val shortBuffer = ShortArray(512)
                val recordingTimeout = 10000 // 增加到10秒录音超时
                val startTime = System.currentTimeMillis()
                var validAudioDetected = false
                
                while (isAsrRecording && (System.currentTimeMillis() - startTime) < recordingTimeout) {
                    val read = asrAudioRecord?.read(shortBuffer, 0, shortBuffer.size) ?: 0
                    if (read > 0) {
                        val floatSamples = FloatArray(read) { shortBuffer[it] / 32768.0f }

                        // 计算音量并更新波浪可视化
                        val volume = calculateVolume(shortBuffer, read)
                        runOnUiThread {
                            updateAudioWaveVolume(volume)
                        }

                        // 检测是否有有效音频信号
                        if (!validAudioDetected && volume > 0.1f) {
                            validAudioDetected = true
                            Log.i(TAG_NET, "✓ 检测到有效音频信号，音量: $volume")
                        }

                        // 使用VAD检测语音段
                        try {
                            asrVad?.acceptWaveform(floatSamples)
                        } catch (e: Exception) {
                            Log.e(TAG_NET, "VAD处理音频数据失败: ${e.message}")
                            // VAD出错时尝试重新初始化
                            break
                        }
                        
                        while (!asrVad!!.empty()) {
                            val segment = asrVad?.front()
                            if (segment != null && segment.samples.isNotEmpty()) {
                                // 进行ASR识别
                                val stream = asrOfflineRecognizer?.createStream()
                                stream?.acceptWaveform(segment.samples, sampleRateInHz)
                                asrOfflineRecognizer?.decode(stream!!)
                                val result = asrOfflineRecognizer?.getResult(stream!!)
                                stream?.release()
                                
                                val recognizedText = result?.text?.trim() ?: ""
                                if (recognizedText.isNotBlank()) {
                                    // 停止录音
                                    isAsrRecording = false
                                    
                                                                    runOnUiThread {
                                    Toast.makeText(this@CallActivity, "ASR识别结果: $recognizedText", Toast.LENGTH_LONG).show()
                                    // 发送识别结果到LLM接口
                                    sendAsrResultToLlm(recognizedText)
                                }
                                
                                // 清理资源
                                cleanupAsrRecording()
                                return@thread
                                }
                            }
                            asrVad?.pop()
                        }
                    }
                }
                
                // 超时或无有效识别结果
                val message = if (!validAudioDetected) {
                    "未检测到有效音频信号，请检查麦克风权限和环境噪音"
                } else {
                    "录音超时，未识别到清晰语音，请重试"
                }

                runOnUiThread {
                    Toast.makeText(this@CallActivity, message, Toast.LENGTH_LONG).show()
                    Log.w(TAG_NET, "ASR录音结束 - $message")

                    // 录音失败后跳转到数字人首页（开场白页面）
                    Log.i(TAG_NET, "录音失败，2秒后跳转到数字人首页")
                    android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                        Log.i(TAG_NET, "执行录音失败后的首页跳转")
                        resetToWelcomeState()
                    }, 2000) // 延迟2秒，让用户看到错误提示
                }

                cleanupAsrRecording()
            }
            
        } catch (e: Exception) {
            Log.e(TAG_NET, "ASR识别失败: ${e.message}", e)
            runOnUiThread {
                Toast.makeText(this, "ASR识别失败: ${e.message}", Toast.LENGTH_SHORT).show()

                // 录音失败后跳转到数字人首页（开场白页面）
                Log.i(TAG_NET, "ASR识别异常失败，2秒后跳转到数字人首页")
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    Log.i(TAG_NET, "执行ASR异常失败后的首页跳转")
                    resetToWelcomeState()
                }, 2000) // 延迟2秒，让用户看到错误提示
            }
            cleanupAsrRecording()
        }
    }
    
    /**
     * 清理ASR录音资源
     */
    private fun cleanupAsrRecording() {
        try {
            isAsrRecording = false
            isWaitingForSpeech = false
            asrAudioRecord?.stop()
            asrAudioRecord?.release()
            asrAudioRecord = null
            asrThread?.interrupt()
            asrThread = null

            // 停用音频波浪录音状态
            runOnUiThread {
                setAudioWaveRecording(false)
                setAudioWaveActive(false)
            }

            // 🔧 关键修复：ASR识别完成后重新启动唤醒词监听
            Log.i(TAG_NET, "ASR识别完成，重新启动唤醒词监听")
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                Log.i(TAG_NET, "========== ASR完成后唤醒词监听状态检查 ==========")
                Log.i(TAG_NET, "localAsrEnabled: $localAsrEnabled")
                Log.i(TAG_NET, "isKwsRecording: $isKwsRecording")
                Log.i(TAG_NET, "kwsAudioRecord状态: ${kwsAudioRecord?.recordingState}")
                Log.i(TAG_NET, "kwsThread状态: ${kwsThread?.isAlive}")

                if (localAsrEnabled && !isKwsRecording) {
                    Log.i(TAG_NET, "✓ 条件满足，重新启动唤醒词监听以支持下次唤醒")
                    startLocalKws()
                } else if (!localAsrEnabled) {
                    Log.i(TAG_NET, "⚠ 本地ASR未启用，跳过唤醒词监听重启")
                } else {
                    Log.w(TAG_NET, "⚠ 唤醒词监听状态异常，强制重新初始化")
                    Log.w(TAG_NET, "当前状态 - isKwsRecording: $isKwsRecording, recordingState: ${kwsAudioRecord?.recordingState}")

                    // 强制重新初始化
                    isKwsRecording = false
                    startLocalKws()
                }
                Log.i(TAG_NET, "========== 唤醒词监听状态检查完成 ==========")
            }, 500) // 延迟500ms确保资源清理完成

        } catch (e: Exception) {
            Log.e(TAG_NET, "清理ASR录音资源失败: ${e.message}", e)
        }
    }

    /**
     * 发送ASR识别结果到LLM接口
     */
    private fun sendAsrResultToLlm(recognizedText: String, resetSession: Boolean = true, requestType: String = "voice_input") {
        Log.i(TAG_NET, "========== 发送ASR结果到LLM ==========")
        Log.i(TAG_NET, "识别文本: $recognizedText")
        Log.i(TAG_NET, "重置会话: $resetSession")

        // 记录当前请求的类型，用于greeting音频播放控制
        currentRequestType = requestType
        Log.i(TAG_NET, "设置请求类型 - currentRequestType: $currentRequestType")

        // 立即隐藏欢迎内容，避免UI重叠
        runOnUiThread {
            Log.i(TAG_NET, "✓ 立即隐藏欢迎内容，避免与AI回复界面重叠")
            hideWelcomeContent()
        }

        // 清空之前的AI回答显示区域，确保新回复不会在旧回复下面继续生成
        runOnUiThread {
            binding.tvAiAnswer.text = ""
            binding.llAiAnswerSection.visibility = android.view.View.GONE
            // 清空累积的回答文本
            currentLlmAnswer.clear()
            completeLlmAnswer.clear()
            // 清空音频播放队列
            clearAudioQueue()
            // 清空媒体项
            clearMediaItems()
            Log.i(TAG_NET, "✓ 清空AI回答显示区域，准备接收新回复")
        }

        // 显示用户问题并添加到历史记录（只有在重置会话时才添加到历史记录）
        runOnUiThread {
            displayUserQuestion(recognizedText, addToHistory = resetSession)
        }

        // 根据参数决定是否重置当前会话问题
        if (resetSession) {
            currentSessionQuestion = recognizedText
            Log.i(TAG_NET, "✓ 设置新的会话问题: $currentSessionQuestion")
        } else {
            Log.i(TAG_NET, "✓ 保持当前会话问题: $currentSessionQuestion")
        }

        // 🔧 增强防重复请求逻辑
        val currentTime = System.currentTimeMillis()
        if (isProcessingLlmRequest) {
            Log.w(TAG_NET, "正在处理LLM请求，忽略重复请求")
            Log.w(TAG_NET, "当前处理中的文本: $currentProcessingText")
            Log.w(TAG_NET, "新请求文本: $recognizedText")
            runOnUiThread {
                Toast.makeText(this@CallActivity, "正在处理中，请稍候...", Toast.LENGTH_SHORT).show()
            }
            return
        }

        // 检查是否是相同文本的重复请求（1秒内）
        if (currentProcessingText == recognizedText && (currentTime - lastRequestTimestamp) < 1000) {
            Log.w(TAG_NET, "检测到1秒内的重复请求，忽略")
            Log.w(TAG_NET, "重复请求文本: $recognizedText")
            Log.w(TAG_NET, "上次请求时间: $lastRequestTimestamp, 当前时间: $currentTime")
            return
        }
        
        try {
            // URL编码识别的文本
            val encodedText = java.net.URLEncoder.encode(recognizedText, "UTF-8")
            Log.i(TAG_NET, "URL编码后的文本: $encodedText")
            
            // 构建完整的LLM URL
            val llmUrl = ApiConfig.buildUrl(ttsBaseUrl, ApiConfig.LLM.LLM_STREAMING_ENDPOINT)
            val fullLlmUrl = "$llmUrl?text=$encodedText"
            Log.i(TAG_NET, "LLM请求URL: $fullLlmUrl")
            
            // 获取请求参数
            val currentApiKey = sharedPrefs.getString(SettingsActivity.KEY_APIKEY, "") ?: ""
            val userId = sharedPrefs.getString(SettingsActivity.KEY_USER_ID, SettingsActivity.DEFAULT_USER_ID) ?: SettingsActivity.DEFAULT_USER_ID

            // 根据模型类别确定reference_id
            val modelCategory = sharedPrefs.getString(SettingsActivity.KEY_SELECTED_MODEL_CATEGORY, "male") ?: "male"
            Log.i(TAG_NET, "LLM请求 - 从SharedPreferences读取的模型类别: $modelCategory")

            val referenceId = when (modelCategory) {
                "female" -> "woman"
                "male" -> "man"
                else -> {
                    Log.w(TAG_NET, "未知的模型类别: $modelCategory, 使用默认值 man")
                    "man" // 默认为男声
                }
            }

            // 获取语速设置
            val ttsSpeed = sharedPrefs.getFloat(SettingsActivity.KEY_TTS_SPEED, SettingsActivity.DEFAULT_TTS_SPEED)

            // 获取语言设置
            val currentLanguage = getCurrentLanguage()

            Log.i(TAG_NET, "请求参数:")
            Log.i(TAG_NET, "  dify_api_key: $currentApiKey")
            Log.i(TAG_NET, "  user_id: $userId")
            Log.i(TAG_NET, "  reference_id: $referenceId")
            Log.i(TAG_NET, "  tts_speed: $ttsSpeed")
            Log.i(TAG_NET, "  translate: $currentLanguage")
            Log.i(TAG_NET, "  模型类别: $modelCategory")
            
            if (currentApiKey.isEmpty()) {
                Log.e(TAG_NET, "API Key为空，无法发送LLM请求")
                runOnUiThread {
                    Toast.makeText(this@CallActivity, "API Key为空，请在设置中选择应用", Toast.LENGTH_SHORT).show()
                }
                return
            }
            
            // 🔧 设置处理状态和追踪信息
            isProcessingLlmRequest = true
            currentProcessingText = recognizedText
            lastRequestTimestamp = System.currentTimeMillis()

            Log.i(TAG_NET, "========== 开始LLM请求追踪 ==========")
            Log.i(TAG_NET, "请求文本: $recognizedText")
            Log.i(TAG_NET, "请求类型: $requestType")
            Log.i(TAG_NET, "请求时间: $lastRequestTimestamp")
            Log.i(TAG_NET, "重置会话: $resetSession")
            Log.i(TAG_NET, "========================================")

            runOnUiThread {
                Toast.makeText(this@CallActivity, "正在处理问题: $recognizedText", Toast.LENGTH_SHORT).show()
            }
            
            // 构建请求
            val request = Request.Builder()
                .url(fullLlmUrl)
                .get()
                .addHeader("dify_api_key", currentApiKey)
                .addHeader("reference_id", referenceId)
                .addHeader("user_id", userId)
                .addHeader("tts_speed", ttsSpeed.toString())
                .addHeader("translate", currentLanguage)
                .addHeader("User-Agent", "Android App")
                .addHeader("Accept", "*/*")
                .addHeader("Connection", "keep-alive")
                .build()
            
            // 打印请求详情
            Log.i(TAG_NET, "========== LLM请求详情 ==========")
            Log.i(TAG_NET, "请求方法: ${request.method}")
            Log.i(TAG_NET, "请求URL: ${request.url}")
            request.headers.forEach { (name, value) ->
                Log.i(TAG_NET, "请求头: $name = $value")
            }
            Log.i(TAG_NET, "===============================")
            
            // 发送请求并保存Call对象用于可能的取消操作
            currentSseCall = okHttpClient.newCall(request)
            currentSseCall?.enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    Log.e(TAG_NET, "LLM请求失败: ${e.message}", e)
                    Log.e(TAG_NET, "失败的请求文本: $currentProcessingText")

                    // 🔧 重置处理状态和追踪信息
                    isProcessingLlmRequest = false
                    currentProcessingText = null
                    lastRequestTimestamp = 0
                    currentSseCall = null  // 清空SSE连接引用

                    runOnUiThread {
                        Toast.makeText(this@CallActivity, "LLM请求失败: ${e.message}", Toast.LENGTH_SHORT).show()
                    }
                }
                
                override fun onResponse(call: Call, response: Response) {
                    try {
                        Log.i(TAG_NET, "========== LLM响应详情 ==========")
                        Log.i(TAG_NET, "响应状态码: ${response.code}")
                        Log.i(TAG_NET, "响应消息: ${response.message}")
                        response.headers.forEach { (name, value) ->
                            Log.i(TAG_NET, "响应头: $name = $value")
                        }
                        
                        Log.i(TAG_NET, "==============================")

                        // 使用流式处理LLM的SSE响应
                        processStreamingLlmSseResponse(response)
                        
                    } catch (e: Exception) {
                        Log.e(TAG_NET, "处理LLM响应失败: ${e.message}", e)
                        Log.e(TAG_NET, "异常处理的请求文本: $currentProcessingText")

                        // 🔧 重置处理状态和追踪信息
                        isProcessingLlmRequest = false
                        currentProcessingText = null
                        lastRequestTimestamp = 0
                        currentSseCall = null  // 清空SSE连接引用

                        runOnUiThread {
                            Toast.makeText(this@CallActivity, "处理LLM响应失败: ${e.message}", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            })
            
        } catch (e: Exception) {
            Log.e(TAG_NET, "发送ASR结果到LLM失败: ${e.message}", e)
            Log.e(TAG_NET, "异常的请求文本: $currentProcessingText")

            // 🔧 重置处理状态和追踪信息
            isProcessingLlmRequest = false
            currentProcessingText = null
            lastRequestTimestamp = 0
            currentSseCall = null  // 清空SSE连接引用

            runOnUiThread {
                Toast.makeText(this@CallActivity, "发送LLM请求失败: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
        Log.i(TAG_NET, "===================================")
    }

    /**
     * 处理LLM的SSE格式响应数据
     * 根据不同的事件类型进行不同的处理
     */
    private fun processLlmSseResponse(responseStr: String) {
        try {
            Log.i(TAG_NET, "========== 开始处理LLM SSE响应 ==========")
            Log.i(TAG_NET, "原始响应长度: ${responseStr.length}")

            // 🔧 优化：使用更高效的字符串分割和处理
            val lines = responseStr.lineSequence().filter { it.isNotBlank() }
            var dataLineCount = 0

            // 🔧 优化：批量处理消息，减少UI更新频率
            val messagesToProcess = mutableListOf<SseMessage>()

            for (line in lines) {
                if (line.startsWith("data: ")) {
                    dataLineCount++
                    val jsonStr = line.substring(6) // 移除"data: "前缀

                    try {
                        val sseMessage = objectMapper.readValue(jsonStr, SseMessage::class.java)
                        messagesToProcess.add(sseMessage)
                    } catch (e: Exception) {
                        Log.e(TAG_NET, "解析LLM SSE消息失败: $jsonStr")
                        Log.e(TAG_NET, "解析错误详情: ${e.message}", e)
                    }
                }
            }

            // 🔧 优化：批量处理消息，减少线程切换
            if (messagesToProcess.isNotEmpty()) {
                runOnUiThread {
                    messagesToProcess.forEach { message ->
                        handleLlmSseMessage(message)
                    }
                }
            }

            Log.i(TAG_NET, "LLM SSE处理完成，共处理${dataLineCount}个data行")
            
            // 🔧 LLM响应处理完成，重置处理状态和追踪信息
            isProcessingLlmRequest = false
            currentProcessingText = null
            lastRequestTimestamp = 0

        } catch (e: Exception) {
            Log.e(TAG_NET, "处理LLM SSE响应失败: ${e.message}", e)
            isProcessingLlmRequest = false  // 重置处理状态
            runOnUiThread {
                Toast.makeText(this@CallActivity, "处理LLM响应失败: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * 处理单个LLM SSE消息
     * 根据greeting、status等字段决定如何显示和播放
     */
    private fun handleLlmSseMessage(message: SseMessage) {
        Log.i(TAG_NET, "========== 处理LLM SSE消息 ==========")
        Log.i(TAG_NET, "消息类型: event=${message.event}")
        Log.i(TAG_NET, "问候标志: greeting=${message.greeting}")
        Log.i(TAG_NET, "状态: status=${message.status}")
        Log.i(TAG_NET, "问题: question=${message.question}")
        Log.i(TAG_NET, "回答: answer=${message.answer}")
        Log.i(TAG_NET, "音频URL: url=${message.url}")
        Log.i(TAG_NET, "数据字段: data=${message.data}")
        Log.i(TAG_NET, "================================")

        // 确保在处理任何SSE消息时，欢迎内容都保持隐藏状态（修复UI错乱问题）
        runOnUiThread {
            if (binding.cvOpeningStatement.visibility == android.view.View.VISIBLE) {
                Log.w(TAG_NET, "⚠ 检测到开场白仍然可见，强制隐藏以避免UI错乱")
                hideWelcomeContent()
            }
        }

        when {
            // 处理greeting=true的消息：根据请求类型决定是否播放音频
            message.greeting == true && !message.url.isNullOrEmpty() -> {
                Log.i(TAG_NET, "✓ 识别为问候消息: ${message.url}")
                Log.i(TAG_NET, "当前请求类型 - currentRequestType: $currentRequestType")

                when (currentRequestType) {
                    "voice_input" -> {
                        // 语音输入，播放欢迎语音频
                        Log.i(TAG_NET, "✓ 语音输入，播放问候音频: ${message.url}")
                        addToAudioQueue(message.url)
                    }
                    "suggestion_click" -> {
                        // 点击建议问题，播放欢迎语音频
                        Log.i(TAG_NET, "✓ 点击建议问题，播放问候音频: ${message.url}")
                        addToAudioQueue(message.url)
                    }
                    "history_click" -> {
                        // 点击历史问题，跳过greeting音频播放
                        Log.i(TAG_NET, "⚠ 点击历史问题，跳过问候音频播放（提升用户体验）")
                        Log.i(TAG_NET, "用户点击历史问题时不需要重复播放欢迎语")
                    }
                    else -> {
                        // 默认播放
                        Log.i(TAG_NET, "✓ 未知请求类型，默认播放问候音频: ${message.url}")
                        addToAudioQueue(message.url)
                    }
                }



                Log.i(TAG_NET, "✓ greeting事件处理完成")
            }

            // 处理question显示：显示用户问题
            !message.question.isNullOrEmpty() -> {
                Log.i(TAG_NET, "✓ 显示用户问题: ${message.question}")

                // 🔧 增强验证：检查服务器返回的问题是否与客户端发送的一致
                val originalQuestion = currentSessionQuestion
                var questionToDisplay = message.question
                var isNewQuestion = currentSessionQuestion != message.question

                if (originalQuestion != null && originalQuestion != message.question && originalQuestion.isNotBlank()) {
                    Log.e(TAG_NET, "⚠ 检测到问答内容不一致！")
                    Log.e(TAG_NET, "客户端发送: $originalQuestion")
                    Log.e(TAG_NET, "服务器返回: ${message.question}")
                    Log.e(TAG_NET, "这可能是服务器端会话混乱导致的问题")

                    // 使用客户端的原始问题，而不是服务器返回的错误问题
                    questionToDisplay = originalQuestion
                    isNewQuestion = false  // 使用原始问题，不重复添加到历史记录
                    Log.i(TAG_NET, "✓ 使用客户端原始问题: $originalQuestion")
                } else {
                    // 检查是否是新问题，避免重复添加到历史记录
                    if (isNewQuestion) {
                        currentSessionQuestion = message.question
                        Log.i(TAG_NET, "✓ 这是新问题，将添加到历史记录")
                    } else {
                        Log.i(TAG_NET, "✓ 这是当前会话的问题，不重复添加到历史记录")
                    }
                }

                runOnUiThread {
                    displayUserQuestion(questionToDisplay, isNewQuestion)
                }
            }

            // 处理正常message事件(status=ok)：累积文本内容，准备一次性显示
            message.event == "message" && message.status == "ok" && !message.answer.isNullOrEmpty() -> {
                Log.i(TAG_NET, "✓ 识别为正常回答消息: ${message.answer}")

                // 累积完整的文本内容（用于最终一次性显示）
                completeLlmAnswer.append(message.answer)
                Log.i(TAG_NET, "✓ 累积文本内容，当前总长度: ${completeLlmAnswer.length}")

                // 立即显示累积的文本内容（不等待SSE流结束）
                runOnUiThread {
                    // 防止并发清空问题，先保存当前完整内容
                    val currentCompleteText = completeLlmAnswer.toString()
                    if (currentCompleteText.isNotEmpty()) {
                        currentLlmAnswer.clear()
                        currentLlmAnswer.append(currentCompleteText)
                        displayMarkdownAnswer(currentLlmAnswer.toString())
                        Log.i(TAG_NET, "✓ 立即显示累积文本，当前长度: ${currentLlmAnswer.length}")
                    } else {
                        Log.w(TAG_NET, "⚠ 完整文本内容为空，跳过显示")
                    }
                }

                // 如果有音频URL，添加到播放队列
                if (!message.url.isNullOrEmpty()) {
                    Log.i(TAG_NET, "✓ 有音频URL，添加到播放队列: ${message.url}")
                    addToAudioQueue(message.url)
                } else {
                    Log.i(TAG_NET, "✓ 无音频URL的文本片段")
                }
            }

            // 处理缓存响应中的文本内容（修复缓存问题文本不显示的问题）
            !message.answer.isNullOrEmpty() && message.url.isNullOrEmpty() -> {
                Log.i(TAG_NET, "✓ 识别为缓存文本内容（无音频）: ${message.answer}")

                // 累积完整的文本内容
                completeLlmAnswer.append(message.answer)
                Log.i(TAG_NET, "✓ 累积缓存文本内容，当前总长度: ${completeLlmAnswer.length}")

                // 立即显示累积的文本内容
                runOnUiThread {
                    // 防止并发清空问题，先保存当前完整内容
                    val currentCompleteText = completeLlmAnswer.toString()
                    if (currentCompleteText.isNotEmpty()) {
                        currentLlmAnswer.clear()
                        currentLlmAnswer.append(currentCompleteText)
                        displayMarkdownAnswer(currentLlmAnswer.toString())
                        Log.i(TAG_NET, "✓ 立即显示缓存文本，当前长度: ${currentLlmAnswer.length}")
                    } else {
                        Log.w(TAG_NET, "⚠ 缓存文本内容为空，跳过显示")
                    }
                }
            }

            // 处理有音频URL但不是标准message格式的内容（修复缓存问题文本不显示的问题）
            !message.answer.isNullOrEmpty() && !message.url.isNullOrEmpty() && message.event != "message" -> {
                Log.i(TAG_NET, "✓ 识别为非标准格式的文本+音频内容: ${message.answer}")

                // 累积完整的文本内容
                completeLlmAnswer.append(message.answer)
                Log.i(TAG_NET, "✓ 累积非标准文本内容，当前总长度: ${completeLlmAnswer.length}")

                // 立即显示累积的文本内容
                runOnUiThread {
                    // 防止并发清空问题，先保存当前完整内容
                    val currentCompleteText = completeLlmAnswer.toString()
                    if (currentCompleteText.isNotEmpty()) {
                        currentLlmAnswer.clear()
                        currentLlmAnswer.append(currentCompleteText)
                        displayMarkdownAnswer(currentLlmAnswer.toString())
                        Log.i(TAG_NET, "✓ 立即显示非标准文本，当前长度: ${currentLlmAnswer.length}")
                    } else {
                        Log.w(TAG_NET, "⚠ 非标准文本内容为空，跳过显示")
                    }
                }

                // 添加到音频队列
                Log.i(TAG_NET, "✓ 有音频URL，添加到播放队列: ${message.url}")
                addToAudioQueue(message.url)
            }

            // 处理data数组：显示建议问题列表
            !message.data.isNullOrEmpty() -> {
                Log.i(TAG_NET, "✓ 识别为建议问题数据: ${message.data}")
                runOnUiThread {
                    // 确保开场白保持隐藏状态
                    hideWelcomeContent()
                    displaySuggestedQuestionsList(message.data)
                }
            }

            // 处理image_link事件：添加图片到媒体展示区域
            message.event == "image_link" && message.linkData != null -> {
                try {
                    Log.i(TAG_NET, "========== 处理图片链接事件 ==========")
                    Log.i(TAG_NET, "图片标题: ${message.linkData.title}")
                    Log.i(TAG_NET, "图片URL: ${message.linkData.url}")
                    Log.i(TAG_NET, "URL长度: ${message.linkData.url?.length ?: 0}")

                    // 验证URL格式
                    val imageUrl = message.linkData.url ?: ""
                    if (imageUrl.isEmpty()) {
                        Log.e(TAG_NET, "⚠️ 图片URL为空，跳过处理")
                        return
                    }

                    if (!imageUrl.startsWith("http://") && !imageUrl.startsWith("https://")) {
                        Log.e(TAG_NET, "⚠️ 图片URL格式无效: $imageUrl")
                        return
                    }

                    Log.i(TAG_NET, "✓ 图片URL验证通过，创建媒体项")
                    val mediaItem = MediaDisplayItem(
                        title = message.linkData.title ?: "未知图片",
                        url = imageUrl,
                        type = MediaDisplayItem.Type.IMAGE
                    )
                    runOnUiThread {
                        // 确保开场白保持隐藏状态（修复UI错乱问题）
                        hideWelcomeContent()
                        addMediaItem(mediaItem)
                        Log.i(TAG_NET, "✓ 图片媒体项已添加，开场白已确保隐藏")
                    }
                    Log.i(TAG_NET, "========== 图片链接事件处理完成 ==========")
                } catch (e: Exception) {
                    Log.e(TAG_NET, "处理图片链接事件失败: ${e.message}", e)
                }
            }

            // 处理video_link事件：添加视频到媒体展示区域
            message.event == "video_link" && message.linkData != null -> {
                try {
                    Log.i(TAG_NET, "✓ 识别为视频链接事件: ${message.linkData.title} - ${message.linkData.url}")
                    val mediaItem = MediaDisplayItem(
                        title = message.linkData.title ?: "未知视频",
                        url = message.linkData.url ?: "",
                        type = MediaDisplayItem.Type.VIDEO
                    )
                    runOnUiThread {
                        // 确保开场白保持隐藏状态（修复UI错乱问题）
                        hideWelcomeContent()
                        addMediaItem(mediaItem)
                        Log.i(TAG_NET, "✓ 视频媒体项已添加，开场白已确保隐藏")
                    }
                } catch (e: Exception) {
                    Log.e(TAG_NET, "处理视频链接事件失败: ${e.message}", e)
                }
            }

            else -> {
                Log.w(TAG_NET, "⚠ 未识别的LLM消息类型或内容为空")
                Log.w(TAG_NET, "  event: ${message.event}")
                Log.w(TAG_NET, "  greeting: ${message.greeting}")
                Log.w(TAG_NET, "  status: ${message.status}")
                Log.w(TAG_NET, "  answer: ${message.answer?.take(50)}...")
                Log.w(TAG_NET, "  url: ${message.url}")
                Log.w(TAG_NET, "  data: ${message.data}")

                // 兜底逻辑：如果消息包含answer字段且不为空，尝试显示文本（修复缓存响应文本不显示问题）
                if (!message.answer.isNullOrEmpty()) {
                    Log.i(TAG_NET, "✓ 兜底逻辑：检测到answer字段，尝试显示文本内容")

                    // 累积完整的文本内容
                    completeLlmAnswer.append(message.answer)
                    Log.i(TAG_NET, "✓ 兜底逻辑：累积文本内容，当前总长度: ${completeLlmAnswer.length}")

                    // 立即显示累积的文本内容
                    runOnUiThread {
                        // 防止并发清空问题，先保存当前完整内容
                        val currentCompleteText = completeLlmAnswer.toString()
                        if (currentCompleteText.isNotEmpty()) {
                            currentLlmAnswer.clear()
                            currentLlmAnswer.append(currentCompleteText)
                            displayMarkdownAnswer(currentLlmAnswer.toString())
                            Log.i(TAG_NET, "✓ 兜底逻辑：显示文本内容，当前长度: ${currentLlmAnswer.length}")
                        } else {
                            Log.w(TAG_NET, "⚠ 兜底逻辑：文本内容为空，跳过显示")
                        }
                    }

                    // 如果有音频URL，添加到播放队列
                    if (!message.url.isNullOrEmpty()) {
                        Log.i(TAG_NET, "✓ 兜底逻辑：有音频URL，添加到播放队列: ${message.url}")
                        addToAudioQueue(message.url)
                    } else {
                        Log.i(TAG_NET, "✓ 兜底逻辑：无音频URL的文本片段")
                    }
                }
            }
        }
    }

    /**
     * 显示用户问题 - 现在显示在标题栏
     */
    private fun displayUserQuestion(question: String, addToHistory: Boolean = true) {
        Log.i(TAG_NET, "显示用户问题: $question, 添加到历史记录: $addToHistory")
        // 将问题显示在标题栏
        binding.tvConversationTitle.text = question
        binding.cvConversation.visibility = android.view.View.VISIBLE

        // 根据参数决定是否添加问题到历史记录
        if (addToHistory) {
            addQuestionToHistory(question)
        }
    }

    /**
     * 添加媒体项到展示区域
     */
    private fun addMediaItem(mediaItem: MediaDisplayItem) {
        Log.i(TAG_NET, "添加媒体项: ${mediaItem.title}, 类型: ${mediaItem.type}")
        Log.i(TAG_NET, "媒体URL: ${mediaItem.url}")

        // 确保开场白保持隐藏状态（额外保护措施，修复UI错乱问题）
        if (binding.cvOpeningStatement.visibility == android.view.View.VISIBLE) {
            Log.w(TAG_NET, "⚠ 在添加媒体项时检测到开场白仍然可见，强制隐藏")
            hideWelcomeContent()
        }

        // 如果是图片类型，先测试URL可访问性
        if (mediaItem.type == MediaDisplayItem.Type.IMAGE) {
            testImageUrlAccessibility(mediaItem.url)
        }

        // 添加到适配器（MediaAdapter内部会管理自己的列表）
        Log.i(TAG_NET, "调用 mediaAdapter.addMediaItem() 前，适配器状态:")
        Log.i(TAG_NET, "  mediaAdapter.itemCount = ${mediaAdapter.itemCount}")
        Log.i(TAG_NET, "  mediaAdapter = $mediaAdapter")

        mediaAdapter.addMediaItem(mediaItem)

        Log.i(TAG_NET, "调用 mediaAdapter.addMediaItem() 后，适配器状态:")
        Log.i(TAG_NET, "  mediaAdapter.itemCount = ${mediaAdapter.itemCount}")

        // 确保AI回答区域可见（媒体容器的父容器）
        binding.llAiAnswerSection.visibility = android.view.View.VISIBLE
        Log.i(TAG_NET, "AI回答区域可见性设置为: ${binding.llAiAnswerSection.visibility}")

        // 显示媒体容器
        binding.mediaContainer.visibility = android.view.View.VISIBLE
        Log.i(TAG_NET, "媒体容器可见性设置为: ${binding.mediaContainer.visibility}")

        // 强制触发布局更新
        binding.llAiAnswerSection.requestLayout()
        binding.mediaContainer.requestLayout()
        binding.rvMediaItems.requestLayout()

        // 延迟检查RecyclerView状态，等待布局完成
        binding.rvMediaItems.post {
            Log.i(TAG_NET, "布局完成后的RecyclerView状态:")
            Log.i(TAG_NET, "  adapter = ${binding.rvMediaItems.adapter}")
            Log.i(TAG_NET, "  layoutManager = ${binding.rvMediaItems.layoutManager}")
            Log.i(TAG_NET, "  visibility = ${binding.rvMediaItems.visibility}")
            Log.i(TAG_NET, "  width = ${binding.rvMediaItems.width}")
            Log.i(TAG_NET, "  height = ${binding.rvMediaItems.height}")
            Log.i(TAG_NET, "  childCount = ${binding.rvMediaItems.childCount}")

            // 检查父容器状态
            Log.i(TAG_NET, "媒体容器状态:")
            Log.i(TAG_NET, "  mediaContainer.visibility = ${binding.mediaContainer.visibility}")
            Log.i(TAG_NET, "  mediaContainer.width = ${binding.mediaContainer.width}")
            Log.i(TAG_NET, "  mediaContainer.height = ${binding.mediaContainer.height}")

            // 检查AI回答区域状态
            Log.i(TAG_NET, "AI回答区域状态:")
            Log.i(TAG_NET, "  llAiAnswerSection.visibility = ${binding.llAiAnswerSection.visibility}")
            Log.i(TAG_NET, "  llAiAnswerSection.width = ${binding.llAiAnswerSection.width}")
            Log.i(TAG_NET, "  llAiAnswerSection.height = ${binding.llAiAnswerSection.height}")

            // 检查NestedScrollView状态
            Log.i(TAG_NET, "NestedScrollView状态:")
            Log.i(TAG_NET, "  nestedScrollViewConversation.visibility = ${binding.nestedScrollViewConversation.visibility}")
            Log.i(TAG_NET, "  nestedScrollViewConversation.width = ${binding.nestedScrollViewConversation.width}")
            Log.i(TAG_NET, "  nestedScrollViewConversation.height = ${binding.nestedScrollViewConversation.height}")

            // 强制设置RecyclerView高度
            Log.w(TAG_NET, "强制设置RecyclerView高度为150dp")
            val layoutParams = binding.rvMediaItems.layoutParams
            layoutParams.height = 150.dpToPx() // 设置150dp的高度
            binding.rvMediaItems.layoutParams = layoutParams

            // 强制刷新适配器
            Log.i(TAG_NET, "强制刷新适配器")
            binding.rvMediaItems.adapter?.notifyDataSetChanged()

            // 再次请求布局
            binding.rvMediaItems.requestLayout()
            binding.mediaContainer.requestLayout()

            // 再次延迟检查
            binding.rvMediaItems.postDelayed({
                Log.i(TAG_NET, "第二次检查RecyclerView状态:")
                Log.i(TAG_NET, "  width = ${binding.rvMediaItems.width}")
                Log.i(TAG_NET, "  height = ${binding.rvMediaItems.height}")
                Log.i(TAG_NET, "  childCount = ${binding.rvMediaItems.childCount}")
                Log.i(TAG_NET, "  adapter.itemCount = ${binding.rvMediaItems.adapter?.itemCount}")
            }, 500)
        }

        // 从适配器获取实际的媒体数量和类型信息
        val mediaCount = mediaAdapter.itemCount
        val hasImages = mediaAdapter.hasImages()
        val hasVideos = mediaAdapter.hasVideos()

        Log.i(TAG_NET, "媒体统计:")
        Log.i(TAG_NET, "  总数量: $mediaCount")
        Log.i(TAG_NET, "  包含图片: $hasImages")
        Log.i(TAG_NET, "  包含视频: $hasVideos")

        val mediaTypeText = when {
            hasImages && hasVideos -> "相关图片和视频"
            hasImages -> "相关图片"
            hasVideos -> "相关视频"
            else -> "相关媒体"
        }
        binding.tvMediaTitle.text = "$mediaTypeText ($mediaCount)"
        Log.i(TAG_NET, "媒体标题设置为: $mediaTypeText ($mediaCount)")

        // 添加媒体内容后自动滚动到底部，让用户看到新添加的媒体内容
        scrollAiAnswerToBottom()

        Log.i(TAG_NET, "✓ 媒体项添加完成，当前媒体数量: $mediaCount，开场白已确保隐藏")
    }

    /**
     * 测试图片URL的可访问性
     */
    private fun testImageUrlAccessibility(imageUrl: String) {
        Log.i(TAG_NET, "========== 测试图片URL可访问性 ==========")
        Log.i(TAG_NET, "测试URL: $imageUrl")

        Thread {
            try {
                // 使用GET请求，但设置Range头只获取前1024字节来减少流量
                val request = Request.Builder()
                    .url(imageUrl)
                    .get()
                    .addHeader("User-Agent", "Android App")
                    .addHeader("Range", "bytes=0-1023") // 只获取前1KB数据
                    .build()

                okHttpClient.newCall(request).execute().use { response ->
                    Log.i(TAG_NET, "URL测试结果:")
                    Log.i(TAG_NET, "  状态码: ${response.code}")
                    Log.i(TAG_NET, "  状态消息: ${response.message}")
                    Log.i(TAG_NET, "  Content-Type: ${response.header("Content-Type")}")
                    Log.i(TAG_NET, "  Content-Length: ${response.header("Content-Length")}")
                    Log.i(TAG_NET, "  Content-Range: ${response.header("Content-Range")}")

                    if (response.isSuccessful || response.code == 206) { // 206是部分内容响应
                        Log.i(TAG_NET, "✓ 图片URL可访问")

                        // 检查Content-Type是否为图片类型
                        val contentType = response.header("Content-Type")
                        if (contentType?.startsWith("image/") == true) {
                            Log.i(TAG_NET, "✓ 确认为图片类型: $contentType")
                        } else {
                            Log.w(TAG_NET, "⚠ 可能不是图片类型: $contentType")
                        }
                    } else {
                        Log.e(TAG_NET, "✗ 图片URL访问失败: ${response.code} ${response.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG_NET, "✗ 图片URL测试异常: ${e.message}", e)
            }
        }.start()

        Log.i(TAG_NET, "========== URL可访问性测试完成 ==========")
    }

    /**
     * 自动滚动AI回复区域到底部
     * 确保用户能看到最新的AI回复内容，无需手动滑动
     */
    private fun scrollAiAnswerToBottom() {
        val scrollView = binding.cvConversation.findViewById<androidx.core.widget.NestedScrollView>(R.id.nested_scroll_view_conversation)
        scrollView?.post {
            scrollView.fullScroll(android.view.View.FOCUS_DOWN)
            Log.i(TAG_NET, "✓ AI回复区域自动滚动到底部")
        }
    }



    /**
     * 用Markdown格式渲染并显示回答文本
     */
    private fun displayMarkdownAnswer(answer: String) {
        if (answer.length > 100) {
            Log.i(TAG_NET, "显示Markdown回答: ${answer.take(100)}...(总长度: ${answer.length})")
        } else {
            Log.i(TAG_NET, "显示Markdown回答: $answer")
        }

        // 确保开场白保持隐藏状态
        if (binding.cvOpeningStatement.visibility == android.view.View.VISIBLE) {
            Log.w(TAG_NET, "⚠ 在显示文本回答时检测到开场白仍然可见，强制隐藏")
            hideWelcomeContent()
        }

        try {
            // 检查内容是否有变化，避免不必要的重新渲染
            val currentText = binding.tvAiAnswer.text?.toString() ?: ""
            if (currentText == answer) {
                Log.d(TAG_NET, "内容未变化，跳过重新渲染")
                return
            }
            
            try {
                // 使用Markwon渲染Markdown文本
                markwon.setMarkdown(binding.tvAiAnswer, answer)
                binding.llAiAnswerSection.visibility = android.view.View.VISIBLE
                binding.cvConversation.visibility = android.view.View.VISIBLE
            } catch (e: Exception) {
                Log.e(TAG_NET, "Markdown渲染失败: ${e.message}", e)
                // 如果Markdown渲染失败，直接显示纯文本
                binding.tvAiAnswer.text = answer
                binding.llAiAnswerSection.visibility = android.view.View.VISIBLE
                binding.cvConversation.visibility = android.view.View.VISIBLE
            }

        } catch (e: Exception) {
            Log.e(TAG_NET, "显示文本失败: ${e.message}", e)
        }
    }
    




    /**
     * 在底部显示建议问题列表（水平排列）
     */
    private fun displaySuggestedQuestionsList(questions: List<String>) {
        Log.i(TAG_NET, "显示建议问题列表: $questions")
        try {
            binding.llLlmSuggestionsContainer.removeAllViews()
            Log.i(TAG_NET, "✓ 清空旧的建议问题")

            // 只显示前3个问题，水平排列
            questions.take(3).forEachIndexed { index, question ->
                val button = Button(this).apply {
                    text = question
                    textSize = 11f
                    setPadding(10, 6, 10, 6)

                    // 设置按钮样式 - 使用与推荐问题相同的背景
                    background = ContextCompat.getDrawable(this@CallActivity, R.drawable.bg_digital_suggested_question)
                    setTextColor(ContextCompat.getColor(this@CallActivity, R.color.digital_text_primary))

                    // 设置字体样式
                    typeface = android.graphics.Typeface.DEFAULT_BOLD
                    letterSpacing = 0.02f

                    // 设置布局参数 - 水平排列，每个按钮占据相等宽度
                    val layoutParams = LinearLayout.LayoutParams(
                        0,
                        (40 * resources.displayMetrics.density).toInt(),  // 固定高度40dp
                        1f  // 权重为1，平均分配宽度
                    ).apply {
                        if (index < questions.take(3).size - 1) {
                            setMargins(0, 0, 8, 0) // 右边距，最后一个按钮不需要
                        }
                    }
                    this.layoutParams = layoutParams

                    // 设置阴影效果
                    elevation = 4f

                    // 点击事件 - 发送选中的问题到LLM
                    setOnClickListener {
                        Log.i(TAG_NET, "========== 用户点击底部建议问题 ==========")
                        Log.i(TAG_NET, "点击的建议问题: $question")
                        Log.i(TAG_NET, "点击前历史问题: ${historyQuestions.joinToString(", ")}")

                        // 隐藏建议问题列表
                        binding.llLlmSuggestionsContainer.visibility = android.view.View.GONE
                        // 清空之前的回答显示
                        clearAiAnswerDisplay()

                        // 设置当前会话问题，避免SSE返回时重复添加到历史记录
                        currentSessionQuestion = question
                        Log.i(TAG_NET, "✓ 设置当前会话问题: $currentSessionQuestion")

                        // 检查问题是否在历史记录中，决定请求类型
                        Log.i(TAG_NET, "🔍 检查底部建议问题是否在历史记录中:")
                        Log.i(TAG_NET, "  当前问题: '$question'")
                        Log.i(TAG_NET, "  历史问题列表: ${historyQuestions.joinToString(", ") { "'$it'" }}")

                        // 清理问题文本进行比较
                        val cleanQuestion = question.trim().replace(Regex("\\s+"), " ")
                        val isInHistory = historyQuestions.any { historyQuestion ->
                            val cleanHistoryQuestion = historyQuestion.trim().replace(Regex("\\s+"), " ")
                            cleanQuestion == cleanHistoryQuestion
                        }

                        val requestType = if (isInHistory) {
                            Log.i(TAG_NET, "✓ 该底部建议问题在历史记录中，使用history_click类型（跳过greeting）")
                            "history_click"
                        } else {
                            Log.i(TAG_NET, "✓ 该底部建议问题不在历史记录中，使用suggestion_click类型（播放greeting）")
                            "suggestion_click"
                        }

                        // 发送选中的问题到LLM
                        sendAsrResultToLlm(question, resetSession = false, requestType = requestType)
                    }
                }

                binding.llLlmSuggestionsContainer.addView(button)
                Log.i(TAG_NET, "✓ 添加建议问题按钮: $question")
            }

            binding.llLlmSuggestionsContainer.visibility = android.view.View.VISIBLE
            Log.i(TAG_NET, "✓ 建议问题列表显示成功，共${questions.size}个按钮")

        } catch (e: Exception) {
            Log.e(TAG_NET, "✗ 建议问题列表显示失败: ${e.message}", e)
        }
    }

    /**
     * 添加音频到播放队列
     */
    private fun addToAudioQueue(audioUrl: String) {
        Log.i(TAG_NET, "添加音频到队列: $audioUrl")
        audioPlayQueue.add(audioUrl)

        // 重置打断标记，开始新的播放会话
        isAudioInterrupted = false
        isManualInterrupt = false  // 🔧 重置手动打断标志
        Log.i(TAG_NET, "✓ 重置音频打断标记，开始新的播放会话")

        // 如果当前没有播放音频，开始播放队列
        if (!isPlayingLlmAudio) {
            playNextInQueue()
        }
    }


    
    /**
     * 播放队列中的下一个音频
     */
    private fun playNextInQueue() {
        if (audioPlayQueue.isEmpty()) {
            Log.i(TAG_NET, "音频队列为空，所有音频播放完成")
            isPlayingLlmAudio = false
            currentPlayingAudioUrl = null

            // 所有音频播放完成，恢复按钮到初始蓝色状态
            runOnUiThread {
                binding.btnRecord.isEnabled = true
                updateRecordButton("等待唤醒")  // 新增状态：等待唤醒（蓝色）
                hideInterruptHint()  // 隐藏打断提示条
            }

            // 🔧 关键修复：所有音频播放完成后重新启动唤醒词监听（使用强制重启机制）
            Log.i(TAG_NET, "所有音频播放完成，重新启动唤醒词监听")
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                if (localAsrEnabled) {
                    // 🔧 使用与红色按钮相同的强制重启机制，解决状态不同步问题
                    if (!isKwsRecording) {
                        Log.i(TAG_NET, "重新启动唤醒词监听以支持下次唤醒")
                        startLocalKws()
                    } else {
                        Log.w(TAG_NET, "⚠ 检测到状态不同步，强制重新初始化唤醒词监听")
                        Log.w(TAG_NET, "当前状态 - isKwsRecording: $isKwsRecording, recordingState: ${kwsAudioRecord?.recordingState}")
                        isKwsRecording = false
                        startLocalKws()
                    }
                } else {
                    Log.i(TAG_NET, "本地ASR未启用，跳过唤醒词监听重启")
                }
            }, 500) // 延迟500ms确保音频完全停止

            // 启动返回开场白定时器
            startReturnToGreetingTimer()

            Log.i(TAG_NET, "✓ 所有音频播放完成，按钮恢复到蓝色状态")
            return
        }
        
        if (isPlayingLlmAudio) {
            Log.i(TAG_NET, "正在播放音频，等待当前音频播放完成")
            return
        }
        
        val nextAudioUrl = audioPlayQueue.removeAt(0)
        Log.i(TAG_NET, "开始播放队列中的音频: $nextAudioUrl")
        currentPlayingAudioUrl = nextAudioUrl
        isPlayingLlmAudio = true
        downloadAndPlayLlmAudio(nextAudioUrl)
    }



    /**
     * LLM音频播放完成回调
     */
    private fun onLlmAudioPlayCompleted() {
        val completedUrl = currentPlayingAudioUrl
        Log.i(TAG_NET, "LLM音频播放完成: $completedUrl")
        Log.i(TAG_NET, "当前打断状态: isAudioInterrupted=$isAudioInterrupted")

        // 清除该音频的重试计数
        if (completedUrl != null) {
            audioRetryMap.remove(completedUrl)
        }

        isPlayingLlmAudio = false
        currentPlayingAudioUrl = null

        // 检查是否用户已经打断了播放
        if (isAudioInterrupted) {
            Log.i(TAG_NET, "✓ 检测到用户打断，停止播放队列中的剩余音频")
            // 清空队列，不再播放后续音频
            clearAudioQueue()
            // 重置打断标记
            isAudioInterrupted = false
            // 确保按钮状态正确
            runOnUiThread {
                binding.btnRecord.isEnabled = true
                updateRecordButton("等待唤醒")
                hideInterruptHint()
            }
            Log.i(TAG_NET, "✓ 音频播放已完全停止，按钮恢复到等待唤醒状态")
            return
        }

        // 播放队列中的下一个音频
        if (audioPlayQueue.isNotEmpty()) {
            Log.i(TAG_NET, "继续播放下一个音频")
            playNextInQueue()
        } else {
            // 所有音频播放完毕
            Log.i(TAG_NET, "========== 所有LLM音频播放完毕 ==========")
            Log.i(TAG_NET, "音频队列为空，检查是否可以启动返回开场白定时器")

            runOnUiThread {
                // 恢复按钮状态
                binding.btnRecord.isEnabled = true
                updateRecordButton("等待唤醒")

                // 🔧 修复：只有在SSE流也结束时才显示播放完成提示，否则显示可打断提示
                if (isLlmStreamActive) {
                    Log.i(TAG_NET, "SSE流仍在活跃，显示可打断提示")
                    showFriendlyHint("playing")
                } else {
                    Log.i(TAG_NET, "SSE流已结束，显示播放完成提示")
                    showFriendlyHint("playback_complete")
                }

                // 🔧 统一启动返回开场白定时器（移除手动打断的限制）
                if (isLlmStreamActive) {
                    Log.i(TAG_NET, "⚠ SSE流仍在活跃，暂不启动返回开场白定时器，等待流结束")
                } else {
                    Log.i(TAG_NET, "✓ SSE流已结束，启动返回开场白定时器")
                    startReturnToGreetingTimer()
                }
            }

            Log.i(TAG_NET, "✓ 所有音频播放完成，按钮恢复到蓝色状态")
        }
    }
    
    /**
     * LLM音频播放失败回调
     */
    private fun onLlmAudioPlayError(error: String) {
        val failedUrl = currentPlayingAudioUrl
        Log.e(TAG_NET, "LLM音频播放失败: $failedUrl, 错误: $error")

        if (failedUrl != null) {
            val retryCount = audioRetryMap.getOrDefault(failedUrl, 0)
            if (retryCount < maxAudioRetryCount) {
                // 重试播放
                audioRetryMap[failedUrl] = retryCount + 1
                Log.i(TAG_NET, "准备重试播放音频: $failedUrl, 第${retryCount + 1}次重试")

                // 延迟1秒后重试
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    downloadAndPlayLlmAudio(failedUrl)
                }, 1000)
                return
            } else {
                // 超过最大重试次数，放弃该音频
                Log.e(TAG_NET, "音频重试超过最大次数，放弃播放: $failedUrl")
                audioRetryMap.remove(failedUrl)
                runOnUiThread {
                    Toast.makeText(this@CallActivity, "音频播放失败，已跳过", Toast.LENGTH_SHORT).show()
                }
            }
        }

        isPlayingLlmAudio = false
        currentPlayingAudioUrl = null

        // 继续播放队列中的下一个音频
        playNextInQueue()
    }
    
    /**
     * 清空音频播放队列
     */
    private fun clearAudioQueue() {
        Log.i(TAG_NET, "清空音频播放队列，队列大小: ${audioPlayQueue.size}")
        audioPlayQueue.clear()
        audioRetryMap.clear()
        isPlayingLlmAudio = false
        currentPlayingAudioUrl = null
    }

    /**
     * 在onResume后恢复ASR状态
     * 注意：不恢复音频播放，因为用户切换回来时不应该继续播放之前的内容
     */
    private fun restoreAsrStateAfterResume() {
        Log.i(TAG_NET, "========== 恢复ASR状态 ==========")
        Log.i(TAG_NET, "之前状态 - ASR活跃: $wasAsrActiveBeforePause, KWS活跃: $wasKwsActiveBeforePause")

        // 重置状态变量，避免下次误判
        wasPlayingBeforePause = false

        // 如果启用了本地ASR且之前KWS是活跃的，恢复唤醒词监听
        if (localAsrEnabled && wasKwsActiveBeforePause) {
            Log.i(TAG_NET, "恢复唤醒词监听")
            try {
                // 🔧 使用强制重启机制，确保唤醒词监听正常工作
                if (!isKwsRecording) {
                    Log.i(TAG_NET, "重新启动唤醒词监听")
                    startLocalKws()
                } else {
                    Log.w(TAG_NET, "⚠ 检测到状态不同步，强制重新初始化唤醒词监听")
                    Log.w(TAG_NET, "当前状态 - isKwsRecording: $isKwsRecording, recordingState: ${kwsAudioRecord?.recordingState}")
                    isKwsRecording = false
                    startLocalKws()
                }
                Log.i(TAG_NET, "✓ 唤醒词监听已恢复")
            } catch (e: Exception) {
                Log.e(TAG_NET, "恢复唤醒词监听失败: ${e.message}", e)
            }
        }

        // 重置状态变量
        wasAsrActiveBeforePause = false
        wasKwsActiveBeforePause = false

        // 确保UI状态正确
        runOnUiThread {
            updateRecordButton("等待唤醒")
        }

        Log.i(TAG_NET, "========== ASR状态恢复完成 ==========")
    }

    /**
     * 手动打断当前音频播报
     */
    private fun interruptAudioPlayback() {
        Log.i(TAG_NET, "========== 手动打断音频播报 ==========")

        // 设置打断标记，防止播放完成回调继续播放下一个音频
        isAudioInterrupted = true
        Log.i(TAG_NET, "✓ 设置音频打断标记，防止继续播放队列中的音频")

        // 立即重置播放状态，防止继续播放
        isPlayingLlmAudio = false
        currentPlayingAudioUrl = null
        isProcessingRequest = false
        isProcessingLlmRequest = false  // 重置LLM请求状态

        // 🔧 停止唤醒词监听监控
        stopKwsMonitoring()

        // 停止当前音频播放
        try {
            val stopResult = duix?.stopAudio()
            Log.i(TAG_NET, "调用duix.stopAudio()结果: $stopResult")

            // 多次调用确保停止
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                try {
                    duix?.stopAudio()
                    Log.i(TAG_NET, "二次调用duix.stopAudio()确保停止")
                } catch (e: Exception) {
                    Log.e(TAG_NET, "二次停止音频播放失败: ${e.message}", e)
                }
            }, 100)

        } catch (e: Exception) {
            Log.e(TAG_NET, "停止音频播放失败: ${e.message}", e)
        }

        // 关闭SSE连接，防止继续接收新的音频数据
        try {
            currentSseCall?.cancel()
            Log.i(TAG_NET, "✓ 已取消SSE连接，防止继续接收音频数据")
        } catch (e: Exception) {
            Log.e(TAG_NET, "取消SSE连接失败: ${e.message}", e)
        }



        // 清空音频播放队列
        clearAudioQueue()

        // 🔧 关键修复：清理会话状态，防止问答内容错乱
        Log.i(TAG_NET, "清理会话状态，防止下次对话内容错乱")
        runOnUiThread {
            // 清空当前累积的回答文本
            currentLlmAnswer.clear()
            completeLlmAnswer.clear()

            // 重置会话相关状态
            currentSessionQuestion = null

            Log.i(TAG_NET, "✓ 会话状态已清理，下次对话将使用全新上下文")
        }

        // 🔧 根据打断类型决定UI更新策略
        if (isManualInterrupt) {
            Log.i(TAG_NET, "手动打断，将在2秒后显示友好提示或返回首页")

            // 更新UI状态 - 先恢复基本状态
            runOnUiThread {
                binding.btnRecord.isEnabled = true
                updateRecordButton("等待唤醒")

                // 🔧 手动打断时不立即隐藏提示条，而是显示友好提示
                showManualInterruptFriendlyHint()

                // 重置音频波浪状态为静音
                setAudioWavePlaying(false)
                setAudioWaveRecording(false)
                setAudioWaveActive(false)
            }

            // 🔧 新增：手动打断后也启动返回开场白定时器
            Log.i(TAG_NET, "手动打断后启动返回开场白定时器")
            startReturnToGreetingTimer()

            // 🔧 2秒后重置手动打断标志
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                Log.i(TAG_NET, "手动打断2秒后，重置手动打断标志")
                // 重置手动打断标志
                isManualInterrupt = false
            }, 2000)

        } else {
            // 语音打断或其他情况，使用原有逻辑
            runOnUiThread {
                binding.btnRecord.isEnabled = true
                updateRecordButton("等待唤醒")
                hideInterruptHint()  // 隐藏打断提示条

                // 重置音频波浪状态为静音
                setAudioWavePlaying(false)
                setAudioWaveRecording(false)
                setAudioWaveActive(false)
            }
        }

        // 🔧 关键修复：打断后重新启动唤醒词监听
        Log.i(TAG_NET, "打断操作完成，重新启动唤醒词监听")
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            Log.i(TAG_NET, "========== 打断后唤醒词监听状态检查 ==========")
            Log.i(TAG_NET, "localAsrEnabled: $localAsrEnabled")
            Log.i(TAG_NET, "isKwsRecording: $isKwsRecording")
            Log.i(TAG_NET, "kwsAudioRecord状态: ${kwsAudioRecord?.recordingState}")

            if (localAsrEnabled) {
                if (!isKwsRecording) {
                    Log.i(TAG_NET, "✓ 重新启动唤醒词监听以支持语音打断后的唤醒")
                    startLocalKws()
                } else {
                    Log.w(TAG_NET, "⚠ 唤醒词监听状态异常，强制重新初始化")
                    isKwsRecording = false
                    startLocalKws()
                }
            } else {
                Log.i(TAG_NET, "⚠ 本地ASR未启用，跳过唤醒词监听重启")
            }
            Log.i(TAG_NET, "========== 打断后唤醒词监听状态检查完成 ==========")
        }, 800) // 延迟800ms确保音频完全停止

        Log.i(TAG_NET, "✓ 音频播报已打断，恢复正常状态")
        Log.i(TAG_NET, "✓ 播放状态已重置: isPlayingLlmAudio=$isPlayingLlmAudio, isProcessingRequest=$isProcessingRequest")
        Log.i(TAG_NET, "✓ LLM请求状态已重置: isProcessingLlmRequest=$isProcessingLlmRequest")
        Log.i(TAG_NET, "================================")
    }

    /**
     * 🔧 启动唤醒词监听定期检查机制
     */
    private fun startKwsMonitoring() {
        // 停止之前的监控
        stopKwsMonitoring()

        if (!localAsrEnabled) {
            Log.d(TAG_NET, "本地ASR未启用，跳过唤醒词监听监控")
            return
        }

        kwsMonitorHandler = android.os.Handler(android.os.Looper.getMainLooper())
        kwsMonitorRunnable = object : Runnable {
            override fun run() {
                // 只在音频播放期间进行监控
                if (isPlayingLlmAudio) {
                    Log.d(TAG_NET, "========== 定期唤醒词监听检查 ==========")
                    Log.d(TAG_NET, "isKwsRecording: $isKwsRecording")
                    Log.d(TAG_NET, "kwsAudioRecord状态: ${kwsAudioRecord?.recordingState}")
                    Log.d(TAG_NET, "kwsThread活跃: ${kwsThread?.isAlive}")

                    val isKwsWorking = isKwsRecording &&
                        kwsAudioRecord != null &&
                        kwsAudioRecord?.recordingState == android.media.AudioRecord.RECORDSTATE_RECORDING &&
                        kwsThread?.isAlive == true

                    if (!isKwsWorking) {
                        Log.w(TAG_NET, "⚠ 定期检查发现唤醒词监听异常，尝试恢复")
                        Log.w(TAG_NET, "异常详情 - isKwsRecording: $isKwsRecording")
                        Log.w(TAG_NET, "异常详情 - recordingState: ${kwsAudioRecord?.recordingState}")
                        Log.w(TAG_NET, "异常详情 - threadAlive: ${kwsThread?.isAlive}")

                        // 🔧 强制重新启动，提高成功率
                        isKwsRecording = false
                        try {
                            kwsAudioRecord?.stop()
                            kwsAudioRecord?.release()
                            kwsAudioRecord = null
                            kwsThread?.interrupt()
                            kwsThread = null
                        } catch (e: Exception) {
                            Log.e(TAG_NET, "清理异常唤醒词状态失败: ${e.message}")
                        }

                        // 🔧 立即重启，不延迟
                        Log.i(TAG_NET, "🔧 定期检查：立即重新启动唤醒词监听")
                        try {
                            startLocalKws()
                            Log.i(TAG_NET, "✓ 定期检查：唤醒词监听重启成功")
                        } catch (e: Exception) {
                            Log.e(TAG_NET, "定期检查：唤醒词监听重启失败: ${e.message}")
                        }
                    } else {
                        Log.d(TAG_NET, "✓ 定期检查：唤醒词监听正常工作")
                    }

                    Log.d(TAG_NET, "========== 定期检查完成 ==========")

                    // 🔧 继续下次检查，缩短间隔提高监控频率
                    kwsMonitorHandler?.postDelayed(this, 1500) // 每1.5秒检查一次，提高监控频率
                } else {
                    Log.d(TAG_NET, "音频播放已结束，停止唤醒词监听监控")
                    stopKwsMonitoring()
                }
            }
        }

        // 🔧 开始第一次检查，缩短启动延迟
        kwsMonitorHandler?.postDelayed(kwsMonitorRunnable!!, 500) // 500ms后开始检查，更快响应
        Log.i(TAG_NET, "✓ 已启动唤醒词监听定期检查机制")
    }

    /**
     * 🔧 停止唤醒词监听定期检查机制
     */
    private fun stopKwsMonitoring() {
        kwsMonitorRunnable?.let { runnable ->
            kwsMonitorHandler?.removeCallbacks(runnable)
        }
        kwsMonitorHandler = null
        kwsMonitorRunnable = null
        Log.d(TAG_NET, "✓ 已停止唤醒词监听定期检查机制")
    }

    /**
     * 清空AI回答显示区域
     */
    private fun clearAiAnswerDisplay() {
        // 清空标题栏
        binding.tvConversationTitle.text = "用户的问题"
        binding.tvAiAnswer.text = ""
        binding.llAiAnswerSection.visibility = android.view.View.GONE
        binding.cvConversation.visibility = android.view.View.GONE
        // 清空累积的回答文本
        currentLlmAnswer.clear()
        completeLlmAnswer.clear()
        // 清空音频播放队列
        clearAudioQueue()
        // 清空媒体项
        clearMediaItems()
        Log.i(TAG_NET, "✓ 清空AI回答显示区域")
    }

    /**
     * 清空媒体项
     */
    private fun clearMediaItems() {
        mediaAdapter.clearMediaItems()
        binding.mediaContainer.visibility = android.view.View.GONE
        Log.i(TAG_NET, "✓ 清空媒体项")
    }

    /**
     * 先获取唤醒词，然后初始化ASR组件
     */
    private fun fetchWakeWordsAndInitAsr() {
        Thread {
            try {
                Log.i(TAG_NET, "开始获取唤醒词并初始化ASR")

                // 先获取唤醒词列表
                val success = fetchAndUpdateWakeWords()

                if (success) {
                    Log.i(TAG_NET, "唤醒词更新成功，开始初始化ASR组件")
                    initLocalAsrComponents()
                } else {
                    Log.w(TAG_NET, "唤醒词更新失败，使用默认配置初始化ASR")
                    initLocalAsrComponents()
                }

            } catch (e: Exception) {
                Log.e(TAG_NET, "获取唤醒词并初始化ASR失败: ${e.message}", e)
                runOnUiThread {
                    Toast.makeText(this, "语音识别初始化失败: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }.start()
    }

    /**
     * 获取并更新唤醒词文件
     */
    private fun fetchAndUpdateWakeWords(): Boolean {
        return try {
            val ttsBaseUrl = sharedPrefs.getString(SettingsActivity.KEY_TTS_URL, "") ?: ""
            if (ttsBaseUrl.isEmpty()) {
                Log.w(TAG_NET, "TTS服务器地址为空，跳过唤醒词更新")
                return false
            }

            val apiKey = sharedPrefs.getString(SettingsActivity.KEY_APIKEY, "") ?: ""
            if (apiKey.isEmpty()) {
                Log.w(TAG_NET, "API Key为空，跳过唤醒词更新")
                return false
            }

            // 构建唤醒词API URL
            val wakeWordsUrl = "${ttsBaseUrl.trimEnd('/')}/api/wakeword/"
            Log.i(TAG_NET, "获取唤醒词URL: $wakeWordsUrl")

            val request = okhttp3.Request.Builder()
                .url(wakeWordsUrl)
                .addHeader("dify_api_key", apiKey)
                .addHeader("User-Agent", "Android App")
                .addHeader("Accept", "*/*")
                .addHeader("Connection", "keep-alive")
                .build()

            val response = okHttpClient.newCall(request).execute()

            if (response.isSuccessful) {
                val responseBody = response.body?.string() ?: ""
                Log.i(TAG_NET, "唤醒词API响应: $responseBody")

                val gson = com.google.gson.Gson()
                val wakeWordItems = gson.fromJson(responseBody, Array<ai.guiji.duix.test.model.WakeWordItem>::class.java).toList()

                Log.i(TAG_NET, "获取到${wakeWordItems.size}个唤醒词")

                // 更新本地keywords.txt文件
                updateLocalWakeWordFile(wakeWordItems)

                true
            } else {
                Log.e(TAG_NET, "获取唤醒词失败: ${response.code} ${response.message}")
                false
            }

        } catch (e: Exception) {
            Log.e(TAG_NET, "获取唤醒词异常: ${e.message}", e)
            false
        }
    }

    /**
     * 更新本地唤醒词文件（同时更新assets和私有目录）
     */
    private fun updateLocalWakeWordFile(wakeWordItems: List<ai.guiji.duix.test.model.WakeWordItem>) {
        try {
            // 获取assets目录下的keywords.txt文件路径
            val keywordsFilePath = "sherpa-onnx-kws-zipformer-wenetspeech-3.3M-2024-01-01/keywords.txt"

            // 读取现有的keywords.txt文件内容
            val existingKeywords = mutableSetOf<String>()
            val existingLines = mutableListOf<String>()
            try {
                assets.open(keywordsFilePath).use { inputStream ->
                    inputStream.bufferedReader().useLines { lines ->
                        lines.forEach { line ->
                            if (line.trim().isNotEmpty()) {
                                existingLines.add(line)
                                if (line.contains("@")) {
                                    val keyword = line.substringAfter("@").trim()
                                    existingKeywords.add(keyword)
                                }
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Log.w(TAG_NET, "读取现有唤醒词文件失败: ${e.message}")
            }

            // 准备新的唤醒词内容
            val newKeywords = wakeWordItems.map { it.word }.toSet()
            val allKeywords = existingKeywords + newKeywords

            // 生成新的keywords.txt内容
            val newContent = StringBuilder()

            // 保留原有的唤醒词（从assets读取的）
            try {
                assets.open(keywordsFilePath).use { inputStream ->
                    inputStream.bufferedReader().useLines { lines ->
                        lines.forEach { line ->
                            if (line.trim().isNotEmpty()) {
                                newContent.appendLine(line)
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Log.w(TAG_NET, "读取原始唤醒词文件失败: ${e.message}")
            }

            // 添加新的唤醒词（直接使用后端返回的拼音格式）
            for (item in wakeWordItems) {
                val keyword = item.word
                if (!existingKeywords.contains(keyword)) {
                    // 直接使用后端返回的pinyin_format字段
                    val pinyinLine = item.pinyinFormat ?: convertToPinyinFormat(keyword)
                    newContent.appendLine(pinyinLine)
                    Log.i(TAG_NET, "添加新唤醒词: $keyword -> $pinyinLine")
                }
            }

            // 创建私有模型目录
            val privateModelDir = java.io.File(filesDir, "sherpa-onnx-kws-zipformer-wenetspeech-3.3M-2024-01-01")
            if (!privateModelDir.exists()) {
                privateModelDir.mkdirs()
                Log.i(TAG_NET, "创建私有模型目录: ${privateModelDir.absolutePath}")

                // 复制模型文件到私有目录（除了keywords.txt）
                copyAssetDirToPrivate("sherpa-onnx-kws-zipformer-wenetspeech-3.3M-2024-01-01", privateModelDir)
            }

            // 将更新后的keywords.txt写入到私有模型目录
            val privateKeywordsFile = java.io.File(privateModelDir, "keywords.txt")
            privateKeywordsFile.writeText(newContent.toString())

            // 私有目录keywords.txt文件已更新

            Log.i(TAG_NET, "唤醒词文件更新完成，共${allKeywords.size}个唤醒词")
            Log.i(TAG_NET, "私有唤醒词文件路径: ${privateKeywordsFile.absolutePath}")

        } catch (e: Exception) {
            Log.e(TAG_NET, "更新本地唤醒词文件失败: ${e.message}", e)
        }
    }

    /**
     * 将中文转换为拼音格式（备用方法，主要使用后端返回的pinyin_format）
     */
    private fun convertToPinyinFormat(chinese: String): String {
        // 这个方法现在主要作为备用，优先使用后端返回的pinyin_format字段
        Log.w(TAG_NET, "后端未提供拼音格式，使用备用转换: $chinese")

        // 简单的备用格式，建议在后端完善pinyin_format字段
        return "# 需要后端提供拼音格式: $chinese"
    }



    /**
     * 在运行时更新assets目录的keywords.txt文件，添加当前唤醒词
     */
    private fun updateAssetsKeywordsWithCurrentWakeWord(privateKeywordsFile: File) {
        try {
            Log.i(TAG_NET, "尝试在运行时更新assets目录的keywords.txt文件")

            // 获取当前唤醒词
            val currentWakeWord = getCurrentWakeWord()
            Log.i(TAG_NET, "当前设置的唤醒词: $currentWakeWord")

            // 检查私有文件是否存在且包含当前唤醒词
            if (privateKeywordsFile.exists()) {
                val privateContent = privateKeywordsFile.readText()
                val privateLines = privateContent.lines().filter { it.trim().isNotEmpty() }

                // 查找当前唤醒词的行
                val currentWakeWordLine = privateLines.find { it.contains("@$currentWakeWord") }

                if (currentWakeWordLine != null) {
                    Log.i(TAG_NET, "找到当前唤醒词行: $currentWakeWordLine")

                    // 读取assets目录的keywords.txt
                    val assetsContent = assets.open("sherpa-onnx-kws-zipformer-wenetspeech-3.3M-2024-01-01/keywords.txt").bufferedReader().readText()
                    val assetsLines = assetsContent.lines().filter { it.trim().isNotEmpty() }.toMutableList()

                    // 检查assets中是否已包含此唤醒词
                    val hasInAssets = assetsLines.any { it.contains("@$currentWakeWord") }

                    if (!hasInAssets) {
                        Log.i(TAG_NET, "添加唤醒词到assets内容: $currentWakeWordLine")
                        assetsLines.add(currentWakeWordLine)

                        // 尝试直接写入assets目录（这可能会失败，但值得尝试）
                        try {
                            // 获取assets目录的实际路径
                            val assetsKeywordsPath = File(applicationInfo.sourceDir).parentFile?.let {
                                File(it, "assets/sherpa-onnx-kws-zipformer-wenetspeech-3.3M-2024-01-01/keywords.txt")
                            }

                            if (assetsKeywordsPath?.exists() == true && assetsKeywordsPath.canWrite()) {
                                val newContent = assetsLines.joinToString("\n")
                                assetsKeywordsPath.writeText(newContent)
                                Log.i(TAG_NET, "✓ 成功直接更新assets目录的keywords.txt文件")
                            } else {
                                Log.w(TAG_NET, "assets目录不可写，使用反射方法")
                                // 使用反射或其他方法尝试修改assets内容
                                updateAssetsContentViaReflection(assetsLines.joinToString("\n"))
                            }
                        } catch (e: Exception) {
                            Log.w(TAG_NET, "直接修改assets文件失败: ${e.message}")
                            // 如果直接修改失败，尝试其他方法
                            updateAssetsContentViaReflection(assetsLines.joinToString("\n"))
                        }
                    } else {
                        Log.i(TAG_NET, "当前唤醒词已存在于assets中，无需更新")
                    }
                } else {
                    Log.w(TAG_NET, "在私有文件中未找到当前唤醒词'$currentWakeWord'")
                }
            } else {
                Log.w(TAG_NET, "私有keywords文件不存在")
            }
        } catch (e: Exception) {
            Log.e(TAG_NET, "更新assets keywords文件失败: ${e.message}", e)
        }
    }

    /**
     * 通过反射或其他技术手段尝试更新assets内容
     */
    private fun updateAssetsContentViaReflection(newContent: String) {
        try {
            Log.i(TAG_NET, "尝试通过反射更新assets内容")
            // 这里可以尝试各种技术手段来修改assets内容
            // 但由于assets通常是只读的，这可能不会成功
            Log.w(TAG_NET, "反射方法暂未实现，将使用默认assets内容")
        } catch (e: Exception) {
            Log.e(TAG_NET, "反射更新assets内容失败: ${e.message}", e)
        }
    }

    /**
     * 验证并显示当前使用的keywords.txt文件内容
     */
    private fun verifyKeywordsFile() {
        try {
            val privateModelDir = java.io.File(filesDir, "sherpa-onnx-kws-zipformer-wenetspeech-3.3M-2024-01-01")
            val privateKeywordsFile = java.io.File(privateModelDir, "keywords.txt")

            Log.i(TAG_NET, "=== 验证keywords.txt文件 ===")
            Log.i(TAG_NET, "私有目录: ${privateModelDir.absolutePath}")
            Log.i(TAG_NET, "私有目录存在: ${privateModelDir.exists()}")
            Log.i(TAG_NET, "keywords文件: ${privateKeywordsFile.absolutePath}")
            Log.i(TAG_NET, "keywords文件存在: ${privateKeywordsFile.exists()}")

            if (privateKeywordsFile.exists()) {
                val content = privateKeywordsFile.readText()
                Log.i(TAG_NET, "文件大小: ${content.length} 字符")
                Log.i(TAG_NET, "文件权限: 可读=${privateKeywordsFile.canRead()}, 可写=${privateKeywordsFile.canWrite()}")

                val lines = content.lines().filter { it.trim().isNotEmpty() }
                Log.i(TAG_NET, "有效行数: ${lines.size}")

                Log.i(TAG_NET, "内容:")
                lines.forEachIndexed { index, line ->
                    Log.i(TAG_NET, "  ${index + 1}: $line")
                }

                // 检查当前唤醒词
                val currentWakeWord = getCurrentWakeWord()
                val hasCurrentWakeWord = lines.any { it.contains("@$currentWakeWord") }
                Log.i(TAG_NET, "当前设置的唤醒词: $currentWakeWord")
                Log.i(TAG_NET, "文件中是否包含当前唤醒词: $hasCurrentWakeWord")

                // 对比assets文件
                try {
                    val assetsContent = assets.open("sherpa-onnx-kws-zipformer-wenetspeech-3.3M-2024-01-01/keywords.txt").bufferedReader().readText()
                    val assetsLines = assetsContent.lines().filter { it.trim().isNotEmpty() }
                    Log.i(TAG_NET, "Assets文件行数: ${assetsLines.size}, 私有文件行数: ${lines.size}")
                    Log.i(TAG_NET, "私有文件比assets文件多: ${lines.size - assetsLines.size} 行")
                } catch (e: Exception) {
                    Log.w(TAG_NET, "读取assets文件失败: ${e.message}")
                }

            } else {
                Log.w(TAG_NET, "私有目录keywords.txt文件不存在")

                // 检查assets文件
                try {
                    val assetsContent = assets.open("sherpa-onnx-kws-zipformer-wenetspeech-3.3M-2024-01-01/keywords.txt").bufferedReader().readText()
                    Log.i(TAG_NET, "Assets文件内容: ${assetsContent}")
                } catch (e: Exception) {
                    Log.e(TAG_NET, "读取assets文件也失败: ${e.message}")
                }
            }
            Log.i(TAG_NET, "=== keywords.txt验证结束 ===")
        } catch (e: Exception) {
            Log.e(TAG_NET, "验证keywords.txt文件失败: ${e.message}", e)
        }
    }

    /**
     * 复制assets目录到私有目录
     */
    private fun copyAssetDirToPrivate(assetDir: String, targetDir: java.io.File): Boolean {
        return try {
            val assetFiles = assets.list(assetDir) ?: return false

            for (filename in assetFiles) {
                if (filename == "keywords.txt") continue // 跳过keywords.txt，我们会单独处理

                val assetPath = "$assetDir/$filename"
                val targetFile = java.io.File(targetDir, filename)

                assets.open(assetPath).use { input ->
                    targetFile.outputStream().use { output ->
                        input.copyTo(output)
                    }
                }
            }
            true
        } catch (e: Exception) {
            Log.e(TAG_NET, "复制assets目录失败: ${e.message}", e)
            false
        }
    }

    /**
     * 初始化本地ASR组件（在后台线程中执行）
     */
    private fun initLocalAsrComponents() {
        try {
            Log.i(TAG_NET, "开始初始化本地ASR组件（后台线程）")

            // 加载JNI库
            try {
                System.loadLibrary("sherpa-onnx-jni")
            } catch (_: Throwable) {
                // 已加载
            }

            // 初始化VAD
            Log.i(TAG_NET, "开始初始化VAD...")
            val vadConfig = com.k2fsa.sherpa.onnx.getVadModelConfig(0) ?: throw Exception("VAD配置获取失败")
            Log.i(TAG_NET, "VAD配置获取成功")
            asrVad = com.k2fsa.sherpa.onnx.Vad(assets, vadConfig)
            Log.i(TAG_NET, "VAD初始化成功")

            // 初始化离线识别器
            Log.i(TAG_NET, "开始初始化离线识别器...")
            val sampleRateInHz = 16000
            val featConfig = com.k2fsa.sherpa.onnx.getFeatureConfig(sampleRateInHz, 80)
            Log.i(TAG_NET, "特征配置创建成功")
            val modelConfig = com.k2fsa.sherpa.onnx.getOfflineModelConfig(0) ?: throw Exception("ASR模型配置获取失败")
            Log.i(TAG_NET, "ASR模型配置获取成功")
            val recognizerConfig = com.k2fsa.sherpa.onnx.OfflineRecognizerConfig(
                featConfig = featConfig,
                modelConfig = modelConfig
            )
            Log.i(TAG_NET, "识别器配置创建成功")
            asrOfflineRecognizer = com.k2fsa.sherpa.onnx.OfflineRecognizer(assets, recognizerConfig)
            Log.i(TAG_NET, "离线识别器初始化成功")

            Log.i(TAG_NET, "本地ASR组件初始化成功")

            // 初始化完成后在主线程启动唤醒词监听
            runOnUiThread {
                Toast.makeText(this, "系统已就绪，随时可以提问。", Toast.LENGTH_SHORT).show()
                startLocalKws()
            }

        } catch (e: Exception) {
            Log.e(TAG_NET, "初始化本地ASR组件失败: ${e.message}", e)
            runOnUiThread {
                Toast.makeText(this, "本地ASR初始化失败: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * 流式处理SSE响应 - 实时处理每个SSE事件
     */
    private fun processStreamingSseResponse(response: Response) {
        Log.i(TAG_NET, "========== 开始流式SSE处理 ==========")

        try {
            response.body?.source()?.let { source ->
                var hasProcessedFirstEvent = false

                while (!source.exhausted()) {
                    val line = source.readUtf8Line()
                    if (line == null) break

                    Log.d(TAG_NET, "读取SSE行: '$line'")

                    if (line.startsWith("data: ")) {
                        val jsonStr = line.substring(6) // 移除"data: "前缀
                        Log.i(TAG_NET, "收到SSE数据: $jsonStr")

                        try {
                            val sseMessage = objectMapper.readValue(jsonStr, SseMessage::class.java)
                            Log.i(TAG_NET, "✓ 解析SSE消息成功: event=${sseMessage.event}, greeting=${sseMessage.greeting}")

                            // 立即处理SSE消息
                            handleSseMessage(sseMessage)

                            // 如果是第一个有效事件，标记已开始处理
                            if (!hasProcessedFirstEvent && (sseMessage.event != null || sseMessage.greeting == true)) {
                                hasProcessedFirstEvent = true
                                Log.i(TAG_NET, "✓ 开始流式处理 - 收到第一个有效事件")
                            }

                        } catch (e: Exception) {
                            Log.e(TAG_NET, "解析SSE消息失败: $jsonStr", e)
                        }
                    } else if (line.isNotBlank()) {
                        Log.d(TAG_NET, "非data行: '$line'")
                    }
                }

                Log.i(TAG_NET, "✓ 流式SSE处理完成")

            } ?: run {
                Log.e(TAG_NET, "响应体为空，无法进行流式处理")
            }

        } catch (e: Exception) {
            Log.e(TAG_NET, "流式SSE处理失败: ${e.message}", e)
        } finally {
            // 确保重置状态
            runOnUiThread {
                if (isProcessingRequest) {
                    isProcessingRequest = false
                    binding.btnRecord.isEnabled = true
                    updateRecordButton("正在录音")
                }
            }
        }

        Log.i(TAG_NET, "========== 流式SSE处理结束 ==========")
    }

    /**
     * 流式处理LLM SSE响应 - 实时处理每个LLM SSE事件
     */
    private fun processStreamingLlmSseResponse(response: Response) {
        Log.i(TAG_NET, "========== 开始流式LLM SSE处理 ==========")

        // 🔧 标记SSE流开始活跃
        isLlmStreamActive = true
        Log.i(TAG_NET, "✓ SSE流状态设置为活跃")

        try {
            response.body?.source()?.let { source ->
                var hasProcessedFirstEvent = false

                while (!source.exhausted()) {
                    val line = source.readUtf8Line()
                    if (line == null) break

                    Log.d(TAG_NET, "读取LLM SSE行: '$line'")

                    if (line.startsWith("data: ")) {
                        val jsonStr = line.substring(6) // 移除"data: "前缀
                        Log.i(TAG_NET, "收到LLM SSE数据: $jsonStr")

                        try {
                            val sseMessage = objectMapper.readValue(jsonStr, SseMessage::class.java)
                            Log.i(TAG_NET, "✓ 解析LLM SSE消息成功: event=${sseMessage.event}, greeting=${sseMessage.greeting}")

                            // 立即处理LLM SSE消息
                            handleLlmSseMessage(sseMessage)

                            // 如果是第一个有效事件，标记已开始处理
                            if (!hasProcessedFirstEvent && (sseMessage.greeting == true || sseMessage.event != null)) {
                                hasProcessedFirstEvent = true
                                Log.i(TAG_NET, "✓ 开始LLM流式处理 - 收到第一个有效事件")

                                // 如果是greeting事件，立即开始处理
                                if (sseMessage.greeting == true) {
                                    Log.i(TAG_NET, "✓ 收到greeting事件，立即开始处理")
                                }
                            }

                        } catch (e: Exception) {
                            Log.e(TAG_NET, "解析LLM SSE消息失败: $jsonStr", e)
                        }
                    } else if (line.isNotBlank()) {
                        Log.d(TAG_NET, "非data行: '$line'")
                    }
                }

                Log.i(TAG_NET, "✓ 流式LLM SSE处理完成")

            } ?: run {
                Log.e(TAG_NET, "LLM响应体为空，无法进行流式处理")
            }

        } catch (e: Exception) {
            Log.e(TAG_NET, "流式LLM SSE处理失败: ${e.message}", e)
        } finally {
            // 🔧 SSE流结束，重置活跃标志
            isLlmStreamActive = false
            Log.i(TAG_NET, "✓ SSE流状态设置为非活跃")

            // 检查是否需要启动返回开场白定时器
            runOnUiThread {
                val audioQueueEmpty = audioPlayQueue.isEmpty()
                val notPlayingAudio = !isPlayingLlmAudio

                if (audioQueueEmpty && notPlayingAudio) {
                    Log.i(TAG_NET, "✓ SSE流结束且音频播放完毕，显示友好提示并启动返回开场白定时器")

                    // 🔧 新增：SSE流结束且音频播放完毕时显示友好提示
                    val hintType = if (isManualInterrupt) "manual_interrupt" else "playback_complete"
                    showFriendlyHint(hintType)

                    // 🔧 统一启动返回开场白定时器（移除手动打断的限制）
                    startReturnToGreetingTimer()
                } else {
                    Log.i(TAG_NET, "⚠ SSE流结束但音频仍在播放，等待音频播放完毕")
                }
            }

            // SSE流结束，确保文本已显示（备用逻辑）
            if (completeLlmAnswer.isNotEmpty()) {
                Log.i(TAG_NET, "✓ SSE流结束，确保文本已显示，长度: ${completeLlmAnswer.length}")

                // 保存完整文本内容，避免并发清空问题
                val finalTextContent = completeLlmAnswer.toString()

                runOnUiThread {
                    // 如果当前显示的内容长度小于完整内容，则更新显示
                    if (currentLlmAnswer.length < finalTextContent.length) {
                        currentLlmAnswer.clear()
                        currentLlmAnswer.append(finalTextContent)
                        displayMarkdownAnswer(currentLlmAnswer.toString())
                        Log.i(TAG_NET, "✓ 更新显示完整文本内容，长度: ${currentLlmAnswer.length}")
                    } else {
                        Log.i(TAG_NET, "✓ 文本已完整显示，无需更新，当前长度: ${currentLlmAnswer.length}")
                    }

                    // 在UI线程中清空完整文本缓存，确保显示操作完成后再清空
                    completeLlmAnswer.clear()
                    Log.i(TAG_NET, "✓ 完整文本缓存已清空，为下次对话做准备")
                }
            }

            // 确保重置LLM处理状态
            isProcessingLlmRequest = false
            currentSseCall = null  // 清空SSE连接引用
            Log.i(TAG_NET, "✓ LLM SSE连接已清理")
        }

        Log.i(TAG_NET, "========== 流式LLM SSE处理结束 ==========")
    }

    /**
     * 更新唤醒词提示 - 现在更新开场白标题
     */
    private fun updateWakeWordHint() {
        val currentWakeWord = getCurrentWakeWord()
        val hintText = "💬 说出 \"$currentWakeWord\" 开始对话"
        binding.tvOpeningTitle.text = hintText
        Log.i(TAG_NET, "开场白标题已更新为唤醒词提示: $hintText")
    }

    /**
     * 显示唤醒词提示 - 现在只更新开场白标题
     */
    private fun showWakeWordHint() {
        updateWakeWordHint()
        Log.i(TAG_NET, "唤醒词提示已集成到开场白标题")
    }

    /**
     * 隐藏唤醒词提示 - 现在是空操作，因为已集成到开场白
     */
    private fun hideWakeWordHint() {
        Log.i(TAG_NET, "唤醒词提示已集成到开场白，无需单独隐藏")
    }

    /**
     * 添加问题到历史记录
     */
    private fun addQuestionToHistory(question: String) {
        if (question.isBlank()) return

        // 清理问题文本（去除多余空格、换行等）
        val cleanQuestion = question.trim().replace(Regex("\\s+"), " ")
        if (cleanQuestion.isEmpty()) return

        // 如果问题已存在，先移除再添加（这样会更新到最新位置）
        if (historyQuestions.contains(cleanQuestion)) {
            historyQuestions.remove(cleanQuestion)
            Log.i(TAG_NET, "问题已存在，移除旧记录: $cleanQuestion")
        }

        // 添加到历史记录末尾
        historyQuestions.add(cleanQuestion)

        // 如果超过最大数量，移除最旧的（LinkedHashSet的第一个元素）
        while (historyQuestions.size > maxHistoryQuestions) {
            val oldestQuestion = historyQuestions.first()
            historyQuestions.remove(oldestQuestion)
            Log.i(TAG_NET, "移除最旧的问题: $oldestQuestion")
        }

        Log.i(TAG_NET, "添加问题到历史记录: $cleanQuestion，当前历史问题数量: ${historyQuestions.size}")
        Log.i(TAG_NET, "当前所有历史问题: ${historyQuestions.joinToString(", ")}")
    }

    /**
     * 显示历史问题标签云 - 3行左右滚动
     */
    private fun showHistoryQuestions() {
        runOnUiThread {
            try {
                // 停止之前的滚动动画
                stopScrollAnimations()

                // 清空之前的标签（无论是否有新问题都要清空）
                binding.llHistoryRow1.removeAllViews()
                binding.llHistoryRow2.removeAllViews()
                binding.llHistoryRow3.removeAllViews()

                // 如果没有历史问题，隐藏容器并返回
                if (historyQuestions.isEmpty()) {
                    binding.cvHistoryQuestions.visibility = android.view.View.GONE
                    Log.i(TAG_NET, "历史问题为空，已清空标签并隐藏容器")
                    return@runOnUiThread
                }

                // 将问题分配到3行：1→4→7, 2→5→8, 3→6→9...
                val questionsList = historyQuestions.toList()
                val rows = arrayOf(
                    binding.llHistoryRow1,
                    binding.llHistoryRow2,
                    binding.llHistoryRow3
                )

                questionsList.forEachIndexed { index, question ->
                    val rowIndex = index % 3
                    val tagButton = createHistoryQuestionTag(question)
                    rows[rowIndex].addView(tagButton)
                }

                // 只有当启用标签复制且问题数量较多时才复制标签
                // 可通过 enableTagDuplication 开关控制是否启用复制功能
                if (enableTagDuplication && historyQuestions.size >= 9) {
                    duplicateTagsForSeamlessScroll()
                    Log.i(TAG_NET, "✓ 已启用标签复制以实现循环滚动")
                } else {
                    Log.i(TAG_NET, "✓ 标签复制已禁用或问题数量不足，使用普通滚动")
                }

                // 显示标签云
                binding.cvHistoryQuestions.visibility = android.view.View.VISIBLE

                // 启动滚动动画
                startScrollAnimations()

                // 设置触摸暂停功能
                setupTouchPauseForScrollViews()

                Log.i(TAG_NET, "✓ 历史问题标签云显示成功（3行滚动）")

            } catch (e: Exception) {
                Log.e(TAG_NET, "显示历史问题失败: ${e.message}", e)
            }
        }
    }

    /**
     * 创建历史问题标签按钮
     */
    private fun createHistoryQuestionTag(question: String): Button {
        // 随机颜色背景数组（不包含白色）
        val colorBackgrounds = arrayOf(
            R.drawable.bg_tag_color_1, // 红色
            R.drawable.bg_tag_color_2, // 青色
            R.drawable.bg_tag_color_3, // 蓝色
            R.drawable.bg_tag_color_4, // 绿色
            R.drawable.bg_tag_color_5, // 黄色
            R.drawable.bg_tag_color_6, // 粉色
            R.drawable.bg_tag_color_7, // 天蓝色
            R.drawable.bg_tag_color_8  // 紫色
        )

        return Button(this).apply {
            text = question
            textSize = 10f  // 减小字体大小：12f -> 10f
            setTextColor(android.graphics.Color.WHITE) // 白色文字在彩色背景上更清晰

            // 随机选择背景颜色
            val randomBackground = colorBackgrounds.random()
            background = ContextCompat.getDrawable(this@CallActivity, randomBackground)

            // 设置更小的内边距：减小高度和宽度
            setPadding(12, 4, 12, 4)  // 左右：16->12，上下：8->4

            // 设置固定的小尺寸（转换为像素）
            val heightInPx = (32 * resources.displayMetrics.density).toInt()  // 32dp转换为像素
            val layoutParams = android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT,
                heightInPx  // 固定高度32dp，让按钮更紧凑
            )
            layoutParams.setMargins(4, 2, 4, 2)  // 减小外边距
            this.layoutParams = layoutParams

            // 设置最小高度，确保按钮不会太高
            minHeight = 0
            minimumHeight = heightInPx

            // 设置点击事件
            setOnClickListener {
                Log.i(TAG_NET, "========== 用户点击历史问题 ==========")
                Log.i(TAG_NET, "点击的历史问题: $question")
                Log.i(TAG_NET, "点击前历史问题: ${historyQuestions.joinToString(", ")}")

                // 隐藏欢迎内容（包括历史问题标签云）
                hideWelcomeContent()

                // 清空之前的AI回答显示
                clearAiAnswerDisplay()

                // 设置当前会话问题，避免SSE返回时重复添加到历史记录
                currentSessionQuestion = question
                Log.i(TAG_NET, "✓ 设置当前会话问题: $currentSessionQuestion")

                // 显示用户问题在标题栏（但不重复添加到历史记录）
                binding.tvConversationTitle.text = question
                binding.cvConversation.visibility = android.view.View.VISIBLE

                // 发送问题到LLM（历史问题不重置会话，避免重复添加）
                sendAsrResultToLlm(question, resetSession = false, requestType = "history_click")
            }

            // 设置阴影效果
            elevation = 4f
        }
    }

    /**
     * 为每行复制标签以实现无缝循环滚动
     * 优化：只在行内容不足以填满滚动区域时才复制
     */
    private fun duplicateTagsForSeamlessScroll() {
        val rows = arrayOf(
            binding.llHistoryRow1,
            binding.llHistoryRow2,
            binding.llHistoryRow3
        )

        rows.forEach { row ->
            val originalChildCount = row.childCount
            if (originalChildCount > 0) {
                // 测量行的总宽度
                row.measure(
                    android.view.View.MeasureSpec.makeMeasureSpec(0, android.view.View.MeasureSpec.UNSPECIFIED),
                    android.view.View.MeasureSpec.makeMeasureSpec(0, android.view.View.MeasureSpec.UNSPECIFIED)
                )
                val rowWidth = row.measuredWidth
                val screenWidth = resources.displayMetrics.widthPixels

                // 只有当行内容宽度小于屏幕宽度的1.5倍时才复制（确保有足够内容滚动）
                if (rowWidth < screenWidth * 1.5) {
                    Log.i(TAG_NET, "行内容不足，复制标签以实现循环滚动 - 行宽度: $rowWidth, 屏幕宽度: $screenWidth")
                    // 复制一遍所有标签，实现无缝循环
                    for (i in 0 until originalChildCount) {
                        val originalTag = row.getChildAt(i) as Button
                        val duplicateTag = createHistoryQuestionTag(originalTag.text.toString())
                        row.addView(duplicateTag)
                    }
                } else {
                    Log.i(TAG_NET, "行内容充足，无需复制标签 - 行宽度: $rowWidth, 屏幕宽度: $screenWidth")
                }
            }
        }
    }

    /**
     * 启动滚动动画
     */
    private fun startScrollAnimations() {
        val scrollViews = arrayOf(
            binding.hsvHistoryRow1,
            binding.hsvHistoryRow2,
            binding.hsvHistoryRow3
        )

        scrollViews.forEachIndexed { index, scrollView ->
            val speed = scrollSpeeds[index]
            val duration = scrollDurations[index]

            val animator = ValueAnimator.ofFloat(0f, 1f).apply {
                this.duration = duration
                repeatCount = ValueAnimator.INFINITE

                addUpdateListener { animation ->
                    val progress = animation.animatedValue as Float
                    val childView = scrollView.getChildAt(0)
                    val maxScrollX = childView.width - scrollView.width

                    if (maxScrollX > 0) {
                        // 根据是否启用标签复制来决定滚动方式
                        val scrollX = if (!enableTagDuplication || historyQuestions.size < 9) {
                            // 来回滚动：0 -> max -> 0 (适用于未启用复制或问题较少的情况)
                            val pingPongProgress = if (progress < 0.5f) {
                                progress * 2f
                            } else {
                                2f - progress * 2f
                            }
                            (pingPongProgress * maxScrollX).toInt()
                        } else {
                            // 循环滚动 (适用于启用复制且问题较多的情况)
                            if (speed > 0) {
                                // 向左滚动
                                (progress * maxScrollX).toInt()
                            } else {
                                // 向右滚动
                                (maxScrollX * (1f - progress)).toInt()
                            }
                        }
                        // 历史问题滚动动画是系统行为，不应触发用户滚动检测
                        // 但这是水平滚动，不会影响垂直滚动监听器，所以不需要设置标志
                        scrollView.scrollTo(scrollX, 0)
                    }
                }

                start()
            }

            scrollAnimators.add(animator)
        }

        Log.i(TAG_NET, "✓ 启动3行滚动动画")
    }

    /**
     * 设置触摸暂停功能
     */
    private fun setupTouchPauseForScrollViews() {
        val scrollViews = arrayOf(
            binding.hsvHistoryRow1,
            binding.hsvHistoryRow2,
            binding.hsvHistoryRow3
        )

        scrollViews.forEach { scrollView ->
            scrollView.setOnTouchListener { _, event ->
                when (event.action) {
                    android.view.MotionEvent.ACTION_DOWN -> {
                        // 用户开始触摸，暂停滚动
                        pauseScrollAnimations()
                    }
                    android.view.MotionEvent.ACTION_UP, android.view.MotionEvent.ACTION_CANCEL -> {
                        // 用户松开触摸，恢复滚动
                        resumeScrollAnimations()
                    }
                }
                false // 不消费事件，让子View可以处理点击
            }
        }
    }

    /**
     * 暂停滚动动画
     */
    private fun pauseScrollAnimations() {
        scrollAnimators.forEach { animator ->
            if (animator.isRunning) {
                animator.pause()
            }
        }
    }

    /**
     * 恢复滚动动画
     */
    private fun resumeScrollAnimations() {
        scrollAnimators.forEach { animator ->
            if (animator.isPaused) {
                animator.resume()
            }
        }
    }

    /**
     * 停止滚动动画
     */
    private fun stopScrollAnimations() {
        scrollAnimators.forEach { animator ->
            animator.cancel()
        }
        scrollAnimators.clear()
        Log.i(TAG_NET, "✓ 停止滚动动画")
    }

    /**
     * 隐藏历史问题容器
     */
    private fun hideHistoryQuestions() {
        stopScrollAnimations()
        binding.cvHistoryQuestions.visibility = android.view.View.GONE
    }

    /**
     * 更新历史问题显示
     * 用于在获取缓存问题后刷新UI显示
     */
    private fun updateHistoryQuestionsDisplay() {
        Log.i(TAG_NET, "更新历史问题显示，当前问题数量: ${historyQuestions.size}")
        showHistoryQuestions()
    }

    /**
     * 获取缓存的历史问题
     * 调用 /v2/get-cached-questions 接口获取用户之前提问过的问题
     */
    private fun getCachedQuestions() {
        Log.i(TAG_NET, "开始获取缓存问题...")

        // 检查服务器地址
        if (ttsBaseUrl.isEmpty()) {
            Log.e(TAG_NET, "服务器地址为空，无法获取缓存问题")
            runOnUiThread {
                Toast.makeText(this@CallActivity, "服务器地址未配置", Toast.LENGTH_SHORT).show()
            }
            return
        }

        // 构建缓存问题接口URL
        val cachedQuestionsUrl = ApiConfig.buildUrl(ttsBaseUrl, ApiConfig.LLM.GET_CACHED_QUESTIONS_ENDPOINT)
        Log.i(TAG_NET, "缓存问题URL: $cachedQuestionsUrl")

        // 获取请求参数
        val currentApiKey = sharedPrefs.getString(SettingsActivity.KEY_APIKEY, "") ?: ""
        val userId = sharedPrefs.getString(SettingsActivity.KEY_USER_ID, SettingsActivity.DEFAULT_USER_ID) ?: SettingsActivity.DEFAULT_USER_ID

        // 根据模型类别确定reference_id
        val modelCategory = sharedPrefs.getString(SettingsActivity.KEY_SELECTED_MODEL_CATEGORY, "male") ?: "male"
        val referenceId = when (modelCategory) {
            "female" -> "woman"
            "male" -> "man"
            else -> {
                Log.w(TAG_NET, "未知的模型类别: $modelCategory, 使用默认值 man")
                "man" // 默认为男声
            }
        }

        Log.i(TAG_NET, "缓存问题请求参数:")
        Log.i(TAG_NET, "  dify_api_key: $currentApiKey")
        Log.i(TAG_NET, "  user_id: $userId")
        Log.i(TAG_NET, "  reference_id: $referenceId")

        if (currentApiKey.isEmpty()) {
            Log.e(TAG_NET, "API Key为空，无法获取缓存问题")
            runOnUiThread {
                Toast.makeText(this@CallActivity, "API Key为空，请在设置中选择应用", Toast.LENGTH_SHORT).show()
            }
            return
        }

        // 构建请求
        val request = Request.Builder()
            .url(cachedQuestionsUrl)
            .get()
            .addHeader("dify_api_key", currentApiKey)
            .addHeader("reference_id", referenceId)
            .addHeader("user_id", userId)
            .addHeader("User-Agent", "Android App")
            .addHeader("Accept", "*/*")
            .addHeader("Connection", "keep-alive")
            .build()

        // 打印请求详情
        Log.i(TAG_NET, "========== 缓存问题请求详情 ==========")
        Log.i(TAG_NET, "请求方法: ${request.method}")
        Log.i(TAG_NET, "请求URL: ${request.url}")
        request.headers.forEach { (name, value) ->
            Log.i(TAG_NET, "请求头: $name = $value")
        }
        Log.i(TAG_NET, "====================================")

        // 发送请求
        try {
            okHttpClient.newCall(request).enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    Log.e(TAG_NET, "缓存问题请求失败: ${e.message}", e)
                    runOnUiThread {
                        Toast.makeText(this@CallActivity, "获取历史问题失败: ${e.message}", Toast.LENGTH_SHORT).show()
                    }
                }

                override fun onResponse(call: Call, response: Response) {
                    try {
                        Log.i(TAG_NET, "========== 缓存问题响应详情 ==========")
                        Log.i(TAG_NET, "响应状态码: ${response.code}")
                        Log.i(TAG_NET, "响应消息: ${response.message}")
                        response.headers.forEach { (name, value) ->
                            Log.i(TAG_NET, "响应头: $name = $value")
                        }

                        if (!response.isSuccessful) {
                            Log.e(TAG_NET, "缓存问题请求失败，状态码: ${response.code}")
                            runOnUiThread {
                                Toast.makeText(this@CallActivity, "获取历史问题失败，服务器返回错误: ${response.code}", Toast.LENGTH_SHORT).show()
                            }
                            return
                        }

                        val responseStr = response.body?.string()
                        Log.i(TAG_NET, "缓存问题响应数据: $responseStr")
                        Log.i(TAG_NET, "====================================")

                        if (responseStr != null && responseStr.isNotEmpty()) {
                            // 解析JSON响应
                            val objectMapper = com.fasterxml.jackson.databind.ObjectMapper()
                            val cachedResponse = objectMapper.readValue(responseStr, CachedQuestionsResponse::class.java)

                            Log.i(TAG_NET, "解析缓存问题响应:")
                            Log.i(TAG_NET, "  success: ${cachedResponse.success}")
                            Log.i(TAG_NET, "  count: ${cachedResponse.count}")
                            Log.i(TAG_NET, "  cache_key: ${cachedResponse.cacheKey}")
                            Log.i(TAG_NET, "  questions: ${cachedResponse.questions}")

                            if (cachedResponse.success && cachedResponse.questions.isNotEmpty()) {
                                runOnUiThread {
                                    // 清空当前历史问题
                                    historyQuestions.clear()

                                    // 添加缓存的问题到历史记录
                                    cachedResponse.questions.forEach { question ->
                                        if (question.isNotBlank()) {
                                            historyQuestions.add(question.trim())
                                        }
                                    }

                                    Log.i(TAG_NET, "✓ 成功加载 ${historyQuestions.size} 个缓存问题")

                                    // 更新UI显示历史问题
                                    updateHistoryQuestionsDisplay()

                                    // Toast.makeText(this@CallActivity, "已加载 ${historyQuestions.size} 个历史问题", Toast.LENGTH_SHORT).show()
                                }
                            } else {
                                Log.i(TAG_NET, "没有找到缓存的问题或请求失败")
                                runOnUiThread {
                                    Toast.makeText(this@CallActivity, "暂无历史问题", Toast.LENGTH_SHORT).show()
                                }
                            }
                        } else {
                            Log.e(TAG_NET, "缓存问题响应体为空")
                            runOnUiThread {
                                Toast.makeText(this@CallActivity, "服务器返回空数据", Toast.LENGTH_SHORT).show()
                            }
                        }

                    } catch (e: Exception) {
                        Log.e(TAG_NET, "处理缓存问题响应失败: ${e.message}", e)
                        runOnUiThread {
                            Toast.makeText(this@CallActivity, "处理响应失败: ${e.message}", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            })
        } catch (e: Exception) {
            Log.e(TAG_NET, "发送缓存问题请求失败: ${e.message}", e)
            runOnUiThread {
                Toast.makeText(this@CallActivity, "发送请求失败: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * 应用动态布局设置
     */
    private fun applyDynamicLayouts() {
        try {
            Log.i(TAG_NET, "开始应用动态布局设置")

            // 检查是否有自定义布局设置
            if (!layoutManager.hasCustomLayout()) {
                Log.i(TAG_NET, "没有自定义布局设置，使用默认布局")
                return
            }

            // 应用开场白卡片位置
            val openingStatementPosition = layoutManager.getLayoutPosition(LayoutManager.LayoutType.OPENING_STATEMENT)
            updateGuidelinePosition("guideline_opening_statement", openingStatementPosition)
            Log.i(TAG_NET, "✓ 开场白卡片位置: ${(openingStatementPosition * 100).toInt()}%")

            // 应用开场白问题位置
            val greetingQuestionsPosition = layoutManager.getLayoutPosition(LayoutManager.LayoutType.GREETING_QUESTIONS)
            updateGuidelinePosition("guideline_greeting_questions", greetingQuestionsPosition)
            Log.i(TAG_NET, "✓ 开场白问题位置: ${(greetingQuestionsPosition * 100).toInt()}%")

            // 应用推荐问题宽度
            val greetingQuestionsWidth = layoutManager.getLayoutPosition(LayoutManager.LayoutType.GREETING_QUESTIONS_WIDTH)
            updateSuggestedQuestionsWidth(greetingQuestionsWidth)
            Log.i(TAG_NET, "✓ 推荐问题宽度: ${(greetingQuestionsWidth * 100).toInt()}%")

            // 应用对话框位置和高度
            val conversationDialogPosition = layoutManager.getLayoutPosition(LayoutManager.LayoutType.CONVERSATION_DIALOG)
            val conversationHeight = layoutManager.getLayoutPosition(LayoutManager.LayoutType.CONVERSATION_HEIGHT)
            updateGuidelinePosition("guideline_user_question", conversationDialogPosition)
            updateGuidelinePosition("guideline_ai_answer_end", conversationDialogPosition + conversationHeight)
            Log.i(TAG_NET, "✓ 对话框位置: ${(conversationDialogPosition * 100).toInt()}% - ${((conversationDialogPosition + conversationHeight) * 100).toInt()}% (高度: ${(conversationHeight * 100).toInt()}%)")

            // 应用底部推荐问题位置
            val bottomSuggestionsPosition = layoutManager.getLayoutPosition(LayoutManager.LayoutType.BOTTOM_SUGGESTIONS)
            updateGuidelinePosition("guideline_bottom_suggestions", bottomSuggestionsPosition)
            Log.i(TAG_NET, "✓ 底部推荐问题位置: ${(bottomSuggestionsPosition * 100).toInt()}%")

            // 应用录音按钮位置
            val recordButtonPosition = layoutManager.getLayoutPosition(LayoutManager.LayoutType.RECORD_BUTTON)
            updateGuidelinePosition("guideline_record_button", recordButtonPosition)
            Log.i(TAG_NET, "✓ 录音按钮位置: ${(recordButtonPosition * 100).toInt()}%")

            // 应用打断提示条位置
            val interruptHintPosition = layoutManager.getLayoutPosition(LayoutManager.LayoutType.INTERRUPT_HINT)
            updateGuidelinePosition("guideline_interrupt_hint", interruptHintPosition)
            Log.i(TAG_NET, "✓ 打断提示条位置: ${(interruptHintPosition * 100).toInt()}%")

            Log.i(TAG_NET, "动态布局设置应用完成")

            // 如果是从布局调整界面进入的，显示提示
            if (intent.getBooleanExtra("layoutAdjusted", false)) {
                runOnUiThread {
                    Toast.makeText(this, "自定义布局已应用", Toast.LENGTH_SHORT).show()
                }
                // 清除标记，避免重复提示
                intent.removeExtra("layoutAdjusted")
            }

        } catch (e: Exception) {
            Log.e(TAG_NET, "应用动态布局失败: ${e.message}", e)
        }
    }

    /**
     * 更新指定Guideline的位置
     */
    private fun updateGuidelinePosition(guidelineId: String, percentage: Float) {
        try {
            val resourceId = resources.getIdentifier(guidelineId, "id", packageName)
            if (resourceId != 0) {
                val guideline = findViewById<androidx.constraintlayout.widget.Guideline>(resourceId)
                guideline?.setGuidelinePercent(percentage)
                Log.d(TAG_NET, "更新Guideline $guidelineId 位置为 ${(percentage * 100).toInt()}%")
            } else {
                Log.w(TAG_NET, "未找到Guideline: $guidelineId")
            }
        } catch (e: Exception) {
            Log.e(TAG_NET, "更新Guideline位置失败: $guidelineId, ${e.message}", e)
        }
    }

    /**
     * 更新推荐问题区域的宽度
     */
    private fun updateSuggestedQuestionsWidth(widthPercentage: Float) {
        try {
            val suggestedQuestionsContainer = findViewById<androidx.core.widget.NestedScrollView>(R.id.suggested_questions_container)
            suggestedQuestionsContainer?.let {
                val layoutParams = it.layoutParams as androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
                layoutParams.matchConstraintPercentWidth = widthPercentage
                it.layoutParams = layoutParams
                Log.i(TAG_NET, "✓ 更新推荐问题宽度为 ${(widthPercentage * 100).toInt()}%")
            }
        } catch (e: Exception) {
            Log.e(TAG_NET, "更新推荐问题宽度失败", e)
        }
    }

    /**
     * 显示打断提示条（播放中可打断的提示）
     */
    private fun showInterruptHint() {
        Log.i(TAG_NET, "显示播放中可打断的提示")
        // 🔧 使用新的友好提示机制，显示"您可以说出唤醒词进行打断"
        showFriendlyHint("playing")
    }

    /**
     * 隐藏打断提示条
     */
    private fun hideInterruptHint() {
        // 防止重复隐藏，避免不必要的操作
        if (!isInterruptHintVisible) {
            Log.d(TAG_NET, "打断提示条已经隐藏，跳过重复操作")
            return
        }

        Log.i(TAG_NET, "隐藏打断提示条")
        runOnUiThread {
            try {
                // 添加淡出动画
                binding.cvInterruptHint.animate()
                    .alpha(0f)
                    .setDuration(300)
                    .withEndAction {
                        binding.cvInterruptHint.visibility = android.view.View.GONE
                        isInterruptHintVisible = false  // 更新状态
                    }
                    .start()

                Log.i(TAG_NET, "✓ 打断提示条已隐藏")
            } catch (e: Exception) {
                Log.e(TAG_NET, "隐藏打断提示条失败: ${e.message}", e)
            }
        }
    }

    /**
     * 🔧 显示友好提示（支持不同场景的差异化文案）
     * @param hintType 提示类型：manual_interrupt(手动打断), playback_complete(播放完成), playing(播放中)
     */
    private fun showFriendlyHint(hintType: String = "playback_complete") {
        Log.i(TAG_NET, "显示${hintType}场景的友好提示")

        runOnUiThread {
            try {
                val currentWakeWord = getCurrentWakeWord()

                // 根据不同场景使用差异化文案
                val friendlyText = when (hintType) {
                    "manual_interrupt" -> "已打断，说出\"$currentWakeWord\"开始对话"
                    "playing" -> "请点击\"红色按钮\"进行打断"
                    "playback_complete" -> "请说出\"$currentWakeWord\"开始新的对话"
                    else -> "请说出\"$currentWakeWord\"开始对话"
                }

                // 创建SpannableString来设置关键词的颜色
                val spannableText = android.text.SpannableString(friendlyText)

                if (hintType == "playing") {
                    // 播放中场景：设置"红色按钮"为红色
                    val redButtonText = "\"红色按钮\""
                    val startIndex = friendlyText.indexOf(redButtonText)
                    if (startIndex != -1) {
                        val endIndex = startIndex + redButtonText.length

                        // 设置"红色按钮"为红色
                        spannableText.setSpan(
                            android.text.style.ForegroundColorSpan(android.graphics.Color.parseColor("#F44336")),
                            startIndex,
                            endIndex,
                            android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                        )

                        // 设置"红色按钮"为粗体
                        spannableText.setSpan(
                            android.text.style.StyleSpan(android.graphics.Typeface.BOLD),
                            startIndex,
                            endIndex,
                            android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                    }
                } else {
                    // 其他场景：设置唤醒词颜色
                    val startIndex = friendlyText.indexOf("\"$currentWakeWord\"")
                    if (startIndex != -1) {
                        val endIndex = startIndex + "\"$currentWakeWord\"".length

                        // 根据场景设置不同的颜色
                        val color = when (hintType) {
                            "manual_interrupt" -> "#4CAF50"  // 绿色（友好色彩，已打断场景）
                            else -> "#FF9800"  // 橙色（默认场景）
                        }

                        spannableText.setSpan(
                            android.text.style.ForegroundColorSpan(android.graphics.Color.parseColor(color)),
                            startIndex,
                            endIndex,
                            android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                        )

                        // 设置唤醒词为粗体
                        spannableText.setSpan(
                            android.text.style.StyleSpan(android.graphics.Typeface.BOLD),
                            startIndex,
                            endIndex,
                            android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                    }
                }

                // 更新提示条文本
                binding.tvInterruptHint.text = spannableText

                // 显示提示条（确保每次都能正确显示和应用样式）
                binding.cvInterruptHint.visibility = android.view.View.VISIBLE

                // 如果是首次显示，添加淡入动画
                if (!isInterruptHintVisible) {
                    isInterruptHintVisible = true
                    binding.cvInterruptHint.alpha = 0f
                    binding.cvInterruptHint.animate()
                        .alpha(1f)
                        .setDuration(300)
                        .start()
                } else {
                    // 如果已经显示，确保完全可见
                    binding.cvInterruptHint.alpha = 1f
                }

                Log.i(TAG_NET, "✓ ${hintType}友好提示已显示: $friendlyText")
            } catch (e: Exception) {
                Log.e(TAG_NET, "显示${hintType}友好提示失败: ${e.message}", e)
            }
        }
    }

    /**
     * 🔧 显示手动打断后的友好提示（保持向后兼容）
     */
    private fun showManualInterruptFriendlyHint() {
        showFriendlyHint("manual_interrupt")
    }

    /**
     * 更新打断提示条文本，显示具体的唤醒词并设置橙色
     */
    private fun updateInterruptHintText() {
        try {
            val currentWakeWord = getCurrentWakeWord()
            // val hintText = "您可以说出\"$currentWakeWord\"或点击红色按钮进行打断"
            val hintText = "您可以点击\"红色按钮\"进行打断"

            // 创建SpannableString来设置"红色按钮"的颜色
            val spannableText = android.text.SpannableString(hintText)

            // 找到"红色按钮"的位置（包含双引号）
            val redButtonText = "\"红色按钮\""
            val startIndex = hintText.indexOf(redButtonText)
            if (startIndex != -1) {
                val endIndex = startIndex + redButtonText.length

                // 设置"红色按钮"为红色
                spannableText.setSpan(
                    android.text.style.ForegroundColorSpan(android.graphics.Color.parseColor("#F44336")),
                    startIndex,
                    endIndex,
                    android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )

                // 设置"红色按钮"为粗体
                spannableText.setSpan(
                    android.text.style.StyleSpan(android.graphics.Typeface.BOLD),
                    startIndex,
                    endIndex,
                    android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }

            binding.tvInterruptHint.text = spannableText
            Log.i(TAG_NET, "✓ 打断提示文本已更新: $hintText")
        } catch (e: Exception) {
            Log.e(TAG_NET, "更新打断提示文本失败: ${e.message}", e)
            // 如果出错，使用简单文本
            binding.tvInterruptHint.text = "您可以点击\"红色按钮\"进行打断"
        }
    }

    /**
     * 初始化音频波浪可视化组件
     */
    private fun initAudioWaveView() {
        try {
            audioWaveView = binding.audioWaveView
            Log.i(TAG_NET, "✓ 音频波浪可视化组件初始化完成")
        } catch (e: Exception) {
            Log.e(TAG_NET, "音频波浪可视化组件初始化失败: ${e.message}", e)
        }
    }

    /**
     * 更新音频波浪的音量数据
     */
    private fun updateAudioWaveVolume(volume: Float) {
        try {
            if (::audioWaveView.isInitialized) {
                audioWaveView.setVolume(volume)
            }
        } catch (e: Exception) {
            Log.e(TAG_NET, "更新音频波浪音量失败: ${e.message}", e)
        }
    }

    /**
     * 设置音频波浪的激活状态
     */
    private fun setAudioWaveActive(active: Boolean) {
        try {
            if (::audioWaveView.isInitialized) {
                audioWaveView.setActive(active)
                Log.i(TAG_NET, "音频波浪激活状态: $active")
            }
        } catch (e: Exception) {
            Log.e(TAG_NET, "设置音频波浪激活状态失败: ${e.message}", e)
        }
    }

    /**
     * 设置音频波浪的录音状态
     */
    private fun setAudioWaveRecording(recording: Boolean) {
        try {
            if (::audioWaveView.isInitialized) {
                audioWaveView.setRecording(recording)
                Log.i(TAG_NET, "音频波浪录音状态: $recording")
            }
        } catch (e: Exception) {
            Log.e(TAG_NET, "设置音频波浪录音状态失败: ${e.message}", e)
        }
    }

    /**
     * 设置音频波浪的播放状态
     */
    private fun setAudioWavePlaying(playing: Boolean) {
        try {
            if (::audioWaveView.isInitialized) {
                audioWaveView.setPlaying(playing)
                Log.i(TAG_NET, "音频波浪播放状态: $playing")
            }
        } catch (e: Exception) {
            Log.e(TAG_NET, "设置音频波浪播放状态失败: ${e.message}", e)
        }
    }

    /**
     * 暂停音频波浪动画
     */
    private fun pauseAudioWave() {
        try {
            if (::audioWaveView.isInitialized) {
                audioWaveView.pauseAnimation()
            }
        } catch (e: Exception) {
            Log.e(TAG_NET, "暂停音频波浪动画失败: ${e.message}", e)
        }
    }

    /**
     * 恢复音频波浪动画
     */
    private fun resumeAudioWave() {
        try {
            if (::audioWaveView.isInitialized) {
                audioWaveView.resumeAnimation()
            }
        } catch (e: Exception) {
            Log.e(TAG_NET, "恢复音频波浪动画失败: ${e.message}", e)
        }
    }

    /**
     * 计算音频音量 (0.0 - 1.0)
     */
    private fun calculateVolume(audioData: ShortArray, length: Int): Float {
        if (length <= 0) return 0f

        var sum = 0.0
        for (i in 0 until length) {
            sum += (audioData[i] * audioData[i]).toDouble()
        }

        val rms = kotlin.math.sqrt(sum / length)
        val volume = (rms / 32768.0).toFloat() // 归一化到0-1

        // 应用一些增益和限制
        return (volume * 3f).coerceIn(0f, 1f)
    }

    /**
     * 检查ASR系统是否完全就绪
     */
    private fun isAsrSystemReady(): Boolean {
        return try {
            // 检查基本组件
            if (asrOfflineRecognizer == null) {
                Log.w(TAG_NET, "ASR系统检查失败: asrOfflineRecognizer为空")
                return false
            }

            if (asrVad == null) {
                Log.w(TAG_NET, "ASR系统检查失败: asrVad为空")
                return false
            }

            // 检查VAD状态
            try {
                // 尝试创建一个测试流来验证VAD是否正常
                val testConfig = asrVad?.config
                if (testConfig == null) {
                    Log.w(TAG_NET, "ASR系统检查失败: VAD配置为空")
                    return false
                }
            } catch (e: Exception) {
                Log.w(TAG_NET, "ASR系统检查失败: VAD状态异常 - ${e.message}")
                return false
            }

            // 检查是否正在录音
            if (isAsrRecording) {
                Log.w(TAG_NET, "ASR系统检查失败: 正在录音中")
                return false
            }

            Log.i(TAG_NET, "✓ ASR系统状态检查通过")
            true
        } catch (e: Exception) {
            Log.e(TAG_NET, "ASR系统状态检查异常: ${e.message}", e)
            false
        }
    }

    /**
     * 重新初始化ASR系统
     */
    private fun reinitializeAsrSystem(): Boolean {
        Log.i(TAG_NET, "========== 重新初始化ASR系统 ==========")

        return try {
            // 清理现有资源
            cleanupAsrRecording()
            asrOfflineRecognizer?.release()
            asrVad?.release()
            asrOfflineRecognizer = null
            asrVad = null

            // 重新初始化
            initLocalAsrComponents()

            // 等待初始化完成
            var retryCount = 0
            while (retryCount < 10 && (asrOfflineRecognizer == null || asrVad == null)) {
                Thread.sleep(500)
                retryCount++
                Log.i(TAG_NET, "等待ASR初始化完成... ($retryCount/10)")
            }

            val success = isAsrSystemReady()
            if (success) {
                Log.i(TAG_NET, "✓ ASR系统重新初始化成功")
            } else {
                Log.e(TAG_NET, "✗ ASR系统重新初始化失败")
            }

            success
        } catch (e: Exception) {
            Log.e(TAG_NET, "ASR系统重新初始化异常: ${e.message}", e)
            false
        }
    }

    /**
     * ASR系统诊断工具
     */
    private fun diagnoseAsrSystem(): String {
        val diagnosis = StringBuilder()
        diagnosis.appendLine("========== ASR系统诊断报告 ==========")

        // 检查基本组件
        diagnosis.appendLine("1. 基本组件状态:")
        diagnosis.appendLine("   - ASR识别器: ${if (asrOfflineRecognizer != null) "✓ 已初始化" else "✗ 未初始化"}")
        diagnosis.appendLine("   - VAD检测器: ${if (asrVad != null) "✓ 已初始化" else "✗ 未初始化"}")
        diagnosis.appendLine("   - 录音状态: ${if (isAsrRecording) "正在录音" else "空闲"}")

        // 检查权限
        diagnosis.appendLine("\n2. 权限检查:")
        val hasRecordPermission = ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED
        diagnosis.appendLine("   - 录音权限: ${if (hasRecordPermission) "✓ 已授权" else "✗ 未授权"}")

        // 检查音频设备
        diagnosis.appendLine("\n3. 音频设备检查:")
        try {
            val testBufferSize = android.media.AudioRecord.getMinBufferSize(
                16000,
                android.media.AudioFormat.CHANNEL_IN_MONO,
                android.media.AudioFormat.ENCODING_PCM_16BIT
            )
            diagnosis.appendLine("   - 最小缓冲区大小: $testBufferSize bytes")
            diagnosis.appendLine("   - 缓冲区状态: ${if (testBufferSize > 0) "✓ 正常" else "✗ 异常"}")
        } catch (e: Exception) {
            diagnosis.appendLine("   - 音频设备: ✗ 检查失败 - ${e.message}")
        }

        // 检查模型文件
        diagnosis.appendLine("\n4. 模型文件检查:")
        try {
            val modelExists = File("$modelDir/tokens.txt").exists()
            diagnosis.appendLine("   - 模型目录: $modelDir")
            diagnosis.appendLine("   - 模型文件: ${if (modelExists) "✓ 存在" else "✗ 缺失"}")
        } catch (e: Exception) {
            diagnosis.appendLine("   - 模型文件: ✗ 检查失败 - ${e.message}")
        }

        // 系统状态
        diagnosis.appendLine("\n5. 系统状态:")
        diagnosis.appendLine("   - 整体状态: ${if (isAsrSystemReady()) "✓ 就绪" else "✗ 未就绪"}")

        diagnosis.appendLine("=====================================")

        val report = diagnosis.toString()
        Log.i(TAG_NET, report)
        return report
    }

    /**
     * Logo菜单展开/收起功能
     * 实现平滑的动画效果，符合响应式设计原则
     */
    private fun toggleMenu() {
        try {
            val menuContainer = binding.llMenuContainer
            
            if (isMenuExpanded) {
                // 收起菜单 - 淡出动画
                menuContainer.animate()
                    .alpha(0f)
                    .scaleX(0.8f)
                    .scaleY(0.8f)
                    .setDuration(200)
                    .withEndAction {
                        menuContainer.visibility = View.GONE
                        isMenuExpanded = false
                        Log.i(TAG_NET, "✓ 菜单已收起")
                    }
                    .start()
            } else {
                // 展开菜单 - 淡入动画
                menuContainer.visibility = View.VISIBLE
                menuContainer.alpha = 0f
                menuContainer.scaleX = 0.8f
                menuContainer.scaleY = 0.8f
                
                menuContainer.animate()
                    .alpha(1f)
                    .scaleX(1f)
                    .scaleY(1f)
                    .setDuration(200)
                    .withEndAction {
                        isMenuExpanded = true
                        Log.i(TAG_NET, "✓ 菜单已展开")
                    }
                    .start()
            }
        } catch (e: Exception) {
            Log.e(TAG_NET, "菜单切换失败: ${e.message}", e)
        }
    }

    // ==================== 智能滚动恢复系统 ====================

    /**
     * 设置智能滚动监听器
     * 检测用户手动滚动行为，实现智能恢复机制
     */
    private fun setupIntelligentScrollListener() {
        try {
            val scrollView = binding.cvConversation.findViewById<androidx.core.widget.NestedScrollView>(R.id.nested_scroll_view_conversation)

            scrollView?.setOnScrollChangeListener { _, _, scrollY, _, oldScrollY ->
                onUserScrollDetected(scrollY, oldScrollY)
                // 同时调用新的滚动检测系统
                onUserScrollDetected()
            }

            scrollView?.setOnTouchListener { _, event ->
                when (event.action) {
                    android.view.MotionEvent.ACTION_DOWN -> {
                        onUserScrollStarted()
                    }
                    android.view.MotionEvent.ACTION_UP, android.view.MotionEvent.ACTION_CANCEL -> {
                        onUserScrollEnded()
                    }
                }
                false // 不消费事件，让滚动正常工作
            }

            Log.i(TAG_NET, "✓ 智能滚动监听器设置完成")
        } catch (e: Exception) {
            Log.e(TAG_NET, "设置智能滚动监听器失败: ${e.message}", e)
        }
    }

    /**
     * 用户开始滚动
     */
    private fun onUserScrollStarted() {
        scrollStartTime = System.currentTimeMillis()
        isUserScrolling = true

        // 取消所有待执行的恢复任务
        handler.removeCallbacks(resetUserScrollingRunnable)

        Log.i(TAG_NET, "用户开始手动滚动")
    }

    /**
     * 检测到用户滚动变化
     */
    private fun onUserScrollDetected(scrollY: Int, oldScrollY: Int) {
        if (!isUserScrolling) {
            onUserScrollStarted()
        }

        // 计算滚动距离
        scrollDistance += kotlin.math.abs(scrollY - oldScrollY)
        lastScrollY = scrollY
        userLastScrollTime = System.currentTimeMillis()

        Log.d(TAG_NET, "用户滚动中，当前位置: $scrollY, 累积距离: $scrollDistance")
    }

    /**
     * 用户结束滚动
     */
    private fun onUserScrollEnded() {
        Log.i(TAG_NET, "用户结束手动滚动")

        // 分析滚动行为
        val scrollBehavior = analyzeScrollBehavior()

        // 根据行为类型安排恢复
        scheduleIntelligentRecovery(scrollBehavior)
    }

    /**
     * 分析用户滚动行为类型
     */
    private fun analyzeScrollBehavior(): ScrollBehaviorType {
        val scrollDuration = System.currentTimeMillis() - scrollStartTime
        val avgVelocity = if (scrollDuration > 0) scrollDistance / scrollDuration.toFloat() else 0f

        return when {
            // 意外触碰：时间短、距离小
            scrollDuration < 200 && scrollDistance < 100 -> {
                Log.i(TAG_NET, "滚动行为分析: 意外触碰 (时长:${scrollDuration}ms, 距离:${scrollDistance}px)")
                ScrollBehaviorType.ACCIDENTAL
            }

            // 快速浏览：速度快、距离大
            avgVelocity > 1.0f && scrollDistance > 500 -> {
                Log.i(TAG_NET, "滚动行为分析: 快速浏览 (速度:${avgVelocity}px/ms, 距离:${scrollDistance}px)")
                ScrollBehaviorType.QUICK_BROWSE
            }

            // 深度阅读：时间长
            scrollDuration > 2000 -> {
                Log.i(TAG_NET, "滚动行为分析: 深度阅读 (时长:${scrollDuration}ms)")
                ScrollBehaviorType.DELIBERATE_READING
            }

            // 位置调整：其他情况
            else -> {
                Log.i(TAG_NET, "滚动行为分析: 位置调整 (时长:${scrollDuration}ms, 距离:${scrollDistance}px)")
                ScrollBehaviorType.POSITION_ADJUST
            }
        }
    }

    /**
     * 根据滚动行为安排智能恢复
     */
    private fun scheduleIntelligentRecovery(behaviorType: ScrollBehaviorType) {
        // 清除之前的恢复任务
        handler.removeCallbacks(resetUserScrollingRunnable)

        val recoveryDelay = when (behaviorType) {
            ScrollBehaviorType.ACCIDENTAL -> 500L        // 0.5秒快速恢复
            ScrollBehaviorType.POSITION_ADJUST -> 1500L  // 1.5秒适中恢复
            ScrollBehaviorType.QUICK_BROWSE -> 3000L     // 3秒延迟恢复
            ScrollBehaviorType.DELIBERATE_READING -> 5000L // 5秒谨慎恢复
        }

        Log.i(TAG_NET, "安排智能恢复: ${behaviorType}, 延迟: ${recoveryDelay}ms")

        // 安排恢复任务
        handler.postDelayed(resetUserScrollingRunnable, recoveryDelay)
    }

    /**
     * 重置用户滚动状态的Runnable
     */
    private val resetUserScrollingRunnable = Runnable {
        Log.i(TAG_NET, "执行智能恢复: 重置用户滚动状态")

        isUserScrolling = false
        scrollDistance = 0

        // 同时调用新系统的滚动停止处理
        onUserScrollStopped()

        // 如果有待恢复的自动功能，现在可以恢复了
        executeIntelligentRecovery()
    }

    /**
     * 执行智能恢复
     */
    private fun executeIntelligentRecovery() {
        Log.i(TAG_NET, "开始执行智能恢复")

        try {
            // 智能恢复逻辑已简化，移除了文本高亮和滚动功能
            Log.i(TAG_NET, "✓ 智能恢复执行完成")
        } catch (e: Exception) {
            Log.e(TAG_NET, "智能恢复执行失败: ${e.message}", e)
        }
    }

    /**
     * 智能滚动执行器
     * 根据用户当前状态和优先级决定是否执行滚动操作
     */
    private fun executeSmartScroll(priority: RecoveryPriority, scrollAction: () -> Unit) {
        when {
            // 用户正在滚动：根据优先级决定处理方式
            isUserScrolling -> {
                when (priority) {
                    RecoveryPriority.CRITICAL -> {
                        // 关键优先级：延迟执行，等用户滚动结束
                        Log.i(TAG_NET, "用户正在滚动，延迟执行关键滚动操作")
                        handler.postDelayed({
                            if (!isUserScrolling) {
                                scrollAction()
                                Log.i(TAG_NET, "✓ 延迟执行关键滚动操作成功")
                            } else {
                                Log.i(TAG_NET, "用户仍在滚动，跳过延迟的关键滚动操作")
                            }
                        }, 2000)
                    }
                    RecoveryPriority.IMPORTANT -> {
                        // 重要优先级：较长延迟
                        Log.i(TAG_NET, "用户正在滚动，延迟执行重要滚动操作")
                        handler.postDelayed({
                            if (!isUserScrolling) scrollAction()
                        }, 3000)
                    }
                    RecoveryPriority.NORMAL -> {
                        // 普通优先级：直接跳过
                        Log.i(TAG_NET, "用户正在滚动，跳过普通优先级滚动操作")
                    }
                }
            }

            // 用户刚刚滚动过：根据时间间隔决定
            System.currentTimeMillis() - userLastScrollTime < USER_SCROLL_TIMEOUT -> {
                when (priority) {
                    RecoveryPriority.CRITICAL -> {
                        // 关键操作：短暂延迟后执行
                        Log.i(TAG_NET, "用户刚刚滚动过，短暂延迟执行关键滚动操作")
                        handler.postDelayed(scrollAction, 1000)
                    }
                    else -> {
                        Log.i(TAG_NET, "用户刚刚滚动过，跳过非关键滚动操作")
                    }
                }
            }

            // 正常情况：直接执行
            else -> {
                Log.i(TAG_NET, "执行${priority.description}滚动操作")
                scrollAction()
            }
        }
    }

    /**
     * 测试开场白问题位置调整功能
     */
    private fun testGreetingQuestionsPosition() {
        Log.i(TAG_NET, "🧪 开始测试开场白问题位置调整功能")

        runOnUiThread {
            try {
                // 确保推荐问题容器可见
                if (binding.suggestedQuestionsContainer.visibility != android.view.View.VISIBLE) {
                    // 如果容器不可见，先显示一些测试问题
                    val testQuestions = listOf("测试问题1", "测试问题2", "测试问题3")
                    showSuggestedQuestions(testQuestions)
                }

                // 手动设置一个测试值
                val testPosition = 0.5f // 50%
                Log.i(TAG_NET, "🧪 测试设置开场白问题位置为50%")

                updateGuidelinePosition("guideline_greeting_questions", testPosition)

                android.widget.Toast.makeText(this, "✅ 测试完成：开场白问题位置已设置为50%", android.widget.Toast.LENGTH_LONG).show()

                // 3秒后恢复原始位置
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    if (::layoutManager.isInitialized) {
                        val originalPosition = layoutManager.getLayoutPosition(LayoutManager.LayoutType.GREETING_QUESTIONS)
                        updateGuidelinePosition("guideline_greeting_questions", originalPosition)
                        android.widget.Toast.makeText(this, "已恢复原始位置: ${(originalPosition * 100).toInt()}%", android.widget.Toast.LENGTH_SHORT).show()
                    }
                }, 3000)

            } catch (e: Exception) {
                Log.e(TAG_NET, "测试开场白问题位置失败: ${e.message}", e)
                android.widget.Toast.makeText(this, "测试失败: ${e.message}", android.widget.Toast.LENGTH_LONG).show()
            }
        }
    }

    /**
     * 启动返回开场白定时器
     */
    private fun startReturnToGreetingTimer() {
        // 如果用户正在滚动，不启动定时器
        if (isUserScrolling) {
            Log.i(TAG_NET, "用户正在滚动，暂不启动返回开场白定时器")
            return
        }

        // 取消之前的定时器
        cancelReturnToGreetingTimer()

        // 从设置中获取返回开场白的时间（秒）
        val returnTimeSeconds = sharedPrefs.getInt(SettingsActivity.KEY_RETURN_TO_GREETING_TIME, SettingsActivity.DEFAULT_RETURN_TO_GREETING_TIME)
        val returnTimeMillis = returnTimeSeconds * 1000L

        Log.i(TAG_NET, "启动返回开场白定时器，延迟时间: ${returnTimeSeconds}秒")

        returnToGreetingHandler = android.os.Handler(android.os.Looper.getMainLooper())
        returnToGreetingRunnable = Runnable {
            Log.i(TAG_NET, "返回开场白定时器触发，开始返回开场白界面")
            returnToGreetingInterface()
        }

        returnToGreetingHandler?.postDelayed(returnToGreetingRunnable!!, returnTimeMillis)
    }

    /**
     * 取消返回开场白定时器
     */
    private fun cancelReturnToGreetingTimer() {
        returnToGreetingRunnable?.let { runnable ->
            returnToGreetingHandler?.removeCallbacks(runnable)
            Log.i(TAG_NET, "已取消返回开场白定时器")
        }
        returnToGreetingHandler = null
        returnToGreetingRunnable = null
    }

    /**
     * 返回开场白界面
     */
    private fun returnToGreetingInterface() {
        Log.i(TAG_NET, "========== 返回开场白界面 ==========")

        runOnUiThread {
            try {
                // 清空当前对话内容
                clearAiAnswerDisplay()

                // 隐藏对话相关UI
                binding.cvConversation.visibility = android.view.View.GONE
                binding.llLlmSuggestionsContainer.visibility = android.view.View.GONE

                // 显示开场白相关UI
                binding.cvOpeningStatement.visibility = android.view.View.VISIBLE
                binding.suggestedQuestionsContainer.visibility = android.view.View.VISIBLE
                binding.cvHistoryQuestions.visibility = android.view.View.VISIBLE

                // 🔧 隐藏打断提示条（返回开场白界面时不需要显示）
                hideInterruptHint()

                // 显示唤醒词提示
                showWakeWordHint()

                // 重置请求状态
                currentRequestType = "voice_input"
                isFirstInteraction = true

                Log.i(TAG_NET, "✓ 成功返回开场白界面")

            } catch (e: Exception) {
                Log.e(TAG_NET, "返回开场白界面失败: ${e.message}", e)
            }
        }
    }

    /**
     * 媒体项点击处理
     */
    private fun onMediaItemClicked(mediaItem: MediaDisplayItem) {
        Log.i(TAG_NET, "========== 用户点击媒体项 ==========")
        Log.i(TAG_NET, "媒体类型: ${mediaItem.type}, 标题: ${mediaItem.title}")

        // 检查当前音频播放状态
        val isCurrentlyPlaying = isPlayingLlmAudio
        val hasQueuedAudio = audioPlayQueue.isNotEmpty()

        Log.i(TAG_NET, "当前音频状态 - 正在播放: $isCurrentlyPlaying, 队列中音频: ${audioPlayQueue.size}")

        if (isCurrentlyPlaying || hasQueuedAudio) {
            // 记录音频被打断的状态
            audioInterruptedByMedia = true
            wasPlayingWhenInterrupted = isCurrentlyPlaying

            // 保存当前音频队列
            interruptedAudioQueue.clear()
            interruptedAudioQueue.addAll(audioPlayQueue)

            Log.i(TAG_NET, "✓ 音频被媒体查看打断，已保存状态")
            Log.i(TAG_NET, "  - 正在播放: $wasPlayingWhenInterrupted")
            Log.i(TAG_NET, "  - 保存的队列: ${interruptedAudioQueue.size} 项")

            // 停止当前音频播放
            if (isCurrentlyPlaying) {
                stopCurrentAudio()
            }
        }

        // 取消返回开场白定时器（用户开始查看媒体内容）
        cancelReturnToGreetingTimer()
        Log.i(TAG_NET, "✓ 已取消返回开场白定时器（用户开始查看媒体）")
    }

    /**
     * 检查并重新启动返回开场白定时器
     * 当用户从媒体查看器返回时调用
     */
    private fun checkAndRestartReturnTimer() {
        Log.i(TAG_NET, "========== 检查并重新启动返回定时器 ==========")

        // 检查是否有被打断的音频需要恢复
        if (audioInterruptedByMedia && interruptedAudioQueue.isNotEmpty()) {
            Log.i(TAG_NET, "检测到被媒体打断的音频，准备恢复播放")
            Log.i(TAG_NET, "恢复音频队列: ${interruptedAudioQueue.size} 项")

            // 恢复音频队列
            audioPlayQueue.clear()
            audioPlayQueue.addAll(interruptedAudioQueue)

            // 重置打断状态
            audioInterruptedByMedia = false
            interruptedAudioQueue.clear()

            // 恢复音频播放
            if (audioPlayQueue.isNotEmpty()) {
                Log.i(TAG_NET, "✓ 开始恢复播放被打断的音频")
                Log.i(TAG_NET, "⚠ 不启动定时器，等待所有音频播放完毕后自然启动")
                playNextInQueue()
                return // 🔧 关键修复：不启动定时器，让音频播放完毕后自然启动
            }
        }

        // 只有在没有恢复音频的情况下，才检查是否启动定时器
        val audioQueueEmpty = audioPlayQueue.isEmpty()
        val notPlayingAudio = !isPlayingLlmAudio

        Log.i(TAG_NET, "音频队列为空: $audioQueueEmpty")
        Log.i(TAG_NET, "未在播放音频: $notPlayingAudio")

        if (audioQueueEmpty && notPlayingAudio) {
            Log.i(TAG_NET, "✓ 音频播放已完成，重新启动返回开场白定时器")
            startReturnToGreetingTimer()
        } else {
            Log.i(TAG_NET, "⚠ 音频仍在播放或队列中，暂不启动定时器")
        }
    }

    /**
     * 用户滚动检测处理
     */
    private fun onUserScrollDetected() {
        if (!isUserScrolling) {
            Log.i(TAG_NET, "检测到用户开始滚动，取消返回开场白定时器")
            isUserScrolling = true

            // 取消当前的返回开场白定时器
            cancelReturnToGreetingTimer()
        }

        // 取消之前的滚动停止检测
        scrollInteractionRunnable?.let { runnable ->
            scrollInteractionHandler?.removeCallbacks(runnable)
        }

        // 设置新的滚动停止检测（2秒后认为用户停止滚动）
        scrollInteractionHandler = android.os.Handler(android.os.Looper.getMainLooper())
        scrollInteractionRunnable = Runnable {
            onUserScrollStopped()
        }
        scrollInteractionHandler?.postDelayed(scrollInteractionRunnable!!, 2000)
    }

    /**
     * 用户停止滚动处理
     */
    private fun onUserScrollStopped() {
        Log.i(TAG_NET, "用户停止滚动，检查是否需要重新启动返回开场白定时器")

        // 只有在还在滚动状态时才重置（避免重复设置）
        if (isUserScrolling) {
            isUserScrolling = false
        }

        // 🔧 关键修复：如果音频被媒体打断，不启动定时器
        if (audioInterruptedByMedia) {
            Log.i(TAG_NET, "⚠ 音频被媒体打断中，不启动定时器，等待用户返回后恢复")
            return
        }

        // 检查音频播放状态，决定是否重新启动定时器
        val audioQueueEmpty = audioPlayQueue.isEmpty()
        val notPlayingAudio = !isPlayingLlmAudio

        if (audioQueueEmpty && notPlayingAudio) {
            Log.i(TAG_NET, "✓ 音频播放已完成且用户停止滚动，重新启动返回开场白定时器")
            startReturnToGreetingTimer()
        } else {
            Log.i(TAG_NET, "⚠ 音频仍在播放，暂不启动定时器")
        }
    }

    /**
     * 取消滚动交互检测
     */
    private fun cancelScrollInteractionDetection() {
        scrollInteractionRunnable?.let { runnable ->
            scrollInteractionHandler?.removeCallbacks(runnable)
        }
        scrollInteractionHandler = null
        scrollInteractionRunnable = null
        isUserScrolling = false
    }

    /**
     * 停止当前音频播放
     */
    private fun stopCurrentAudio() {
        try {
            val stopResult = duix?.stopAudio()
            Log.i(TAG_NET, "停止当前音频播放结果: $stopResult")

            // 清空音频队列
            audioPlayQueue.clear()

            // 重置播放状态
            isPlayingLlmAudio = false

        } catch (e: Exception) {
            Log.e(TAG_NET, "停止当前音频播放失败: ${e.message}", e)
        }
    }

    /**
     * 设置响应式内边距（基于15dp的百分比计算）
     */
    private fun setupResponsivePadding() {
        try {
            // 获取屏幕高度
            val displayMetrics = resources.displayMetrics
            val screenHeight = displayMetrics.heightPixels

            // 计算基于15dp的百分比内边距
            // 15dp在标准密度下约为15px，相对于1920px高度约为0.78%
            val paddingPercent = 0.0078f // 0.78%
            val paddingPx = (screenHeight * paddingPercent).toInt()

            // 设置最小内边距，确保在小屏幕上也有合理的显示
            val minPaddingPx = (12 * displayMetrics.density).toInt() // 12dp最小值
            val finalPaddingPx = maxOf(paddingPx, minPaddingPx)

            // 应用到打断提示条容器
            binding.llInterruptHintContainer.setPadding(
                finalPaddingPx, finalPaddingPx, finalPaddingPx, finalPaddingPx
            )

            Log.i(TAG_NET, "✓ 打断提示条响应式内边距设置完成: ${finalPaddingPx}px (屏幕高度: ${screenHeight}px)")
        } catch (e: Exception) {
            Log.e(TAG_NET, "设置响应式内边距失败: ${e.message}", e)
            // 失败时使用默认内边距
            val defaultPadding = (16 * resources.displayMetrics.density).toInt()
            binding.llInterruptHintContainer.setPadding(defaultPadding, defaultPadding, defaultPadding, defaultPadding)
        }
    }

}
