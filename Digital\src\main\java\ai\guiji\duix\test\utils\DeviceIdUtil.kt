package ai.guiji.duix.test.utils

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.provider.Settings
import android.telephony.TelephonyManager
import java.security.MessageDigest
import java.util.*

object DeviceIdUtil {
    private const val PREFS_FILE = "device_id.xml"
    private const val PREFS_DEVICE_ID = "device_id"

    @SuppressLint("HardwareIds")
    fun getDeviceId(context: Context): String {
        // 首先尝试从SharedPreferences获取持久化的设备ID
        val prefs = context.getSharedPreferences(PREFS_FILE, Context.MODE_PRIVATE)
        var deviceId = prefs.getString(PREFS_DEVICE_ID, null)
        
        if (deviceId == null) {
            // 生成新的设备ID
            deviceId = generateDeviceId(context)
            
            // 保存到SharedPreferences
            prefs.edit().putString(PREFS_DEVICE_ID, deviceId).apply()
        }
        
        return deviceId
    }

    @SuppressLint("HardwareIds")
    private fun generateDeviceId(context: Context): String {
        val deviceIdentifiers = mutableListOf<String>()

        // 1. ANDROID_ID
        val androidId = Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
        deviceIdentifiers.add(androidId ?: "")

        // 2. BUILD 信息
        deviceIdentifiers.add(Build.BOARD)
        deviceIdentifiers.add(Build.BRAND)
        deviceIdentifiers.add(Build.DEVICE)
        deviceIdentifiers.add(Build.HARDWARE)
        deviceIdentifiers.add(Build.MANUFACTURER)
        deviceIdentifiers.add(Build.MODEL)
        deviceIdentifiers.add(Build.PRODUCT)
        deviceIdentifiers.add(Build.SERIAL)

        // 3. 随机UUID
        deviceIdentifiers.add(UUID.randomUUID().toString())

        // 组合所有标识符并生成MD5
        return generateMD5(deviceIdentifiers.joinToString("|"))
    }

    private fun generateMD5(input: String): String {
        try {
            val md = MessageDigest.getInstance("MD5")
            val digest = md.digest(input.toByteArray())
            return digest.joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            e.printStackTrace()
            // 如果MD5生成失败，返回一个随机UUID
            return UUID.randomUUID().toString()
        }
    }
}
