<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape>
            <corners android:radius="16dp" />
            <gradient
                android:startColor="#DD1E3A8A"
                android:endColor="#DD374151"
                android:angle="135" />
            <stroke 
                android:width="2dp" 
                android:color="#80FFFFFF" />
        </shape>
    </item>
    <item>
        <shape>
            <corners android:radius="16dp" />
            <gradient
                android:startColor="#CC1E3A8A"
                android:endColor="#CC374151"
                android:angle="135" />
            <stroke 
                android:width="2dp" 
                android:color="#60FFFFFF" />
        </shape>
    </item>
</selector>
