<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 大屏幕（平板）适配 - 增大间距和尺寸 -->
    
    <!-- 基础间距 - 在大屏幕上增大 -->
    <dimen name="spacing_tiny">6dp</dimen>
    <dimen name="spacing_small">12dp</dimen>
    <dimen name="spacing_medium">16dp</dimen>
    <dimen name="spacing_large">24dp</dimen>
    <dimen name="spacing_xlarge">32dp</dimen>
    <dimen name="spacing_xxlarge">48dp</dimen>

    <!-- 图标尺寸 - 在大屏幕上增大 -->
    <dimen name="icon_size_small">20dp</dimen>
    <dimen name="icon_size_medium">32dp</dimen>
    <dimen name="icon_size_large">56dp</dimen>
    <dimen name="icon_size_xlarge">64dp</dimen>

    <!-- 文字尺寸 - 在大屏幕上略微增大 -->
    <dimen name="text_size_caption">14sp</dimen>
    <dimen name="text_size_body">16sp</dimen>
    <dimen name="text_size_subtitle">18sp</dimen>
    <dimen name="text_size_title">24sp</dimen>
    <dimen name="text_size_headline">28sp</dimen>
    <dimen name="text_size_button">16sp</dimen>

    <!-- 卡片相关 - 在大屏幕上增大 -->
    <dimen name="card_corner_radius">16dp</dimen>
    <dimen name="card_elevation">4dp</dimen>
    <dimen name="card_padding">24dp</dimen>
    <dimen name="card_margin">24dp</dimen>

    <!-- 按钮相关 - 在大屏幕上增大 -->
    <dimen name="button_height_small">44dp</dimen>
    <dimen name="button_height_medium">56dp</dimen>
    <dimen name="button_height_large">64dp</dimen>
    <dimen name="button_corner_radius">12dp</dimen>

    <!-- 输入框相关 -->
    <dimen name="input_height">56dp</dimen>
    <dimen name="input_corner_radius">12dp</dimen>

    <!-- 进度条 -->
    <dimen name="progress_bar_height">8dp</dimen>

    <!-- 最小触摸目标尺寸 -->
    <dimen name="min_touch_target">48dp</dimen>

    <!-- 头部区域 -->
    <dimen name="header_height">88dp</dimen>

    <!-- 录音按钮底部间距 - 大屏幕上增大间距 -->
    <dimen name="record_button_bottom_margin">@dimen/spacing_xxlarge</dimen>
</resources>
