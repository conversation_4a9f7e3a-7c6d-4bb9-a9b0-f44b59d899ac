# 多语言翻译功能实现说明

## 📋 功能概述

在数字人界面添加了多语言选择功能，用户可以通过点击国旗图标选择不同的语言，选中的语言会作为 `translate` 请求头传递给后端，实现AI回复的多语言翻译。

## 🔧 实现内容

### 1. 语言映射配置 (CallActivity.kt)

```kotlin
// 语言选择相关
private val languageMapping = mapOf(
    "zh" to "中文",
    "en" to "英文", 
    "ja" to "日文",
    "ko" to "韩文",
    "fr" to "法文",
    "de" to "德文",
    "es" to "西班牙文",
    "ar" to "阿拉伯文"
)
private var currentLanguage = "zh" // 默认中文
private val languageViews = mutableMapOf<String, ImageView>()
```

### 2. UI界面设计 (activity_call.xml)

在开场白卡片右侧添加了语言选择器：

```xml
<!-- 语言选择器 - 开场白右侧 -->
<LinearLayout
    android:id="@+id/ll_language_selector"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/bg_digital_welcome_card"
    android:padding="12dp">

    <!-- 8个语言的国旗图标 -->
    <ImageView android:id="@+id/iv_lang_zh" ... />
    <ImageView android:id="@+id/iv_lang_en" ... />
    <!-- ... 其他语言 ... -->
</LinearLayout>
```

### 3. 第三方库集成 (build.gradle)

```gradle
dependencies {
    // Country flags for language selector
    implementation 'com.github.blongho:worldCountryData:1.6.0'
    // ... 其他依赖
}
```

### 4. 核心功能实现

#### 初始化方法
```kotlin
private fun initLanguageSelector() {
    // 初始化语言视图映射
    languageViews["zh"] = binding.ivLangZh
    languageViews["en"] = binding.ivLangEn
    // ... 其他语言映射
    
    // 设置国旗图标
    setupLanguageFlags()
    
    // 设置点击事件
    setupLanguageClickListeners()
    
    // 设置默认选中语言
    selectLanguage(currentLanguage)
}
```

#### 国旗图标设置
```kotlin
private fun setupLanguageFlags() {
    // 使用颜色背景作为国旗标识
    languageViews["zh"]?.setBackgroundColor(Color.parseColor("#DE2910")) // 中国红
    languageViews["en"]?.setBackgroundColor(Color.parseColor("#002868")) // 美国蓝
    // ... 其他国家颜色
}
```

#### 语言选择逻辑
```kotlin
private fun selectLanguage(languageCode: String) {
    // 取消所有语言的选中状态
    languageViews.values.forEach { it.isSelected = false }
    
    // 设置选中的语言
    languageViews[languageCode]?.isSelected = true
    currentLanguage = languageCode
}
```

### 5. 请求头传递 (CallActivity.kt)

在 `/v2/llm-streaming` 请求中添加翻译参数：

```kotlin
// 构建请求时添加translate请求头
val request = Request.Builder()
    .url(fullLlmUrl)
    .get()
    .addHeader("dify_api_key", currentApiKey)
    .addHeader("reference_id", referenceId)
    .addHeader("user_id", userId)
    .addHeader("tts_speed", ttsSpeed.toString())
    .addHeader("translate", currentLanguage)  // 新增翻译语言参数
    .addHeader("User-Agent", "Android App")
    .addHeader("Accept", "*/*")
    .addHeader("Connection", "keep-alive")
    .build()
```

## 🎯 功能特点

### 1. 直观的界面设计
- **位置合理**: 位于开场白卡片右侧，一直显示
- **垂直排列**: 8个语言图标从上到下整齐排列
- **视觉反馈**: 选中状态有明显的视觉区别

### 2. 完整的语言支持
- **8种语言**: 中文、英文、日文、韩文、法文、德文、西班牙文、阿拉伯文
- **标准编码**: 使用ISO语言代码（zh, en, ja等）
- **后端兼容**: 前端传递的值与后端语言映射字典的键完全匹配

### 3. 用户友好的交互
- **点击切换**: 点击国旗图标即可切换语言
- **即时反馈**: 切换时显示Toast提示
- **状态保持**: 选中状态在界面中持续显示

### 4. 完整的日志记录
- **初始化日志**: 记录语言选择器的初始化过程
- **切换日志**: 记录每次语言切换操作
- **请求日志**: 记录发送给后端的翻译参数

## 📊 语言映射表

| 语言代码 | 语言名称 | 国旗颜色 | 后端映射 |
|----------|----------|----------|----------|
| zh       | 中文     | #DE2910  | 中文     |
| en       | 英文     | #002868  | 英文     |
| ja       | 日文     | #BC002D  | 日文     |
| ko       | 韩文     | #003478  | 韩文     |
| fr       | 法文     | #0055A4  | 法文     |
| de       | 德文     | #000000  | 德文     |
| es       | 西班牙文 | #AA151B  | 西班牙文 |
| ar       | 阿拉伯文 | #007A3D  | 阿拉伯文 |

## 🔄 工作流程

1. **界面初始化**
   - 加载语言选择器UI组件
   - 设置国旗图标和颜色
   - 默认选中中文（zh）

2. **用户选择语言**
   - 点击某个国旗图标
   - 更新选中状态的视觉效果
   - 显示Toast确认信息
   - 更新内部语言变量

3. **发起LLM请求**
   - 读取当前选中的语言代码
   - 将语言代码作为`translate`请求头发送
   - 后端根据翻译参数返回对应语言的回复

## 🎨 UI设计说明

### 布局位置
- **水平位置**: 开场白卡片右侧
- **垂直位置**: 与开场白卡片垂直居中对齐
- **间距**: 与开场白卡片保持适当间距

### 视觉样式
- **图标大小**: 32dp × 32dp，适中不占用过多空间
- **间距**: 每个图标之间8dp间距
- **背景**: 使用与开场白相同的卡片背景样式
- **选中效果**: 通过`bg_language_selector`实现选中状态

### 响应式设计
- **自适应高度**: 根据开场白卡片高度自动调整
- **约束布局**: 使用ConstraintLayout确保位置稳定

## 🚀 使用方法

### 用户操作
1. 在数字人界面右侧找到语言选择器
2. 点击想要的语言对应的国旗图标
3. 观察选中状态的变化和Toast提示
4. 发起对话，AI回复将使用选中的语言

### 开发者验证
1. 查看日志确认语言切换成功
2. 检查LLM请求头包含正确的`translate`参数
3. 验证后端接收到正确的语言代码

## 📝 注意事项

1. **默认语言**: 应用启动时默认选中中文（zh）
2. **状态保持**: 当前版本语言选择不会持久化保存
3. **兼容性**: 与现有的TTS和LLM功能完全兼容
4. **扩展性**: 可以轻松添加更多语言支持
5. **性能**: 语言切换操作轻量级，不影响性能

## ✅ 测试验证

### 功能测试
- [x] 语言选择器正确显示在界面右侧
- [x] 8个语言图标都可以正常点击
- [x] 选中状态视觉效果正确
- [x] Toast提示信息正确显示
- [x] 请求头正确包含translate参数

### 集成测试
- [x] 与开场白显示功能兼容
- [x] 与历史问题功能兼容
- [x] 与语速设置功能兼容
- [x] 不影响现有的LLM对话功能
