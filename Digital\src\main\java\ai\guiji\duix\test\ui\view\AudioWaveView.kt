package ai.guiji.duix.test.ui.view

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import android.view.animation.LinearInterpolator
import kotlin.math.*

/**
 * 波浪形音频可视化组件
 * 根据音频音量实时显示波浪动画效果
 */
class AudioWaveView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 波浪参数
    private var waveAmplitude = 0f // 波浪振幅，默认为0（静音状态）
    private var waveFrequency = 0.02f // 波浪频率
    private var waveSpeed = 2f // 波浪移动速度
    private var waveOffset = 0f // 波浪相位偏移

    // 音频相关
    private var currentVolume = 0f // 当前音量 (0-1)
    private var targetAmplitude = 0f // 目标振幅，默认为0
    private var isActive = false // 是否激活状态
    private var isRecording = false // 是否正在录音
    private var isPlaying = false // 是否正在播放音频
    
    // 绘制相关
    private val wavePaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val wavePath = Path()
    private val waveGradient: LinearGradient by lazy {
        LinearGradient(
            0f, 0f, width.toFloat(), 0f,
            intArrayOf(
                Color.parseColor("#4FC3F7"), // 浅蓝
                Color.parseColor("#29B6F6"), // 蓝色
                Color.parseColor("#03A9F4")  // 深蓝
            ),
            null,
            Shader.TileMode.CLAMP
        )
    }
    
    // 动画
    private var flowAnimator: ValueAnimator? = null
    private var amplitudeAnimator: ValueAnimator? = null
    
    init {
        setupPaint()
        startFlowAnimation()
    }
    
    private fun setupPaint() {
        wavePaint.apply {
            style = Paint.Style.FILL
            strokeWidth = 3f
            alpha = 180 // 半透明效果
        }
    }
    
    /**
     * 启动波浪流动动画
     */
    private fun startFlowAnimation() {
        flowAnimator?.cancel()
        flowAnimator = ValueAnimator.ofFloat(0f, 2 * PI.toFloat()).apply {
            duration = 3000 // 3秒一个周期
            repeatCount = ValueAnimator.INFINITE
            interpolator = LinearInterpolator()
            addUpdateListener { animator ->
                waveOffset = animator.animatedValue as Float
                invalidate()
            }
            start()
        }
    }
    
    /**
     * 设置音频音量 (0.0 - 1.0)
     */
    fun setVolume(volume: Float) {
        currentVolume = volume.coerceIn(0f, 1f)

        // 根据音量和状态计算目标振幅
        val newTargetAmplitude = when {
            !isActive && !isRecording && !isPlaying -> {
                // 完全静音状态 - 无波浪
                0f
            }
            isRecording && volume > 0.05f -> {
                // 录音状态且有音量 - 大幅度波浪
                30f + (volume * 80f) // 基础30 + 音量相关的0-80
            }
            isPlaying -> {
                // 播放状态 - 中等幅度波浪
                25f + (volume * 40f) // 基础25 + 音量相关的0-40
            }
            isActive -> {
                // 激活但无音量 - 小幅度波浪
                if (volume > 0.02f) 15f + (volume * 30f) else 8f
            }
            else -> {
                // 其他情况 - 静音
                0f
            }
        }

        // 平滑过渡到新振幅
        animateToAmplitude(newTargetAmplitude)
    }
    
    /**
     * 设置激活状态
     */
    fun setActive(active: Boolean) {
        isActive = active
        setVolume(currentVolume) // 重新计算振幅
    }

    /**
     * 设置录音状态
     */
    fun setRecording(recording: Boolean) {
        isRecording = recording
        setVolume(currentVolume) // 重新计算振幅
    }

    /**
     * 设置播放状态
     */
    fun setPlaying(playing: Boolean) {
        isPlaying = playing
        setVolume(currentVolume) // 重新计算振幅
    }
    
    /**
     * 平滑过渡到目标振幅
     */
    private fun animateToAmplitude(newAmplitude: Float) {
        amplitudeAnimator?.cancel()
        amplitudeAnimator = ValueAnimator.ofFloat(targetAmplitude, newAmplitude).apply {
            duration = 200 // 200ms过渡
            addUpdateListener { animator ->
                targetAmplitude = animator.animatedValue as Float
                waveAmplitude = targetAmplitude
            }
            start()
        }
    }
    
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        // 重新创建渐变
        wavePaint.shader = LinearGradient(
            0f, 0f, w.toFloat(), 0f,
            intArrayOf(
                Color.parseColor("#4FC3F7"),
                Color.parseColor("#29B6F6"),
                Color.parseColor("#03A9F4")
            ),
            null,
            Shader.TileMode.CLAMP
        )
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        if (width <= 0 || height <= 0) return

        if (waveAmplitude <= 1f) {
            // 静音状态 - 不绘制任何内容
            return
        } else {
            // 有音频状态 - 绘制波浪
            drawWave(canvas, 1.0f, 0f) // 主波浪
            drawWave(canvas, 0.6f, PI.toFloat() / 3) // 第二层波浪
            drawWave(canvas, 0.3f, PI.toFloat() * 2 / 3) // 第三层波浪
        }
    }
    


    /**
     * 绘制单层波浪
     */
    private fun drawWave(canvas: Canvas, alphaMultiplier: Float, phaseOffset: Float) {
        wavePath.reset()

        val centerY = height / 2f
        val waveLength = width / 2f // 波长

        // 设置透明度
        wavePaint.alpha = (180 * alphaMultiplier).toInt()

        // 生成波浪路径
        wavePath.moveTo(0f, centerY)

        for (x in 0..width step 5) {
            val normalizedX = x.toFloat() / waveLength
            val waveY = sin(normalizedX * 2 * PI + waveOffset + phaseOffset) * waveAmplitude
            val y = centerY + waveY.toFloat()

            if (x == 0) {
                wavePath.moveTo(x.toFloat(), y)
            } else {
                wavePath.lineTo(x.toFloat(), y)
            }
        }

        // 闭合路径到底部
        wavePath.lineTo(width.toFloat(), height.toFloat())
        wavePath.lineTo(0f, height.toFloat())
        wavePath.close()

        canvas.drawPath(wavePath, wavePaint)
    }
    
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        flowAnimator?.cancel()
        amplitudeAnimator?.cancel()
    }
    
    /**
     * 暂停动画
     */
    fun pauseAnimation() {
        flowAnimator?.pause()
    }
    
    /**
     * 恢复动画
     */
    fun resumeAnimation() {
        flowAnimator?.resume()
    }
    
    /**
     * 停止所有动画
     */
    fun stopAnimation() {
        flowAnimator?.cancel()
        amplitudeAnimator?.cancel()
    }
}
