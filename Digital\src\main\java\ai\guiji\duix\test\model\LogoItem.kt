package ai.guiji.duix.test.model

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * Logo数据模型
 * 用于解析服务器返回的logo列表数据
 */
data class LogoItem(
    @JsonProperty("id")
    val id: Int,

    @JsonProperty("name")
    val name: String,

    @JsonProperty("file_path")
    val filePath: String,

    @JsonProperty("description")
    val description: String,

    @JsonProperty("update_log")
    val updateLog: String,

    @JsonProperty("is_active")
    val isActive: Boolean,

    @JsonProperty("created_at")
    val createdAt: String,

    @JsonProperty("updated_at")
    val updatedAt: String,

    @JsonProperty("url")
    val url: String
) {
    /**
     * 用于下拉框显示的文本
     */
    override fun toString(): String {
        return name
    }
}
