# 唤醒词动态配置功能实现说明

## 🎯 功能目标

实现从后端动态获取唤醒词列表，在设置界面提供下拉选择，并在主界面显示当前选择的唤醒词提示。

## 📋 需求分析

### **后端接口**
- **接口地址**: `/api/wakeword/`
- **返回格式**: JSON数组，包含id、word、description等字段

### **前端需求**
1. **设置界面**: 添加唤醒词下拉选择器
2. **主界面**: 在开场白上方显示"您可以说 xx 开始对话？"

## 🔧 技术实现

### **1. API配置 (ApiConfig.kt)**

```kotlin
object WakeWord {
    const val GET_WAKE_WORDS_ENDPOINT = "/api/wakeword/"
}
```

### **2. 数据模型 (WakeWordItem.kt)**

```kotlin
data class WakeWordItem(
    @SerializedName("id") val id: Int,
    @SerializedName("word") val word: String,
    @SerializedName("description") val description: String,
    @SerializedName("created_at") val createdAt: String,
    @SerializedName("updated_at") val updatedAt: String
)
```

### **3. 设置界面 (SettingsActivity)**

#### **常量定义**
```kotlin
const val KEY_WAKE_WORD = "wake_word"
const val DEFAULT_WAKE_WORD = "小爱同学"
```

#### **UI布局**
```xml
<TextView
    android:id="@+id/tv_wake_word_label"
    android:text="唤醒词" />

<Spinner
    android:id="@+id/spinner_wake_word" />
```

#### **核心方法**
- `initWakeWordSelector()` - 初始化唤醒词选择器
- `fetchWakeWordsFromServer()` - 从服务器获取唤醒词列表
- `updateWakeWordSpinner()` - 更新下拉菜单内容

### **4. 主界面 (CallActivity)**

#### **UI布局**
```xml
<androidx.cardview.widget.CardView
    android:id="@+id/cv_wake_word_hint">
    
    <LinearLayout>
        <TextView android:text="💬" />
        <TextView
            android:id="@+id/tv_wake_word_hint"
            android:text="您可以说&quot;小爱同学&quot;开始对话？" />
    </LinearLayout>
    
</androidx.cardview.widget.CardView>
```

#### **核心方法**
- `getCurrentWakeWord()` - 从设置中读取当前唤醒词
- `updateWakeWordHint()` - 更新唤醒词提示文本
- `showWakeWordHint()` - 显示唤醒词提示
- `hideWakeWordHint()` - 隐藏唤醒词提示

## 📊 功能流程

### **设置流程**
```
用户进入设置界面
    ↓
自动请求 /api/wakeword/ 接口
    ↓
解析返回的唤醒词列表
    ↓
更新下拉菜单选项
    ↓
用户选择唤醒词
    ↓
保存到 SharedPreferences
```

### **显示流程**
```
主界面启动
    ↓
从 SharedPreferences 读取唤醒词
    ↓
更新提示文本："您可以说"xx"开始对话？"
    ↓
在开场白显示时同时显示唤醒词提示
    ↓
在隐藏欢迎内容时隐藏唤醒词提示
```

## 🎨 界面设计

### **设置界面**
- **位置**: 语言选择下方
- **样式**: 标准Spinner下拉菜单
- **标题**: "唤醒词"

### **主界面提示**
- **位置**: 开场白上方
- **样式**: 精美卡片容器
- **图标**: 💬 对话气泡
- **文本**: "您可以说"唤醒词"开始对话？"

## 📱 用户体验

### **设置体验**
1. **自动加载**: 进入设置界面自动获取唤醒词列表
2. **默认选择**: 显示当前已选择的唤醒词
3. **即时保存**: 选择后立即保存设置
4. **错误处理**: 网络失败时使用默认唤醒词

### **使用体验**
1. **动态显示**: 根据设置动态更新提示文本
2. **合适时机**: 只在显示开场白时显示提示
3. **自动隐藏**: 开始对话后自动隐藏提示
4. **视觉美观**: 使用精美的卡片设计

## 🔄 数据流转

### **存储机制**
- **本地存储**: SharedPreferences
- **键名**: `wake_word`
- **默认值**: `小爱同学`

### **同步机制**
- **设置→主界面**: 通过SharedPreferences传递
- **实时更新**: 主界面启动时读取最新设置

## 🛠️ 错误处理

### **网络错误**
- **超时处理**: 设置合理的超时时间
- **失败回退**: 使用默认唤醒词列表
- **用户提示**: Toast提示获取失败

### **数据错误**
- **空数据**: 使用默认唤醒词
- **解析失败**: 记录日志并使用默认值
- **格式错误**: 容错处理

## 📝 关键代码片段

### **获取唤醒词列表**
```kotlin
private fun fetchWakeWordsFromServer() {
    val wakeWordsUrl = ApiConfig.buildUrl(ttsBaseUrl, ApiConfig.WakeWord.GET_WAKE_WORDS_ENDPOINT)
    
    okHttpClient.newCall(request).enqueue(object : Callback {
        override fun onResponse(call: Call, response: Response) {
            val wakeWordItems = gson.fromJson(responseBody, Array<WakeWordItem>::class.java).toList()
            runOnUiThread { updateWakeWordSpinner(wakeWordItems) }
        }
    })
}
```

### **更新提示文本**
```kotlin
private fun updateWakeWordHint() {
    val currentWakeWord = getCurrentWakeWord()
    val hintText = "您可以说\"$currentWakeWord\"开始对话？"
    binding.tvWakeWordHint.text = hintText
}
```

## 🎯 功能特点

### **动态配置**
- ✅ 从后端动态获取唤醒词列表
- ✅ 支持添加新的唤醒词无需更新应用
- ✅ 实时同步最新的唤醒词配置

### **用户友好**
- ✅ 直观的下拉选择界面
- ✅ 清晰的使用提示
- ✅ 美观的视觉设计

### **稳定可靠**
- ✅ 完善的错误处理机制
- ✅ 默认值保障功能可用
- ✅ 本地缓存提升体验

现在用户可以在设置界面选择不同的唤醒词，主界面会动态显示相应的使用提示！🎊
