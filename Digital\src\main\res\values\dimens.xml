<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 基础间距 - 使用 8dp 网格系统 -->
    <dimen name="spacing_tiny">4dp</dimen>
    <dimen name="spacing_small">8dp</dimen>
    <dimen name="spacing_medium">12dp</dimen>
    <dimen name="spacing_large">16dp</dimen>
    <dimen name="spacing_xlarge">24dp</dimen>
    <dimen name="spacing_xxlarge">32dp</dimen>

    <!-- 图标尺寸 -->
    <dimen name="icon_size_small">16dp</dimen>
    <dimen name="icon_size_medium">24dp</dimen>
    <dimen name="icon_size_large">40dp</dimen>
    <dimen name="icon_size_xlarge">48dp</dimen>

    <!-- 文字尺寸 -->
    <dimen name="text_size_caption">12sp</dimen>
    <dimen name="text_size_body">14sp</dimen>
    <dimen name="text_size_subtitle">16sp</dimen>
    <dimen name="text_size_title">20sp</dimen>
    <dimen name="text_size_headline">24sp</dimen>
    <dimen name="text_size_button">14sp</dimen>

    <!-- 卡片相关 -->
    <dimen name="card_corner_radius">12dp</dimen>
    <dimen name="card_elevation">2dp</dimen>
    <dimen name="card_padding">16dp</dimen>
    <dimen name="card_margin">16dp</dimen>

    <!-- 按钮相关 -->
    <dimen name="button_height_small">36dp</dimen>
    <dimen name="button_height_medium">48dp</dimen>
    <dimen name="button_height_large">56dp</dimen>
    <dimen name="button_corner_radius">8dp</dimen>

    <!-- 输入框相关 -->
    <dimen name="input_height">48dp</dimen>
    <dimen name="input_corner_radius">8dp</dimen>

    <!-- 进度条 -->
    <dimen name="progress_bar_height">6dp</dimen>

    <!-- 最小触摸目标尺寸 -->
    <dimen name="min_touch_target">48dp</dimen>

    <!-- 头部区域 -->
    <dimen name="header_height">72dp</dimen>

    <!-- 录音按钮底部间距 - 响应式设计 -->
    <dimen name="record_button_bottom_margin">@dimen/spacing_xlarge</dimen>

    <!-- 打断提示条位置百分比 - 响应式设计 -->
    <dimen name="interrupt_hint_position_percent">0.40</dimen>
</resources>
