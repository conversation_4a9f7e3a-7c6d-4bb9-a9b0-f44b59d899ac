package ai.guiji.duix.test.util

import android.content.Context
import android.util.Log
import kotlinx.coroutines.*
import java.io.File
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 音频播放优化器
 * 主要功能：
 * 1. 音频预加载和缓存管理
 * 2. 减少文件IO操作
 * 3. 优化播放队列处理
 */
class AudioOptimizer(private val context: Context) {
    
    companion object {
        private const val TAG = "AudioOptimizer"
        private const val MAX_CACHE_SIZE = 50 * 1024 * 1024 // 50MB缓存限制
        private const val PRELOAD_COUNT = 3 // 预加载音频数量
    }
    
    private val audioCache = mutableMapOf<String, File>()
    private val preloadQueue = ConcurrentLinkedQueue<String>()
    private val isPreloading = AtomicBoolean(false)
    private val preloadScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    /**
     * 优化音频播放 - 使用预加载和缓存
     */
    suspend fun optimizedPlayAudio(audioUrl: String, onReady: (File) -> Unit) {
        withContext(Dispatchers.IO) {
            try {
                // 检查缓存
                val cachedFile = audioCache[audioUrl]
                if (cachedFile?.exists() == true) {
                    Log.i(TAG, "使用缓存音频: $audioUrl")
                    withContext(Dispatchers.Main) {
                        onReady(cachedFile)
                    }
                    return@withContext
                }
                
                // 下载音频
                val audioFile = downloadAudioOptimized(audioUrl)
                if (audioFile != null) {
                    // 添加到缓存
                    audioCache[audioUrl] = audioFile
                    cleanupCacheIfNeeded()
                    
                    withContext(Dispatchers.Main) {
                        onReady(audioFile)
                    }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "优化音频播放失败: ${e.message}", e)
            }
        }
    }
    
    /**
     * 预加载音频队列
     */
    fun preloadAudioQueue(audioUrls: List<String>) {
        if (isPreloading.get()) return
        
        preloadQueue.clear()
        preloadQueue.addAll(audioUrls.take(PRELOAD_COUNT))
        
        preloadScope.launch {
            isPreloading.set(true)
            try {
                while (preloadQueue.isNotEmpty()) {
                    val url = preloadQueue.poll() ?: break
                    if (!audioCache.containsKey(url)) {
                        downloadAudioOptimized(url)?.let { file ->
                            audioCache[url] = file
                        }
                    }
                }
            } finally {
                isPreloading.set(false)
            }
        }
    }
    
    /**
     * 优化的音频下载 - 减少IO操作
     */
    private suspend fun downloadAudioOptimized(audioUrl: String): File? {
        return try {
            // 使用更高效的下载方式
            val audioDir = File(context.getExternalFilesDir("duix"), "audio_cache")
            if (!audioDir.exists()) audioDir.mkdirs()
            
            val fileName = "audio_${audioUrl.hashCode()}.wav"
            val localFile = File(audioDir, fileName)
            
            if (localFile.exists()) {
                Log.i(TAG, "音频文件已存在: $fileName")
                return localFile
            }
            
            // 实际下载逻辑（这里需要集成到现有的下载代码中）
            Log.i(TAG, "下载音频文件: $audioUrl")
            // ... 下载实现
            
            localFile
        } catch (e: Exception) {
            Log.e(TAG, "下载音频失败: ${e.message}", e)
            null
        }
    }
    
    /**
     * 清理缓存
     */
    private fun cleanupCacheIfNeeded() {
        val totalSize = audioCache.values.sumOf { it.length() }
        if (totalSize > MAX_CACHE_SIZE) {
            Log.i(TAG, "缓存超限，开始清理: ${totalSize / 1024 / 1024}MB")
            
            // 删除最旧的文件
            val sortedFiles = audioCache.entries.sortedBy { it.value.lastModified() }
            var cleanedSize = 0L
            
            for ((url, file) in sortedFiles) {
                if (totalSize - cleanedSize <= MAX_CACHE_SIZE * 0.8) break
                
                if (file.delete()) {
                    cleanedSize += file.length()
                    audioCache.remove(url)
                    Log.i(TAG, "清理缓存文件: $url")
                }
            }
        }
    }
    
    /**
     * 清空所有缓存
     */
    fun clearCache() {
        audioCache.values.forEach { it.delete() }
        audioCache.clear()
        preloadQueue.clear()
        Log.i(TAG, "已清空音频缓存")
    }
    
    /**
     * 释放资源
     */
    fun release() {
        preloadScope.cancel()
        clearCache()
    }
}