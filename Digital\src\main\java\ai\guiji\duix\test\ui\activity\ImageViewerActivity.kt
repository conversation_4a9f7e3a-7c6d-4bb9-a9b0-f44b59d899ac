package ai.guiji.duix.test.ui.activity

import ai.guiji.duix.test.R
import ai.guiji.duix.test.databinding.ActivityImageViewerBinding
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.TranslateAnimation
import com.bumptech.glide.request.RequestOptions
import java.io.File

/**
 * 图片查看器Activity
 * 功能：
 * 1. 支持图片放大缩放查看
 * 2. 支持手势操作（双击缩放、拖拽等）
 * 3. 支持图片下载保存
 * 4. 提供加载状态和错误处理
 */
class ImageViewerActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "ImageViewerActivity"
        private const val EXTRA_IMAGE_URL = "extra_image_url"
        private const val EXTRA_IMAGE_TITLE = "extra_image_title"
        private const val EXTRA_IMAGE_LIST = "extra_image_list"
        private const val EXTRA_CURRENT_POSITION = "extra_current_position"

        /**
         * 启动图片查看器（单张图片）
         */
        fun start(context: Context, imageUrl: String, imageTitle: String = "图片查看") {
            val intent = Intent(context, ImageViewerActivity::class.java).apply {
                putExtra(EXTRA_IMAGE_URL, imageUrl)
                putExtra(EXTRA_IMAGE_TITLE, imageTitle)
            }
            context.startActivity(intent)
        }

        /**
         * 创建图片查看器Intent（单张图片）
         */
        fun createIntent(context: Context, imageUrl: String, imageTitle: String = "图片查看"): Intent {
            return Intent(context, ImageViewerActivity::class.java).apply {
                putExtra(EXTRA_IMAGE_URL, imageUrl)
                putExtra(EXTRA_IMAGE_TITLE, imageTitle)
            }
        }
        
        /**
         * 启动图片查看器（多张图片）
         */
        fun start(context: Context, imageUrls: ArrayList<String>, titles: ArrayList<String>, currentPosition: Int = 0) {
            val intent = Intent(context, ImageViewerActivity::class.java).apply {
                putStringArrayListExtra(EXTRA_IMAGE_LIST, imageUrls)
                putExtra(EXTRA_CURRENT_POSITION, currentPosition)
                putExtra(EXTRA_IMAGE_TITLE, titles.getOrNull(currentPosition) ?: "图片查看")
            }
            context.startActivity(intent)
        }

        /**
         * 创建图片查看器Intent（多张图片）
         */
        fun createIntent(context: Context, imageUrls: ArrayList<String>, titles: ArrayList<String>, currentPosition: Int = 0): Intent {
            return Intent(context, ImageViewerActivity::class.java).apply {
                putStringArrayListExtra(EXTRA_IMAGE_LIST, imageUrls)
                putExtra(EXTRA_CURRENT_POSITION, currentPosition)
                putExtra(EXTRA_IMAGE_TITLE, titles.getOrNull(currentPosition) ?: "图片查看")
            }
        }
    }

    private lateinit var binding: ActivityImageViewerBinding
    private var imageUrl: String = ""
    private var imageTitle: String = ""
    private var currentBitmap: Bitmap? = null
    
    // 多图片浏览相关变量
    private var imageUrls: ArrayList<String>? = null
    private var imageTitles: ArrayList<String>? = null
    private var currentPosition: Int = 0
    private var isMultipleImages: Boolean = false
    
    // 手势检测器
    private lateinit var gestureDetector: GestureDetector

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.i(TAG, "========== ImageViewerActivity onCreate ==========")

        binding = ActivityImageViewerBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 检查是否是多图片模式
        imageUrls = intent.getStringArrayListExtra(EXTRA_IMAGE_LIST)
        isMultipleImages = imageUrls != null && imageUrls?.isNotEmpty() == true
        
        if (isMultipleImages) {
            // 多图片模式
            currentPosition = intent.getIntExtra(EXTRA_CURRENT_POSITION, 0)
            imageUrl = imageUrls?.getOrNull(currentPosition) ?: ""
            imageTitle = intent.getStringExtra(EXTRA_IMAGE_TITLE) ?: "图片查看"
            Log.i(TAG, "多图片模式: 总数=${imageUrls?.size}, 当前位置=$currentPosition")
        } else {
            // 单图片模式
            imageUrl = intent.getStringExtra(EXTRA_IMAGE_URL) ?: ""
            imageTitle = intent.getStringExtra(EXTRA_IMAGE_TITLE) ?: "图片查看"
        }

        Log.i(TAG, "图片URL: $imageUrl")
        Log.i(TAG, "图片标题: $imageTitle")

        if (imageUrl.isEmpty()) {
            Log.e(TAG, "图片URL为空，无法加载")
            showError("图片地址无效")
            return
        }

        initViews()
        setupGestureDetector()
        loadImage()
        
        // 如果是多图片模式，显示左右滑动提示
        if (isMultipleImages) {
            showSwipeHint()
        }
    }

    private fun initViews() {
        // 设置标题
        binding.tvTitle.text = imageTitle

        // 返回按钮点击事件
        binding.ivBack.setOnClickListener {
            Log.i(TAG, "返回按钮被点击")
            finish()
        }

        // 隐藏下载按钮
        binding.ivDownload.visibility = View.GONE

        // 重试按钮点击事件
        binding.btnRetry.setOnClickListener {
            Log.i(TAG, "重试按钮被点击")
            loadImage()
        }

        // 设置ImageView的点击事件（单击隐藏/显示工具栏）
        binding.ivImage.setOnClickListener {
            toggleToolbarVisibility()
        }
        
        // 如果是多图片模式，显示导航指示器
        if (isMultipleImages) {
            binding.tvPageIndicator.visibility = View.VISIBLE
            updatePageIndicator()
        } else {
            binding.tvPageIndicator.visibility = View.GONE
        }

        Log.i(TAG, "✓ 视图初始化完成")
    }

    private fun loadImage() {
        Log.i(TAG, "开始加载图片: $imageUrl")

        // 显示加载状态
        binding.pbLoading.visibility = View.VISIBLE
        binding.llError.visibility = View.GONE
        binding.ivImage.visibility = View.VISIBLE // 确保ImageView可见

        // 使用Glide加载图片，修改scaleType为fitCenter以确保图片正确显示
        binding.ivImage.scaleType = android.widget.ImageView.ScaleType.FIT_CENTER
        
        try {
            Log.i(TAG, "尝试使用Glide加载图片URL: $imageUrl")
            
            // 添加请求选项，确保图片正确加载
            val requestOptions = RequestOptions()
                .error(R.drawable.ic_error)
                .fitCenter()
                // 使用缓存而不是跳过缓存，避免重复加载
                .diskCacheStrategy(DiskCacheStrategy.ALL)
            
            Glide.with(this)
                .load(imageUrl) // 直接使用load而不是asBitmap，更灵活处理各种图片格式
                .apply(requestOptions)
                .listener(object : RequestListener<android.graphics.drawable.Drawable> {
                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<android.graphics.drawable.Drawable>?,
                        isFirstResource: Boolean
                    ): Boolean {
                        Log.e(TAG, "图片加载失败: ${e?.message}")
                        // 打印详细错误信息
                        e?.logRootCauses(TAG)
                        runOnUiThread {
                            showError("图片加载失败")
                        }
                        return false
                    }

                    override fun onResourceReady(
                        resource: android.graphics.drawable.Drawable?,
                        model: Any?,
                        target: Target<android.graphics.drawable.Drawable>?,
                        dataSource: DataSource?,
                        isFirstResource: Boolean
                    ): Boolean {
                        Log.i(TAG, "图片加载成功, 数据来源: $dataSource")
                        runOnUiThread {
                            showImage()
                        }
                        return false
                    }
                })
                .into(binding.ivImage)
        } catch (e: Exception) {
            Log.e(TAG, "Glide加载图片时发生异常: ${e.message}", e)
            showError("加载图片时发生错误")
        }
    }

    private fun showImage() {
        binding.pbLoading.visibility = View.GONE
        binding.llError.visibility = View.GONE
        binding.ivImage.visibility = View.VISIBLE
        Log.i(TAG, "✓ 图片显示成功")
        
        // 移除循环加载逻辑，避免闪烁和重复加载
        // 如果图片加载成功，就直接显示
    }

    private fun showError(message: String) {
        binding.pbLoading.visibility = View.GONE
        binding.ivImage.visibility = View.GONE
        binding.llError.visibility = View.VISIBLE
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
        Log.e(TAG, "显示错误: $message")
        
        // 检查URL是否有效
        if (imageUrl.isNotEmpty()) {
            Log.i(TAG, "尝试分析URL: $imageUrl")
            // 检查URL格式
            try {
                val uri = Uri.parse(imageUrl)
                Log.i(TAG, "URL解析结果 - 协议: ${uri.scheme}, 主机: ${uri.host}, 路径: ${uri.path}")
                
                // 如果是本地文件，检查文件是否存在
                if (uri.scheme == "file" || uri.scheme == null) {
                    val file = File(uri.path ?: "")
                    Log.i(TAG, "本地文件检查 - 存在: ${file.exists()}, 可读: ${file.canRead()}, 大小: ${file.length()}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "URL分析失败: ${e.message}")
            }
        }
    }

    private fun toggleToolbarVisibility() {
        val isVisible = binding.llToolbar.visibility == View.VISIBLE
        binding.llToolbar.visibility = if (isVisible) View.GONE else View.VISIBLE
        Log.i(TAG, "工具栏可见性切换: ${if (isVisible) "隐藏" else "显示"}")
    }

    // 下载功能已移除

    /**
     * 设置手势检测器
     */
    private fun setupGestureDetector() {
        gestureDetector = GestureDetector(this, object : GestureDetector.SimpleOnGestureListener() {
            override fun onFling(e1: MotionEvent?, e2: MotionEvent, velocityX: Float, velocityY: Float): Boolean {
                // 只有在多图片模式下才处理滑动
                if (!isMultipleImages) return false
                
                val diffX = e2.x - (e1?.x ?: 0f)
                val diffY = e2.y - (e1?.y ?: 0f)
                
                // 确保是水平滑动而不是垂直滑动
                if (Math.abs(diffX) > Math.abs(diffY)) {
                    if (diffX > 100) { // 右滑，显示上一张
                        showPreviousImage()
                        return true
                    } else if (diffX < -100) { // 左滑，显示下一张
                        showNextImage()
                        return true
                    }
                }
                return false
            }
        })
        
        // 设置触摸监听
        binding.ivImage.setOnTouchListener { _, event ->
            gestureDetector.onTouchEvent(event)
            false // 返回false以便点击事件仍然能被处理
        }
    }
    
    /**
     * 显示上一张图片
     */
    private fun showPreviousImage() {
        if (!isMultipleImages || imageUrls == null || imageUrls?.isEmpty() == true) return
        
        if (currentPosition > 0) {
            currentPosition--
            imageUrl = imageUrls?.get(currentPosition) ?: return
            imageTitle = intent.getStringExtra(EXTRA_IMAGE_TITLE) ?: "图片查看"
            
            // 更新标题和页码指示器
            binding.tvTitle.text = imageTitle
            updatePageIndicator()
            
            // 加载新图片，带动画效果
            loadImageWithAnimation(true)
        } else {
            // 已经是第一张，显示提示
            Toast.makeText(this, "已经是第一张图片", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 显示下一张图片
     */
    private fun showNextImage() {
        if (!isMultipleImages || imageUrls == null || imageUrls?.isEmpty() == true) return
        
        if (currentPosition < (imageUrls?.size ?: 0) - 1) {
            currentPosition++
            imageUrl = imageUrls?.get(currentPosition) ?: return
            imageTitle = intent.getStringExtra(EXTRA_IMAGE_TITLE) ?: "图片查看"
            
            // 更新标题和页码指示器
            binding.tvTitle.text = imageTitle
            updatePageIndicator()
            
            // 加载新图片，带动画效果
            loadImageWithAnimation(false)
        } else {
            // 已经是最后一张，显示提示
            Toast.makeText(this, "已经是最后一张图片", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 带动画效果加载图片
     * @param fromRight 是否从右侧滑入
     */
    private fun loadImageWithAnimation(fromRight: Boolean) {
        // 创建滑动动画
        val screenWidth = resources.displayMetrics.widthPixels.toFloat()
        val startX = if (fromRight) -screenWidth else screenWidth
        val animation = TranslateAnimation(startX, 0f, 0f, 0f).apply {
            duration = 300
            interpolator = AccelerateDecelerateInterpolator()
        }
        
        // 加载图片并应用动画
        loadImage()
        binding.ivImage.startAnimation(animation)
    }
    
    /**
     * 更新页码指示器
     */
    private fun updatePageIndicator() {
        if (isMultipleImages && imageUrls != null) {
            binding.tvPageIndicator.text = "${currentPosition + 1}/${imageUrls?.size}"
            binding.tvPageIndicator.visibility = View.VISIBLE
        } else {
            binding.tvPageIndicator.visibility = View.GONE
        }
    }
    
    /**
     * 显示左右滑动提示
     */
    private fun showSwipeHint() {
        binding.tvSwipeHint.visibility = View.VISIBLE
        binding.tvSwipeHint.alpha = 1.0f
        binding.tvSwipeHint.animate()
            .alpha(0.0f)
            .setDuration(3000)
            .withEndAction {
                binding.tvSwipeHint.visibility = View.GONE
            }
            .start()
    }
    
    override fun onTouchEvent(event: MotionEvent?): Boolean {
        // 将触摸事件传递给手势检测器
        if (event != null) {
            if (gestureDetector.onTouchEvent(event)) {
                return true
            }
        }
        return super.onTouchEvent(event)
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 清理资源
        currentBitmap?.recycle()
        currentBitmap = null
        Log.i(TAG, "ImageViewerActivity onDestroy")
    }
}
