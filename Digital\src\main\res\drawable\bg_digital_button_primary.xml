<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape>
            <corners android:radius="16dp" />
            <gradient
                android:startColor="@color/digital_primary"
                android:endColor="@color/digital_accent"
                android:angle="135" />
        </shape>
    </item>
    <item>
        <shape>
            <corners android:radius="16dp" />
            <gradient
                android:startColor="@color/digital_primary_light"
                android:endColor="@color/digital_accent"
                android:angle="135" />
            <stroke 
                android:width="1dp" 
                android:color="#40FFFFFF" />
        </shape>
    </item>
</selector>
