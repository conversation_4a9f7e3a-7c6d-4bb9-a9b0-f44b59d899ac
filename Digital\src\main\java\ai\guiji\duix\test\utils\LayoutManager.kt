package ai.guiji.duix.test.utils

import android.content.Context
import android.content.SharedPreferences
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import ai.guiji.duix.test.ui.activity.LayoutAdjustmentActivity

/**
 * 布局管理器
 * 负责根据用户设置动态调整UI元素位置
 */
class LayoutManager(private val context: Context) {

    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(LayoutAdjustmentActivity.PREFS_NAME, Context.MODE_PRIVATE)

    /**
     * 应用动态布局到指定的View
     * @param view 要调整位置的View
     * @param layoutType 布局类型
     */
    fun applyDynamicLayout(view: View, layoutType: LayoutType) {
        val layoutParams = view.layoutParams as? ConstraintLayout.LayoutParams ?: return
        val parentHeight = (view.parent as? View)?.height ?: return
        
        if (parentHeight <= 0) {
            // 如果父容器高度还没有确定，延迟执行
            view.post {
                applyDynamicLayout(view, layoutType)
            }
            return
        }

        val percentage = getLayoutPercentage(layoutType)
        val topMargin = (parentHeight * percentage).toInt()
        
        // 更新顶部边距
        layoutParams.topMargin = topMargin
        view.layoutParams = layoutParams
    }

    /**
     * 应用动态布局到指定的View（使用ConstraintLayout的百分比约束）
     * @param view 要调整位置的View
     * @param layoutType 布局类型
     */
    fun applyDynamicLayoutWithPercentage(view: View, layoutType: LayoutType) {
        val layoutParams = view.layoutParams as? ConstraintLayout.LayoutParams ?: return
        val percentage = getLayoutPercentage(layoutType)
        
        // 使用ConstraintLayout的垂直偏移百分比
        layoutParams.verticalBias = percentage
        view.layoutParams = layoutParams
    }

    /**
     * 获取指定布局类型的位置百分比
     */
    private fun getLayoutPercentage(layoutType: LayoutType): Float {
        return when (layoutType) {
            LayoutType.OPENING_STATEMENT -> sharedPreferences.getFloat(
                LayoutAdjustmentActivity.KEY_OPENING_STATEMENT,
                LayoutAdjustmentActivity.DEFAULT_OPENING_STATEMENT
            )
            LayoutType.GREETING_QUESTIONS -> sharedPreferences.getFloat(
                LayoutAdjustmentActivity.KEY_GREETING_QUESTIONS,
                LayoutAdjustmentActivity.DEFAULT_GREETING_QUESTIONS
            )
            LayoutType.GREETING_QUESTIONS_WIDTH -> sharedPreferences.getFloat(
                LayoutAdjustmentActivity.KEY_GREETING_QUESTIONS_WIDTH,
                LayoutAdjustmentActivity.DEFAULT_GREETING_QUESTIONS_WIDTH
            )
            LayoutType.USER_QUESTION -> sharedPreferences.getFloat(
                LayoutAdjustmentActivity.KEY_CONVERSATION_DIALOG,
                LayoutAdjustmentActivity.DEFAULT_CONVERSATION_DIALOG
            )
            LayoutType.AI_ANSWER -> sharedPreferences.getFloat(
                LayoutAdjustmentActivity.KEY_CONVERSATION_DIALOG,
                LayoutAdjustmentActivity.DEFAULT_CONVERSATION_DIALOG
            )
            LayoutType.CONVERSATION_DIALOG -> sharedPreferences.getFloat(
                LayoutAdjustmentActivity.KEY_CONVERSATION_DIALOG,
                LayoutAdjustmentActivity.DEFAULT_CONVERSATION_DIALOG
            )
            LayoutType.CONVERSATION_HEIGHT -> sharedPreferences.getFloat(
                LayoutAdjustmentActivity.KEY_CONVERSATION_HEIGHT,
                LayoutAdjustmentActivity.DEFAULT_CONVERSATION_HEIGHT
            )
            LayoutType.BOTTOM_SUGGESTIONS -> sharedPreferences.getFloat(
                LayoutAdjustmentActivity.KEY_BOTTOM_SUGGESTIONS,
                LayoutAdjustmentActivity.DEFAULT_BOTTOM_SUGGESTIONS
            )
            LayoutType.RECORD_BUTTON -> sharedPreferences.getFloat(
                LayoutAdjustmentActivity.KEY_RECORD_BUTTON,
                LayoutAdjustmentActivity.DEFAULT_RECORD_BUTTON
            )
            LayoutType.INTERRUPT_HINT -> sharedPreferences.getFloat(
                LayoutAdjustmentActivity.KEY_INTERRUPT_HINT,
                LayoutAdjustmentActivity.DEFAULT_INTERRUPT_HINT
            )
        }
    }

    /**
     * 获取指定布局类型的位置百分比（公开方法，供外部调用）
     */
    fun getLayoutPosition(layoutType: LayoutType): Float {
        return getLayoutPercentage(layoutType)
    }

    /**
     * 检查是否有自定义布局设置
     */
    fun hasCustomLayout(): Boolean {
        return sharedPreferences.contains(LayoutAdjustmentActivity.KEY_OPENING_STATEMENT) ||
               sharedPreferences.contains(LayoutAdjustmentActivity.KEY_GREETING_QUESTIONS) ||
               sharedPreferences.contains(LayoutAdjustmentActivity.KEY_GREETING_QUESTIONS_WIDTH) ||
               sharedPreferences.contains(LayoutAdjustmentActivity.KEY_CONVERSATION_DIALOG) ||
               sharedPreferences.contains(LayoutAdjustmentActivity.KEY_CONVERSATION_HEIGHT) ||
               sharedPreferences.contains(LayoutAdjustmentActivity.KEY_BOTTOM_SUGGESTIONS) ||
               sharedPreferences.contains(LayoutAdjustmentActivity.KEY_RECORD_BUTTON) ||
               sharedPreferences.contains(LayoutAdjustmentActivity.KEY_INTERRUPT_HINT)
    }

    /**
     * 应用所有动态布局（批量操作）
     */
    fun applyAllDynamicLayouts(views: Map<LayoutType, View>) {
        views.forEach { (layoutType, view) ->
            applyDynamicLayout(view, layoutType)
        }
    }

    /**
     * 监听布局设置变化
     */
    fun registerLayoutChangeListener(listener: OnLayoutChangeListener) {
        sharedPreferences.registerOnSharedPreferenceChangeListener { _, key ->
            val layoutType = when (key) {
                LayoutAdjustmentActivity.KEY_OPENING_STATEMENT -> LayoutType.OPENING_STATEMENT
                LayoutAdjustmentActivity.KEY_GREETING_QUESTIONS -> LayoutType.GREETING_QUESTIONS
                LayoutAdjustmentActivity.KEY_GREETING_QUESTIONS_WIDTH -> LayoutType.GREETING_QUESTIONS_WIDTH
                LayoutAdjustmentActivity.KEY_CONVERSATION_DIALOG -> LayoutType.CONVERSATION_DIALOG
                LayoutAdjustmentActivity.KEY_CONVERSATION_HEIGHT -> LayoutType.CONVERSATION_HEIGHT
                LayoutAdjustmentActivity.KEY_BOTTOM_SUGGESTIONS -> LayoutType.BOTTOM_SUGGESTIONS
                LayoutAdjustmentActivity.KEY_RECORD_BUTTON -> LayoutType.RECORD_BUTTON
                LayoutAdjustmentActivity.KEY_INTERRUPT_HINT -> LayoutType.INTERRUPT_HINT
                else -> null
            }
            
            layoutType?.let { type ->
                listener.onLayoutChanged(type, getLayoutPercentage(type))
            }
        }
    }

    /**
     * 布局类型枚举
     */
    enum class LayoutType {
        OPENING_STATEMENT,      // 开场白卡片
        GREETING_QUESTIONS,     // 开场白问题
        GREETING_QUESTIONS_WIDTH, // 推荐问题宽度（新增）
        USER_QUESTION,          // 用户问题（保留兼容性）
        AI_ANSWER,              // AI回复（保留兼容性）
        CONVERSATION_DIALOG,    // 对话框位置（合并后的新类型）
        CONVERSATION_HEIGHT,    // 对话框高度（新增）
        BOTTOM_SUGGESTIONS,     // 底部推荐问题
        RECORD_BUTTON,          // 录音按钮
        INTERRUPT_HINT          // 打断提示条
    }

    /**
     * 布局变化监听器
     */
    interface OnLayoutChangeListener {
        fun onLayoutChanged(layoutType: LayoutType, newPercentage: Float)
    }

    companion object {
        /**
         * 创建单例实例
         */
        @Volatile
        private var INSTANCE: LayoutManager? = null

        fun getInstance(context: Context): LayoutManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: LayoutManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
}
